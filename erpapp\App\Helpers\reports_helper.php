<?php
/**
 * Reports Helper Functions
 * مساعد التقارير - نظام منفصل ومرن للتقارير
 */

/**
 * عرض صفحة تقرير موحدة
 *
 * @param array $config إعدادات الصفحة والتقرير
 * @return void
 */
function render_report_page($config)
{
    // إعداد المتغيرات الأساسية
    $title = $config['title'] ?? 'تقرير البيانات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    
    // بيانات التقرير
    $report_data = $config['data'] ?? [];
    $charts = $config['charts'] ?? [];
    $stats = $config['stats'] ?? [];
    $filters = $config['filters'] ?? [];
    $actions = $config['actions'] ?? [];
    
    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض الصفحة
    render_report_layout($title, $module, $entity, $report_data, $charts, $stats, $filters, $breadcrumb, $actions);
}

/**
 * عرض تخطيط صفحة التقرير
 */
function render_report_layout($title, $module, $entity, $report_data, $charts, $stats, $filters, $breadcrumb, $actions)
{
    $has_stats = !empty($stats);
    $has_charts = !empty($charts);
    $has_data = !empty($report_data);
    $has_filters = !empty($filters);
    $has_actions = !empty($actions);
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_report_header($title, $breadcrumb); ?>

        <!-- Report Actions -->
        <?php if ($has_actions): ?>
            <?php render_report_actions($actions); ?>
        <?php endif; ?>

        <!-- Report Filters -->
        <?php if ($has_filters): ?>
            <?php render_report_filters($filters, $module, $entity); ?>
        <?php endif; ?>

        <!-- Stats Cards -->
        <?php if ($has_stats): ?>
            <?php render_report_stats($stats); ?>
        <?php endif; ?>

        <!-- Charts -->
        <?php if ($has_charts): ?>
            <?php render_report_charts($charts); ?>
        <?php endif; ?>

        <!-- Report Data -->
        <?php if ($has_data): ?>
            <?php render_report_data($report_data); ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <?php render_report_scripts($module, $entity, $charts); ?>
    <?php
}

/**
 * عرض رأس صفحة التقرير
 */
function render_report_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض إجراءات التقرير
 */
function render_report_actions($actions)
{
    ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="report-actions text-end">
                <?php foreach ($actions as $action): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-<?= $action['type'] ?? 'secondary' ?>">
                        <i class="<?= $action['icon'] ?>"></i>
                        <?= $action['text'] ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض فلاتر التقرير
 */
function render_report_filters($filters, $module, $entity)
{
    ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">فلاتر التقرير</h5>
                    <form method="POST" action="<?= base_url($module . '/' . $entity . '/apply-filters') ?>" class="row">
                        <?php foreach ($filters as $filter): ?>
                            <div class="col-md-<?= $filter['col_size'] ?? 3 ?>">
                                <div class="form-group">
                                    <label><?= $filter['label'] ?></label>
                                    <?php if ($filter['type'] === 'select'): ?>
                                        <select name="<?= $filter['name'] ?>" class="form-control">
                                            <option value=""><?= $filter['placeholder'] ?? 'اختر...' ?></option>
                                            <?php foreach ($filter['options'] as $value => $text): ?>
                                                <option value="<?= $value ?>"><?= $text ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php elseif ($filter['type'] === 'date'): ?>
                                        <input type="date" name="<?= $filter['name'] ?>" class="form-control">
                                    <?php else: ?>
                                        <input type="text" name="<?= $filter['name'] ?>" class="form-control" 
                                               placeholder="<?= $filter['placeholder'] ?? '' ?>">
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter"></i> تطبيق الفلاتر
                            </button>
                            <a href="<?= base_url($module . '/' . $entity . '/clear-filters') ?>" class="btn btn-secondary">
                                <i class="fas fa-times"></i> مسح الفلاتر
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض إحصائيات التقرير
 */
function render_report_stats($stats)
{
    ?>
    <div class="row mb-3">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= number_format($stat['value']) ?></h3>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض الرسوم البيانية
 */
function render_report_charts($charts)
{
    ?>
    <div class="row mb-3">
        <?php foreach ($charts as $chart): ?>
            <div class="col-md-<?= $chart['col_size'] ?? 6 ?>">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><?= $chart['title'] ?></h5>
                        <canvas id="<?= $chart['id'] ?>" height="<?= $chart['height'] ?? 300 ?>"></canvas>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض بيانات التقرير
 */
function render_report_data($report_data)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">بيانات التقرير</h5>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <?php if (!empty($report_data['headers'])): ?>
                                <thead>
                                    <tr>
                                        <?php foreach ($report_data['headers'] as $header): ?>
                                            <th><?= $header ?></th>
                                        <?php endforeach; ?>
                                    </tr>
                                </thead>
                            <?php endif; ?>
                            <tbody>
                                <?php if (!empty($report_data['rows'])): ?>
                                    <?php foreach ($report_data['rows'] as $row): ?>
                                        <tr>
                                            <?php foreach ($row as $cell): ?>
                                                <td><?= $cell ?></td>
                                            <?php endforeach; ?>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="<?= count($report_data['headers'] ?? [1]) ?>" class="text-center">
                                            لا توجد بيانات
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript للتقارير
 */
function render_report_scripts($module, $entity, $charts)
{
    ?>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    // Reports JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📊 Reports system loaded for <?= $module ?>/<?= $entity ?>');
        
        // إنشاء الرسوم البيانية
        <?php foreach ($charts as $chart): ?>
            <?php if (!empty($chart['data'])): ?>
                const <?= $chart['id'] ?>_ctx = document.getElementById('<?= $chart['id'] ?>').getContext('2d');
                new Chart(<?= $chart['id'] ?>_ctx, <?= json_encode($chart['data']) ?>);
            <?php endif; ?>
        <?php endforeach; ?>
    });
    </script>
    <?php
}
?>
