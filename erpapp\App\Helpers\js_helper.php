<?php
/**
 * مساعد تحميل JavaScript الذكي
 * نفس نمط css_helper.php لكن للسكريبت
 */

/**
 * تحميل ملفات JavaScript المطلوبة للصفحة الحالية
 *
 * @return array قائمة بملفات JavaScript المطلوبة
 */
function get_required_js_files() {
    $module = get_current_module();
    $page = get_current_page();

    $js_files = [];

    // ملفات JavaScript الأساسية (تحمل في كل صفحة)
    $core_files = [
        'core/app.js',              // الوظائف الأساسية للتطبيق
        'components/dropdown.js',   // مكون القوائم المنسدلة
        'components/modals.js',     // مكون النوافذ المنبثقة
        'components/forms.js',      // مكون النماذج
        'components/tables.js',     // مكون الجداول
        'components/notifications.js' // مكون الإشعارات
    ];

    $js_files = array_merge($js_files, $core_files);

    // ملف JavaScript الخاص بالوحدة
    if ($module && file_exists(BASE_PATH . "/public/js/modules/{$module}.js")) {
        $js_files[] = "modules/{$module}.js";
    }

    // ملف JavaScript الخاص بالصفحة
    if ($page && file_exists(BASE_PATH . "/public/js/pages/{$page}.js")) {
        $js_files[] = "pages/{$page}.js";
    }

    // ملفات JavaScript للمكونات الخاصة حسب الصفحة
    $special_components = get_page_specific_js_components($module, $page);
    if (!empty($special_components)) {
        $js_files = array_merge($js_files, $special_components);
    }

    return $js_files;
}

/**
 * تحديد المكونات الخاصة بالصفحة
 *
 * @param string $module
 * @param string $page
 * @return array
 */
function get_page_specific_js_components($module, $page) {
    $components = [];

    // مكونات خاصة بوحدات معينة
    $module_components = [
        'purchases' => [
            'components/datatable.js',
            'components/filters.js',
            'components/charts.js'
        ],
        'inventory' => [
            'components/datatable.js',
            'components/filters.js',
            'components/barcode.js'
        ],
        'sales' => [
            'components/datatable.js',
            'components/filters.js',
            'components/pos.js'
        ],
        'auth' => [
            'components/validation.js'
        ],
        'dashboard' => [
            'components/charts.js',
            'components/widgets.js'
        ]
    ];

    // إضافة مكونات الوحدة
    if (isset($module_components[$module])) {
        foreach ($module_components[$module] as $component) {
            if (file_exists(BASE_PATH . "/public/js/{$component}")) {
                $components[] = $component;
            }
        }
    }

    // مكونات خاصة بصفحات معينة
    $page_components = [
        'purchases-suppliers' => [
            'components/supplier-management.js'
        ],
        'inventory-products' => [
            'components/product-management.js'
        ],
        'sales-invoices' => [
            'components/invoice-management.js'
        ]
    ];

    // إضافة مكونات الصفحة
    if (isset($page_components[$page])) {
        foreach ($page_components[$page] as $component) {
            if (file_exists(BASE_PATH . "/public/js/{$component}")) {
                $components[] = $component;
            }
        }
    }

    return array_unique($components);
}

/**
 * طباعة تاجات script لملفات JavaScript
 *
 * @return void
 */
function load_js_files() {
    $js_files = get_required_js_files();
    $base_url = rtrim(APP_URL, '/');

    echo "<!-- DEBUG: Loading JavaScript files -->\n";
    echo "<!-- DEBUG: Base URL: $base_url -->\n";
    echo "<!-- DEBUG: JS files: " . implode(', ', $js_files) . " -->\n";

    foreach ($js_files as $file) {
        $file_path = BASE_PATH . "/public/js/{$file}";

        if (file_exists($file_path)) {
            $version = filemtime($file_path); // للتحكم في cache
            echo "<script src=\"{$base_url}/public/js/{$file}?v={$version}\"></script>\n";
            echo "<!-- DEBUG: Loaded JS: {$file} -->\n";
        } else {
            echo "<!-- DEBUG: JS file not found: {$file} at {$file_path} -->\n";
        }
    }
}

/**
 * تحميل JavaScript مخصص للصفحة
 *
 * @param string $js_content محتوى JavaScript
 * @return void
 */
function inline_js($js_content) {
    echo "<script>\n{$js_content}\n</script>\n";
}

/**
 * تحميل JavaScript من ملف خارجي
 *
 * @param string $file_path مسار الملف
 * @return void
 */
function load_external_js($file_path) {
    if (file_exists($file_path)) {
        $version = filemtime($file_path);
        $base_url = rtrim(APP_URL, '/');
        $relative_path = str_replace(BASE_PATH . '/public/', '', $file_path);
        echo "<script src=\"{$base_url}/public/{$relative_path}?v={$version}\"></script>\n";
    }
}

/**
 * تحميل مكتبات خارجية (CDN) - CSS و JavaScript
 *
 * @return void
 */
function load_external_libraries() {
    $libraries = get_required_external_libraries();

    foreach ($libraries as $library) {
        $type = $library['type'] ?? 'js';

        if ($type === 'css') {
            echo "<link rel=\"stylesheet\" href=\"{$library['url']}\">\n";
            echo "<!-- DEBUG: Loaded external CSS library: {$library['name']} -->\n";
        } else {
            echo "<script src=\"{$library['url']}\"></script>\n";
            echo "<!-- DEBUG: Loaded external JS library: {$library['name']} -->\n";
        }
    }
}

/**
 * تحديد المكتبات الخارجية المطلوبة
 *
 * @return array
 */
function get_required_external_libraries() {
    $module = get_current_module();
    $page = get_current_page();
    
    $libraries = [];
    
    // مكتبات أساسية
    $core_libraries = [
        [
            'name' => 'jQuery',
            'url' => 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js',
            'condition' => true // تحمل دائماً
        ],
        [
            'name' => 'Toastr CSS',
            'url' => 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/css/toastr.min.css',
            'condition' => true,
            'type' => 'css'
        ],
        [
            'name' => 'Toastr JS',
            'url' => 'https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/js/toastr.min.js',
            'condition' => true // تحمل دائماً
        ]
    ];
    
    // مكتبات خاصة بوحدات معينة
    $module_libraries = [
        'dashboard' => [
            [
                'name' => 'Chart.js',
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js'
            ]
        ],
        'inventory' => [
            [
                'name' => 'JsBarcode',
                'url' => 'https://cdnjs.cloudflare.com/ajax/libs/jsbarcode/3.11.5/JsBarcode.all.min.js'
            ]
        ],
        'purchases' => [
            [
                'name' => 'Select2 CSS',
                'url' => 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                'type' => 'css'
            ],
            [
                'name' => 'Select2 JS',
                'url' => 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js'
            ]
        ],
        'sales' => [
            [
                'name' => 'Select2 CSS',
                'url' => 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                'type' => 'css'
            ],
            [
                'name' => 'Select2 JS',
                'url' => 'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js'
            ]
        ]
    ];
    
    // إضافة المكتبات الأساسية
    foreach ($core_libraries as $library) {
        if ($library['condition']) {
            $libraries[] = $library;
        }
    }
    
    // إضافة مكتبات الوحدة
    if (isset($module_libraries[$module])) {
        $libraries = array_merge($libraries, $module_libraries[$module]);
    }
    
    return $libraries;
}

/**
 * تحميل متغيرات JavaScript من PHP
 *
 * @param array $variables
 * @return void
 */
function load_js_variables($variables = []) {
    $default_variables = [
        'APP_URL' => APP_URL,
        'BASE_PATH' => '/erpapp',
        'CURRENT_MODULE' => get_current_module(),
        'CURRENT_PAGE' => get_current_page(),
        'USER_ID' => $_SESSION['user_id'] ?? null,
        'COMPANY_ID' => $_SESSION['company_id'] ?? null,
        'LANG' => current_lang() ?? 'ar',
        'IS_RTL' => is_rtl_page(),
        'CSRF_TOKEN' => $_SESSION['csrf_token'] ?? ''
    ];
    
    $all_variables = array_merge($default_variables, $variables);
    
    echo "<script>\n";
    echo "// متغيرات التطبيق\n";
    echo "window.APP_CONFIG = " . json_encode($all_variables, JSON_UNESCAPED_UNICODE) . ";\n";
    echo "console.log('🚀 تم تحميل متغيرات التطبيق:', window.APP_CONFIG);\n";
    echo "</script>\n";
}

/**
 * تحميل جميع ملفات JavaScript للصفحة
 *
 * @param array $custom_variables متغيرات مخصصة
 * @return void
 */
function load_all_js($custom_variables = []) {
    // تحميل المكتبات الخارجية
    load_external_libraries();
    
    // تحميل متغيرات JavaScript
    load_js_variables($custom_variables);
    
    // تحميل ملفات JavaScript
    load_js_files();
    
    echo "<!-- DEBUG: All JavaScript files loaded successfully -->\n";
}
