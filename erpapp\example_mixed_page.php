<?php
/**
 * مثال على الصفحة المختلطة (بطاقات + جداول)
 * استخدام النظام الذكي الجديد
 */

require_once 'loader.php';

echo "<h1>🔄 مثال على الصفحة المختلطة</h1>";

// إعداد بيانات البطاقات (الإحصائيات)
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => 25,
        'icon' => 'fas fa-truck',
        'color' => 'primary',
        'description' => 'العدد الكلي للموردين'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => 20,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'description' => 'الموردين الذين يتم التعامل معهم'
    ],
    [
        'title' => 'إجمالي المشتريات',
        'value' => '125,000 ريال',
        'icon' => 'fas fa-shopping-cart',
        'color' => 'info',
        'description' => 'قيمة المشتريات هذا الشهر'
    ],
    [
        'title' => 'الطلبات المعلقة',
        'value' => 5,
        'icon' => 'fas fa-clock',
        'color' => 'warning',
        'description' => 'طلبات في انتظار التأكيد'
    ]
];

// إعداد أعمدة الجدول
$table_columns = [
    [
        'title' => 'اسم المورد',
        'field' => 'name',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}/view',
        'subtitle_field' => 'email'
    ],
    [
        'title' => 'رقم الهاتف',
        'field' => 'phone',
        'type' => 'text'
    ],
    [
        'title' => 'إجمالي المشتريات',
        'field' => 'total_purchases',
        'type' => 'text'
    ],
    [
        'title' => 'الحالة',
        'field' => 'status',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'pending' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'pending' => 'معلق'
            ]
        ]
    ],
    [
        'title' => 'الإجراءات',
        'field' => 'actions',
        'type' => 'actions',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/view',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-outline-warning',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'onclick' => 'confirmDelete({entity_number})',
                'class' => 'btn-outline-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف'
            ]
        ]
    ]
];

// إعداد بيانات الجدول
$table_data = [
    [
        'entity_number' => 1,
        'name' => 'شركة الأمل للتجارة',
        'email' => '<EMAIL>',
        'phone' => '0501234567',
        'total_purchases' => '45,000 ريال',
        'status' => 'active'
    ],
    [
        'entity_number' => 2,
        'name' => 'مؤسسة النور التجارية',
        'email' => '<EMAIL>',
        'phone' => '0507654321',
        'total_purchases' => '32,500 ريال',
        'status' => 'active'
    ],
    [
        'entity_number' => 3,
        'name' => 'شركة الفجر للمواد',
        'email' => '<EMAIL>',
        'phone' => '0551234567',
        'total_purchases' => '28,750 ريال',
        'status' => 'pending'
    ],
    [
        'entity_number' => 4,
        'name' => 'مؤسسة الصباح',
        'email' => '<EMAIL>',
        'phone' => '0567890123',
        'total_purchases' => '18,750 ريال',
        'status' => 'inactive'
    ]
];

// إعداد الإجراءات
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'fas fa-plus',
        'text' => 'إضافة مورد جديد'
    ],
    [
        'type' => 'success',
        'url' => 'purchases/suppliers/import',
        'icon' => 'fas fa-file-import',
        'text' => 'استيراد موردين'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/suppliers/export',
        'icon' => 'fas fa-file-export',
        'text' => 'تصدير البيانات'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث',
        'placeholder' => 'ابحث في أسماء الموردين...',
        'icon' => 'fas fa-search',
        'col_size' => 6
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'الحالة',
        'placeholder' => 'اختر الحالة',
        'icon' => 'fas fa-filter',
        'col_size' => 6,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'pending' => 'معلق'
        ]
    ]
];

// إعداد pagination
$pagination = [
    'current_page' => 1,
    'total_pages' => 3,
    'total_items' => 25,
    'per_page' => 10,
    'start_item' => 1,
    'end_item' => 10,
    'has_previous' => false,
    'has_next' => true,
    'previous_page' => 0,
    'next_page' => 2
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => 'dashboard'],
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'إدارة الموردين', 'active' => true]
];

// استخدام النظام الذكي - سيحدد تلقائياً أنه نظام مختلط
render_page([
    'title' => 'إدارة الموردين - صفحة مختلطة',
    'module' => 'purchases',
    'entity' => 'suppliers',
    
    // البطاقات (الإحصائيات)
    'stats' => $stats_cards,
    
    // الجدول
    'columns' => $table_columns,
    'data' => $table_data,
    
    // الإعدادات الإضافية
    'actions' => $actions,
    'filters_config' => $filters_config,
    'pagination' => $pagination,
    'filters' => [],
    'breadcrumb' => $breadcrumb,
    
    'empty_state' => [
        'icon' => 'mdi mdi-truck-outline',
        'message' => 'لا توجد موردين مسجلين',
        'action' => [
            'url' => 'purchases/suppliers/create',
            'text' => 'إضافة أول مورد'
        ]
    ]
]);

echo "<hr>";
echo "<h2>📊 معلومات النظام:</h2>";
echo "<p><strong>نوع الصفحة المحدد:</strong> " . detect_page_type([
    'stats' => $stats_cards,
    'columns' => $table_columns
]) . "</p>";

$systems = get_available_systems();
echo "<h3>🔍 الأنظمة المتاحة:</h3>";
echo "<ul>";
foreach ($systems as $system => $available) {
    $status = $available ? '✅ متاح' : '❌ غير متاح';
    echo "<li><strong>$system:</strong> $status</li>";
}
echo "</ul>";
?>
