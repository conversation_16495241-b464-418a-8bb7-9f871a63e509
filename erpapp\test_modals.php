<?php
/**
 * اختبار النوافذ المنبثقة الجديدة
 */

// تضمين الملفات المطلوبة
require_once 'App/Helpers/datatable_helper.php';

// بيانات تجريبية للفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'fas fa-search',
        'col_size' => 6,
        'help' => 'البحث في الاسم العربي، الإنجليزي، أو اسم الشركة'
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 3,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'مجموعة الموردين',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'fas fa-folder',
        'col_size' => 3,
        'options' => [
            '1' => 'موردين محليين',
            '2' => 'موردين دوليين',
            '3' => 'موردين مميزين'
        ]
    ]
];

$filters = [
    'per_page' => 20,
    'search' => '',
    'status' => '',
    'group_id' => ''
];

$stats = [];
$pagination = ['total_items' => 150];

$module = 'purchases';
$entity = 'suppliers';

// دوال مساعدة مؤقتة
function base_url($path = '') {
    return 'http://localhost/erpapp/' . $path;
}

function has_active_filters($filters) {
    return !empty($filters['search']) || !empty($filters['status']) || !empty($filters['group_id']);
}

if (!function_exists('htmlspecialchars_decode')) {
    function htmlspecialchars_decode($string) {
        return htmlspecialchars($string);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النوافذ المنبثقة الأنيقة</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="public/css/core/variables.css">
    <link rel="stylesheet" href="public/css/core/base.css">
    <link rel="stylesheet" href="public/css/components/buttons.css">
    <link rel="stylesheet" href="public/css/components/forms.css">
    <link rel="stylesheet" href="public/css/components/modals.css">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 200vh; /* جعل الصفحة طويلة لإظهار التمرير */
            margin: 0;
            padding: 2rem;
        }
        
        body.dark-theme {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        body.dark-theme .header {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--light-text-color);
            margin-bottom: 1rem;
        }
        
        body.dark-theme .header h1 {
            color: var(--dark-text-color);
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        body.dark-theme .test-btn {
            background: rgba(31, 41, 55, 0.9);
            color: var(--primary-color);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .test-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: var(--primary-color);
            text-decoration: none;
        }
        
        body.dark-theme .test-btn:hover {
            background: rgba(31, 41, 55, 1);
            color: var(--primary-color);
        }
        
        .theme-toggle {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
        }
        
        .theme-toggle:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
        }
        
        body.dark-theme .info-box {
            background: rgba(31, 41, 55, 0.9);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .info-box h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .info-box p {
            color: var(--light-text-color);
            margin-bottom: 0;
            line-height: 1.6;
        }
        
        body.dark-theme .info-box p {
            color: var(--dark-text-color);
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🎨 اختبار النوافذ المنبثقة الأنيقة</h1>
        <p>تصميم بسيط وأنيق مع خلفية شفافة وتأثيرات blur</p>
        
        <button onclick="toggleTheme()" class="theme-toggle">
            <i class="fas fa-moon" id="theme-icon"></i>
            <span id="theme-text">الوضع الداكن</span>
        </button>
    </div>

    <!-- Info Box -->
    <div class="info-box">
        <h3>✨ المميزات الجديدة:</h3>
        <p>• خلفية شفافة مع تأثير blur<br>
        • تصميم بسيط وأنيق<br>
        • انيميشن ناعم وهادئ<br>
        • أزرار أنيقة ومتجاوبة<br>
        • دعم الوضع الداكن والفاتح<br>
        • <strong>🎯 سحب النافذة من العنوان</strong><br>
        • <strong>📜 عدم تحريك المحتوى خلف النافذة</strong></p>
    </div>

    <!-- Test Buttons -->
    <div class="test-buttons">
        <button class="test-btn" data-toggle="modal" data-target="#filtersModal">
            <i class="fas fa-filter"></i>
            نافذة الفلاتر
        </button>
        
        <button class="test-btn" onclick="showConfirmModal()">
            <i class="fas fa-exclamation-triangle"></i>
            نافذة تأكيد
        </button>
        
        <button class="test-btn" onclick="showInfoModal()">
            <i class="fas fa-info-circle"></i>
            نافذة معلومات
        </button>
        
        <button class="test-btn" onclick="showLoadingModal()">
            <i class="fas fa-spinner"></i>
            نافذة تحميل
        </button>
    </div>

    <!-- محتوى إضافي لإظهار التمرير -->
    <div class="info-box">
        <h3>📜 اختبار التمرير:</h3>
        <p>هذا المحتوى موجود لاختبار التمرير خلف النافذة المنبثقة. يجب أن تكون قادر<|im_start|> على التمرير في الصفحة حتى لو كانت النافذة مفتوحة.</p>
    </div>

    <div class="info-box">
        <h3>🎯 التحسينات المطبقة:</h3>
        <p>• خلفية شفافة تمام<|im_start|><br>
        • السماح بالتمرير خلف النافذة<br>
        • تأثير blur متقدم<br>
        • تصميم أنيق ومتجاوب<br>
        • سحب النافذة من العنوان</p>
    </div>

    <div class="info-box">
        <h3>🔧 كيفية الاستخدام:</h3>
        <p>1. اضغط على أي زر لفتح النافذة<br>
        2. <strong>اسحب النافذة من العنوان لتحريكها</strong><br>
        3. جرب التمرير في الصفحة<br>
        4. لاحظ عدم تحريك المحتوى<br>
        5. اضغط خارج النافذة أو على زر الإغلاق</p>
    </div>

    <div class="info-box">
        <h3>✨ المميزات الجديدة:</h3>
        <p>• تصميم يشبه الصورة المرفقة<br>
        • خلفية شفافة بدون حجب المحتوى<br>
        • تجربة مستخدم محسنة<br>
        • أداء سريع ومتجاوب</p>
    </div>

    <div class="info-box">
        <h3>🎨 التصميم:</h3>
        <p>تم تصميم النوافذ لتكون هادئة وبسيطة واحترافية وأنيقة، مع الحفاظ على سهولة الاستخدام والوضوح البصري.</p>
    </div>

</div>

<!-- Filter Modal -->
<?php render_filters_modal($module, $entity, $filters_config, $filters, $stats, $pagination); ?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom Modals JS -->
<script src="public/js/components/modals.js"></script>

<script>
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');
    
    const icon = document.getElementById('theme-icon');
    const text = document.getElementById('theme-text');
    
    if (isDark) {
        icon.className = 'fas fa-sun';
        text.textContent = 'الوضع الفاتح';
    } else {
        icon.className = 'fas fa-moon';
        text.textContent = 'الوضع الداكن';
    }
    
    localStorage.setItem('darkMode', isDark);
}

// Load saved theme
if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-theme');
    document.getElementById('theme-icon').className = 'fas fa-sun';
    document.getElementById('theme-text').textContent = 'الوضع الفاتح';
}

function showConfirmModal() {
    if (typeof confirmModal === 'function') {
        confirmModal({
            title: 'تأكيد الحذف',
            message: 'هل أنت متأكد من حذف هذا العنصر؟ لا يمكن التراجع عن هذا الإجراء.',
            icon: 'warning',
            confirmText: 'حذف',
            cancelText: 'إلغاء',
            confirmClass: 'btn-danger',
            onConfirm: function() {
                alert('تم الحذف بنجاح!');
            }
        });
    }
}

function showInfoModal() {
    if (typeof createModal === 'function') {
        const modal = createModal({
            title: 'معلومات النظام',
            body: `
                <div style="text-align: center;">
                    <i class="fas fa-info-circle" style="font-size: 48px; color: var(--info-color); margin-bottom: 1rem;"></i>
                    <h4>نظام إدارة موارد المؤسسات</h4>
                    <p>الإصدار 2.0.0</p>
                    <p>تم تطوير هذا النظام باستخدام أحدث التقنيات لضمان الأداء والأمان.</p>
                </div>
            `,
            buttons: [
                {
                    text: 'إغلاق',
                    class: 'btn-primary',
                    dismiss: true
                }
            ]
        });
        
        // فتح النافذة
        const openModal = window.openModal || function(m) { 
            m.style.display = 'flex';
            m.classList.add('show');
        };
        openModal(modal);
    }
}

function showLoadingModal() {
    if (typeof loadingModal === 'function') {
        const modal = loadingModal('جاري تحميل البيانات...');
        
        // إغلاق النافذة بعد 3 ثواني
        setTimeout(() => {
            const closeModal = window.closeModal || function(m) { 
                m.style.display = 'none';
                m.classList.remove('show');
            };
            closeModal(modal);
        }, 3000);
    }
}
</script>

</body>
</html>
