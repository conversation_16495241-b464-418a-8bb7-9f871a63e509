/**
 * Custom Modal Component (بديل Bootstrap)
 * يوفر وظائف modal بدون الحاجة لـ Bootstrap
 */

// التأكد من عدم تعارض مع jQuery أو مكتبات أخرى
(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initModals);
    } else {
        initModals();
    }

    function initModals() {
        console.log('🚀 بدء تهيئة نظام النوافذ المخصص...');

        // ===== MODAL FUNCTIONALITY ===== //

        let activeModal = null;
        let modalStack = [];
        let isDragging = false;
        let dragOffset = { x: 0, y: 0 };
    
    // العثور على جميع عناصر modal triggers
    const modalTriggers = document.querySelectorAll('[data-toggle="modal"]');
    
    modalTriggers.forEach(function(trigger) {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const targetSelector = trigger.getAttribute('data-target') || trigger.getAttribute('href');
            const modal = document.querySelector(targetSelector);
            
            if (modal) {
                openModal(modal);
            }
        });
    });
    
    // إغلاق modal عند النقر على close buttons
    document.addEventListener('click', function(e) {
        // البحث عن زر الإغلاق أو أي عنصر بداخله
        const closeButton = e.target.closest('.modal-close, [data-dismiss="modal"]');
        if (closeButton) {
            e.preventDefault();
            e.stopPropagation();
            const modal = closeButton.closest('.modal');
            if (modal) {
                console.log('🔴 إغلاق النافذة عبر زر الإغلاق');
                closeModal(modal);
            }
        }
    });
    

    
    // إغلاق modal عند الضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && activeModal) {
            if (!activeModal.hasAttribute('data-keyboard-false')) {
                closeModal(activeModal);
            }
        }
    });
    
    // ===== MODAL FUNCTIONS ===== //
    
    function openModal(modal) {
        if (!modal) return;

        // إضافة modal للـ stack
        modalStack.push(modal);
        activeModal = modal;

        // إضافة class للـ body (بدون منع التمرير)
        document.body.classList.add('modal-open');

        // إنشاء backdrop منفصل
        const backdrop = document.createElement('div');
        backdrop.className = 'modal-backdrop';
        backdrop.setAttribute('data-modal-id', modal.id);
        document.body.appendChild(backdrop);

        // ✅ إعادة تعيين موقع النافذة للمركز قبل العرض
        const modalDialog = modal.querySelector('.modal-dialog');
        if (modalDialog) {
            modalDialog.style.transform = '';
        }

        // عرض modal
        modal.style.display = 'flex';

        // عرض backdrop مع تأخير
        setTimeout(() => {
            backdrop.classList.add('show');
            modal.classList.add('show');
        }, 10);

        // تركيز على modal
        modal.setAttribute('tabindex', '-1');
        modal.focus();

        // إضافة animation
        if (modalDialog) {
            modalDialog.classList.add('modal-fade-in');
        }

        // إضافة ميزة السحب (فقط إذا لم تكن موجودة)
        if (!modal.hasAttribute('data-enhanced')) {
            makeDraggable(modal);
            modal.setAttribute('data-enhanced', 'true');
        }

        // إطلاق event
        const event = new CustomEvent('modal:opened', { detail: { modal } });
        modal.dispatchEvent(event);

        // تركيز على أول عنصر قابل للتركيز
        setTimeout(() => {
            const focusableElement = modal.querySelector('input, button, select, textarea, [tabindex]:not([tabindex="-1"])');
            if (focusableElement) {
                focusableElement.focus();
            }
        }, 100);

        // إضافة مستمع للنقر على backdrop
        backdrop.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (!modal.hasAttribute('data-backdrop-static')) {
                console.log('🔴 إغلاق النافذة عبر النقر على backdrop');
                closeModal(modal);
            }
        });

        // إضافة مستمع للنقر على modal نفسه
        modal.addEventListener('click', function(e) {
            // إذا كان النقر على modal نفسه وليس على محتواه
            if (e.target === modal && !modal.hasAttribute('data-backdrop-static')) {
                console.log('🔴 إغلاق النافذة عبر النقر على المنطقة الخارجية');
                closeModal(modal);
            }
        });
    }
    
    function closeModal(modal) {
        if (!modal) return;

        // إزالة modal من الـ stack
        const index = modalStack.indexOf(modal);
        if (index > -1) {
            modalStack.splice(index, 1);
        }

        // تحديث active modal
        activeModal = modalStack.length > 0 ? modalStack[modalStack.length - 1] : null;

        // العثور على backdrop المرتبط بهذا modal
        const backdrop = document.querySelector(`[data-modal-id="${modal.id}"]`);

        // إضافة animation للإغلاق
        const dialog = modal.querySelector('.modal-dialog');
        if (dialog) {
            dialog.classList.remove('modal-fade-in');
            dialog.classList.add('modal-fade-out');
            // ✅ إعادة تعيين الموقع فور بدء الإغلاق لتجنب الحركة
            dialog.style.transform = '';
        }

        // إخفاء backdrop
        if (backdrop) {
            backdrop.classList.remove('show');
        }

        // إخفاء modal
        modal.classList.remove('show');

        // إخفاء modal بعد animation
        setTimeout(() => {
            modal.style.display = 'none';

            // إزالة backdrop من DOM
            if (backdrop && backdrop.parentNode) {
                backdrop.parentNode.removeChild(backdrop);
            }

            // إزالة animation classes وإعادة تعيين شاملة
            if (dialog) {
                dialog.classList.remove('modal-fade-out');
                dialog.classList.remove('modal-fade-in');
                dialog.classList.remove('dragging');
                // ✅ إعادة تعيين موقع النافذة للمركز نهائي<|im_start|>
                dialog.style.transform = '';
                dialog.style.transition = '';
            }

            // إزالة class من body إذا لم تكن هناك modals أخرى
            if (modalStack.length === 0) {
                document.body.classList.remove('modal-open');
            }

            // إطلاق event
            const event = new CustomEvent('modal:closed', { detail: { modal } });
            modal.dispatchEvent(event);

        }, 300);
    }
    
        // ===== MODAL UTILITIES ===== //

        // إنشاء modal ديناميكي
        window.createModal = function(options) {
        const defaults = {
            id: 'dynamic-modal-' + Date.now(),
            title: 'Modal',
            body: '',
            size: '', // sm, lg, xl, fullscreen
            backdrop: true,
            keyboard: true,
            buttons: []
        };
        
        const config = Object.assign({}, defaults, options);
        
        const modalHTML = `
            <div class="modal ${config.size ? 'modal-' + config.size : ''}" id="${config.id}" tabindex="-1" 
                 ${!config.backdrop ? 'data-backdrop-static' : ''} 
                 ${!config.keyboard ? 'data-keyboard-false' : ''}>
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${config.title}</h5>
                            <button type="button" class="modal-close" data-dismiss="modal">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            ${config.body}
                        </div>
                        ${config.buttons.length > 0 ? `
                            <div class="modal-footer">
                                ${config.buttons.map(btn => `
                                    <button type="button" class="btn ${btn.class || 'btn-secondary'}" 
                                            ${btn.dismiss ? 'data-dismiss="modal"' : ''} 
                                            ${btn.id ? 'id="' + btn.id + '"' : ''}>
                                        ${btn.text}
                                    </button>
                                `).join('')}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
        
        // إضافة modal للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        const modal = document.getElementById(config.id);
        
        // إضافة event listeners للأزرار
        config.buttons.forEach((btn, index) => {
            if (btn.onclick) {
                const button = modal.querySelectorAll('.modal-footer .btn')[index];
                button.addEventListener('click', btn.onclick);
            }
        });
        
        return modal;
    };
    
    // عرض modal تأكيد
    window.confirmModal = function(options) {
        const defaults = {
            title: 'تأكيد',
            message: 'هل أنت متأكد؟',
            icon: 'warning',
            confirmText: 'تأكيد',
            cancelText: 'إلغاء',
            confirmClass: 'btn-danger',
            onConfirm: null,
            onCancel: null
        };
        
        const config = Object.assign({}, defaults, options);
        
        const modal = createModal({
            title: config.title,
            size: 'sm',
            body: `
                <div class="text-center">
                    <div class="modal-icon ${config.icon}">
                        <i class="fas fa-${config.icon === 'warning' ? 'exclamation-triangle' : 
                                           config.icon === 'danger' ? 'times-circle' : 
                                           config.icon === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    </div>
                    <p>${config.message}</p>
                </div>
            `,
            buttons: [
                {
                    text: config.cancelText,
                    class: 'btn-secondary',
                    dismiss: true,
                    onclick: config.onCancel
                },
                {
                    text: config.confirmText,
                    class: config.confirmClass,
                    onclick: function() {
                        if (config.onConfirm) {
                            config.onConfirm();
                        }
                        closeModal(modal);
                    }
                }
            ]
        });
        
        modal.classList.add('confirmation-modal');
        openModal(modal);
        
        return modal;
    };
    
    // عرض modal تحميل
    window.loadingModal = function(message = 'جاري التحميل...') {
        const modal = createModal({
            title: '',
            body: `
                <div class="text-center">
                    <div class="loading-spinner"></div>
                    <p>${message}</p>
                </div>
            `,
            backdrop: false,
            keyboard: false
        });
        
        modal.classList.add('loading-modal');
        modal.querySelector('.modal-header').style.display = 'none';
        openModal(modal);
        
        return modal;
    };
    
    // إغلاق جميع modals
    window.closeAllModals = function() {
        modalStack.forEach(modal => closeModal(modal));
    };
    
    // ===== ACCESSIBILITY ===== //
    
    // تحسين التنقل بـ Tab داخل modal
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab' && activeModal) {
            const focusableElements = activeModal.querySelectorAll(
                'input, button, select, textarea, [tabindex]:not([tabindex="-1"])'
            );
            
            const firstElement = focusableElements[0];
            const lastElement = focusableElements[focusableElements.length - 1];
            
            if (e.shiftKey) {
                if (document.activeElement === firstElement) {
                    e.preventDefault();
                    lastElement.focus();
                }
            } else {
                if (document.activeElement === lastElement) {
                    e.preventDefault();
                    firstElement.focus();
                }
            }
        }
    });
    
    // ===== DRAGGING FUNCTIONALITY ===== //

    function makeDraggable(modal) {
        const header = modal.querySelector('.modal-header');
        const dialog = modal.querySelector('.modal-dialog');

        if (!header || !dialog) return;

        let isDragging = false;
        let startX, startY, initialX, initialY;
        let dragTimeout;

        header.addEventListener('mousedown', function(e) {
            if (e.target.closest('.modal-close')) return;

            // تأخير بسيط لتجنب التداخل مع النقر
            dragTimeout = setTimeout(() => {
                isDragging = true;
                dialog.classList.add('dragging');

                startX = e.clientX;
                startY = e.clientY;

                const computedStyle = window.getComputedStyle(dialog);
                const transform = computedStyle.transform;

                if (transform && transform !== 'none') {
                    const matrix = new DOMMatrix(transform);
                    initialX = matrix.e;
                    initialY = matrix.f;
                } else {
                    initialX = 0;
                    initialY = 0;
                }

                document.addEventListener('mousemove', onMouseMove);
                document.addEventListener('mouseup', onMouseUp);
            }, 100);

            e.preventDefault();
        });

        header.addEventListener('mouseup', function() {
            if (dragTimeout) {
                clearTimeout(dragTimeout);
                dragTimeout = null;
            }
        });

        function onMouseMove(e) {
            if (!isDragging) return;

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            const newX = initialX + deltaX;
            const newY = initialY + deltaY;

            // ✅ حفظ الموقع الجديد بدون scale لتجنب التداخل
            dialog.style.transform = `translate(${newX}px, ${newY}px)`;
        }

        function onMouseUp() {
            if (dragTimeout) {
                clearTimeout(dragTimeout);
                dragTimeout = null;
            }

            isDragging = false;
            dialog.classList.remove('dragging');
            document.removeEventListener('mousemove', onMouseMove);
            document.removeEventListener('mouseup', onMouseUp);
        }
    }



        console.log('✅ Custom Modal Component loaded successfully');
    }
})();
