<?php
/**
 * اختبار الصفحة المختلطة للموردين
 * مثال مباشر على النظام المختلط الجديد
 */

require_once 'loader.php';

echo "<h1>🔄 اختبار الصفحة المختلطة للموردين</h1>";

// إعداد الإحصائيات (البطاقات)
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => 25,
        'icon' => 'fas fa-truck',
        'color' => 'primary',
        'description' => 'العدد الكلي للموردين المسجلين في النظام'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => 20,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'description' => 'الموردين الذين يتم التعامل معهم حالياً'
    ],
    [
        'title' => 'إجمالي المشتريات',
        'value' => '485,250 ريال',
        'icon' => 'fas fa-shopping-cart',
        'color' => 'info',
        'description' => 'قيمة المشتريات من جميع الموردين هذا الشهر'
    ],
    [
        'title' => 'متوسط قيمة الطلب',
        'value' => '24,262 ريال',
        'icon' => 'fas fa-chart-line',
        'color' => 'warning',
        'description' => 'متوسط قيمة طلب الشراء الواحد'
    ],
    [
        'title' => 'الطلبات المعلقة',
        'value' => 8,
        'icon' => 'fas fa-clock',
        'color' => 'danger',
        'description' => 'طلبات شراء في انتظار التأكيد أو التسليم'
    ],
    [
        'title' => 'أفضل مورد',
        'value' => 'شركة الأمل للتجارة',
        'icon' => 'fas fa-star',
        'color' => 'success',
        'description' => 'المورد الأكثر تعاملاً هذا الشهر'
    ]
];

// إعداد أعمدة الجدول (أفضل الموردين)
$columns = [
    [
        'field' => 'entity_number',
        'title' => '#',
        'type' => 'text',
        'width' => '60px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'S_company_name',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'total_orders',
        'title' => 'عدد الطلبات',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'total_amount',
        'title' => 'إجمالي المشتريات',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'last_order_date',
        'title' => 'آخر طلب',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'date'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ],
        'sortable' => true
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '150px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض التفاصيل'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/orders/create?supplier_id={entity_number}',
                'class' => 'btn-outline-success',
                'icon' => 'fas fa-plus',
                'title' => 'طلب جديد'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-outline-warning',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ]
        ]
    ]
];

// بيانات أفضل الموردين
$top_suppliers = [
    [
        'entity_number' => 1,
        'G_name_ar' => 'شركة الأمل للتجارة',
        'S_company_name' => 'Al-Amal Trading Company',
        'total_orders' => 45,
        'total_amount' => '125,000 ريال',
        'last_order_date' => '2024-01-15',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 2,
        'G_name_ar' => 'مؤسسة النور التجارية',
        'S_company_name' => 'Al-Noor Commercial Est.',
        'total_orders' => 38,
        'total_amount' => '98,500 ريال',
        'last_order_date' => '2024-01-14',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 3,
        'G_name_ar' => 'شركة الفجر للمواد',
        'S_company_name' => 'Al-Fajr Materials Co.',
        'total_orders' => 32,
        'total_amount' => '87,250 ريال',
        'last_order_date' => '2024-01-13',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 4,
        'G_name_ar' => 'مؤسسة الصباح للتوريدات',
        'S_company_name' => 'Al-Sabah Supplies Est.',
        'total_orders' => 28,
        'total_amount' => '76,800 ريال',
        'last_order_date' => '2024-01-12',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 5,
        'G_name_ar' => 'شركة الرياض التجارية',
        'S_company_name' => 'Riyadh Trading Company',
        'total_orders' => 25,
        'total_amount' => '65,400 ريال',
        'last_order_date' => '2024-01-11',
        'G_status' => 'suspended'
    ]
];

// إعداد الإجراءات السريعة
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'fas fa-plus-circle',
        'text' => 'إضافة مورد جديد'
    ],
    [
        'type' => 'success',
        'url' => 'purchases/orders/create',
        'icon' => 'fas fa-shopping-cart',
        'text' => 'طلب شراء جديد'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/suppliers',
        'icon' => 'fas fa-list',
        'text' => 'جميع الموردين'
    ],
    [
        'type' => 'warning',
        'url' => 'purchases/suppliers/stats',
        'icon' => 'fas fa-chart-bar',
        'text' => 'الإحصائيات التفصيلية'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'fas fa-search',
        'col_size' => 6
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 6,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ]
];

// إعداد pagination
$pagination = [
    'current_page' => 1,
    'total_pages' => 1,
    'total_items' => count($top_suppliers),
    'per_page' => 10,
    'start_item' => 1,
    'end_item' => count($top_suppliers),
    'has_previous' => false,
    'has_next' => false,
    'previous_page' => 0,
    'next_page' => 0
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => 'dashboard'],
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'اختبار الصفحة المختلطة', 'active' => true]
];

// استخدام النظام الذكي - سيحدد تلقائياً أنه نظام مختلط
render_page([
    'title' => 'اختبار الصفحة المختلطة - الموردين',
    'module' => 'purchases',
    'entity' => 'suppliers-test',
    
    // الإحصائيات (البطاقات) - سيتم عرضها أولاً
    'stats' => $stats_cards,
    
    // الجدول (أفضل الموردين) - سيتم عرضه ثانياً
    'columns' => $columns,
    'data' => $top_suppliers,
    
    // الإعدادات الإضافية
    'actions' => $actions,
    'filters_config' => $filters_config,
    'pagination' => $pagination,
    'filters' => [],
    'breadcrumb' => $breadcrumb,
    
    'empty_state' => [
        'icon' => 'fas fa-truck-loading',
        'message' => 'لا توجد بيانات موردين',
        'action' => [
            'url' => 'purchases/suppliers/create',
            'text' => 'إضافة أول مورد'
        ]
    ]
]);

echo "<hr>";
echo "<div class='alert alert-success'>";
echo "<h4>🎉 تم تشغيل النظام المختلط بنجاح!</h4>";
echo "<p><strong>نوع الصفحة المحدد:</strong> " . detect_page_type([
    'stats' => $stats_cards,
    'columns' => $columns
]) . "</p>";
echo "<p><strong>المكونات:</strong></p>";
echo "<ul>";
echo "<li>✅ <strong>6 بطاقات إحصائية</strong> - تعرض أهم المؤشرات</li>";
echo "<li>✅ <strong>جدول تفاعلي</strong> - يعرض أفضل 5 موردين</li>";
echo "<li>✅ <strong>4 أزرار إجراءات</strong> - للوصول السريع</li>";
echo "<li>✅ <strong>نظام فلترة</strong> - للبحث والتصفية</li>";
echo "<li>✅ <strong>تصميم متجاوب</strong> - يعمل على جميع الأجهزة</li>";
echo "</ul>";
echo "</div>";
?>
