<?php
/**
 * اختبار الأيقونات
 */

// تضمين الملفات المطلوبة
require_once 'App/Helpers/datatable_helper.php';
require_once 'App/Helpers/js_helper.php';

// بيانات تجريبية
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'mdi mdi-plus-circle',
        'text' => 'إضافة مورد جديد'
    ]
];

$filters = [
    'per_page' => 20,
    'search' => '',
    'status' => ''
];

$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث',
        'placeholder' => 'ابحث...',
        'icon' => 'mdi mdi-magnify',
        'col_size' => 6
    ]
];

$module = 'purchases';
$entity = 'suppliers';

// دوال مساعدة مؤقتة
function base_url($path = '') {
    return 'http://localhost/erpapp/' . $path;
}

if (!function_exists('htmlspecialchars_decode')) {
    function htmlspecialchars_decode($string) {
        return htmlspecialchars($string);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأيقونات</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Material Design Icons -->
    <link rel="stylesheet" href="https://cdn.materialdesignicons.com/7.2.96/css/materialdesignicons.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="public/css/core/variables.css">
    <link rel="stylesheet" href="public/css/core/base.css">
    <link rel="stylesheet" href="public/css/components/buttons.css">
    <link rel="stylesheet" href="public/css/components/forms.css">
    <link rel="stylesheet" href="public/css/components/tables.css">
    
    <style>
        body { 
            font-family: 'Tajawal', sans-serif; 
            background: var(--light-bg-color);
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
        }
        
        body.dark-theme {
            background: var(--dark-bg-color);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: var(--light-card-bg);
            border-radius: var(--border-radius);
            border: 1px solid var(--light-card-border);
        }
        
        body.dark-theme .header {
            background: var(--dark-card-bg);
            border-color: var(--dark-card-border);
        }
        
        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--light-text-color);
            margin-bottom: 1rem;
        }
        
        body.dark-theme .header h1 {
            color: var(--dark-text-color);
        }
        
        .icons-test {
            background: var(--light-card-bg);
            border: 1px solid var(--light-card-border);
            border-radius: var(--border-radius);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        body.dark-theme .icons-test {
            background: var(--dark-card-bg);
            border-color: var(--dark-card-border);
        }
        
        .icon-row {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            padding: 1rem;
            background: var(--light-bg-secondary);
            border-radius: var(--border-radius);
        }
        
        body.dark-theme .icon-row {
            background: var(--dark-bg-secondary);
        }
        
        .icon-large {
            font-size: 2rem;
            width: 3rem;
            text-align: center;
        }
        
        .icon-code {
            font-family: 'Courier New', monospace;
            background: var(--gray-100);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.875rem;
        }
        
        body.dark-theme .icon-code {
            background: var(--gray-800);
            color: var(--gray-200);
        }
        
        .theme-toggle {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
        }
        
        .theme-toggle:hover {
            background: var(--primary-dark);
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🔍 اختبار الأيقونات</h1>
        <p>فحص Material Design Icons و Font Awesome</p>
        
        <button onclick="toggleTheme()" class="theme-toggle">
            <i class="fas fa-moon" id="theme-icon"></i>
            <span id="theme-text">الوضع الداكن</span>
        </button>
    </div>

    <!-- Icons Test -->
    <div class="icons-test">
        <h3>🎯 اختبار الأيقونات المستخدمة في النظام:</h3>
        
        <div class="icon-row">
            <i class="mdi mdi-plus-circle icon-large" style="color: var(--primary-color);"></i>
            <div>
                <strong>أيقونة الإضافة (Material Design)</strong><br>
                <span class="icon-code">mdi mdi-plus-circle</span>
            </div>
        </div>
        
        <div class="icon-row">
            <i class="fas fa-filter icon-large" style="color: var(--primary-color);"></i>
            <div>
                <strong>أيقونة الفلاتر (Font Awesome)</strong><br>
                <span class="icon-code">fas fa-filter</span>
            </div>
        </div>
        
        <div class="icon-row">
            <i class="fas fa-filter-circle-xmark icon-large" style="color: var(--danger-color);"></i>
            <div>
                <strong>أيقونة مسح الفلاتر (Font Awesome)</strong><br>
                <span class="icon-code">fas fa-filter-circle-xmark</span>
            </div>
        </div>
        
        <div class="icon-row">
            <i class="mdi mdi-eye icon-large" style="color: var(--primary-color);"></i>
            <div>
                <strong>أيقونة العرض (Material Design)</strong><br>
                <span class="icon-code">mdi mdi-eye</span>
            </div>
        </div>
        
        <div class="icon-row">
            <i class="mdi mdi-pencil icon-large" style="color: var(--success-color);"></i>
            <div>
                <strong>أيقونة التعديل (Material Design)</strong><br>
                <span class="icon-code">mdi mdi-pencil</span>
            </div>
        </div>
        
        <div class="icon-row">
            <i class="mdi mdi-delete icon-large" style="color: var(--danger-color);"></i>
            <div>
                <strong>أيقونة الحذف (Material Design)</strong><br>
                <span class="icon-code">mdi mdi-delete</span>
            </div>
        </div>
    </div>

    <!-- Toolbar Test -->
    <div class="datatable-container">
        <div class="datatable-content">
            <h3>📊 اختبار شريط الأدوات:</h3>
            <?php render_datatable_toolbar($module, $entity, $actions, $filters, $filters_config); ?>
        </div>
    </div>

</div>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');
    
    const icon = document.getElementById('theme-icon');
    const text = document.getElementById('theme-text');
    
    if (isDark) {
        icon.className = 'fas fa-sun';
        text.textContent = 'الوضع الفاتح';
    } else {
        icon.className = 'fas fa-moon';
        text.textContent = 'الوضع الداكن';
    }
    
    localStorage.setItem('darkMode', isDark);
}

// Load saved theme
if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-theme');
    document.getElementById('theme-icon').className = 'fas fa-sun';
    document.getElementById('theme-text').textContent = 'الوضع الفاتح';
}
</script>

</body>
</html>
