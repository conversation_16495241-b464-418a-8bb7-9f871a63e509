<?php
/**
 * Page Helper Functions - نسخة مبسطة وآمنة
 * المتحكم الرئيسي في جميع أنواع الصفحات
 *
 * يدير ويوجه إلى الأنظمة المنفصلة:
 * - datatable_helper.php (الجداول)
 * - cards_helper.php (البطاقات)
 * - forms_helper.php (النماذج)
 * - reports_helper.php (التقارير)
 */

// التحقق من عدم تعريف الدوال مسبقاً لتجنب التضارب
if (!function_exists('render_page')) {

/**
 * عرض صفحة ذكية - يحدد النوع تلقائياً
 * هذه هي الدالة الرئيسية التي تستدعي النظام المناسب
 *
 * @param array $config إعدادات الصفحة
 * @return void
 */
function render_page($config)
{
    try {
        // تحديد نوع الصفحة بناءً على المحتوى
        $page_type = detect_page_type($config);

        // توجيه إلى النظام المناسب
        switch ($page_type) {
            case 'datatable':
                if (function_exists('render_datatable_page')) {
                    render_datatable_page($config);
                } else {
                    throw new Exception('نظام الجداول غير متاح');
                }
                break;

            case 'cards':
                if (function_exists('render_cards_page')) {
                    render_cards_page($config);
                } else {
                    throw new Exception('نظام البطاقات غير متاح');
                }
                break;

            case 'form':
                if (function_exists('render_form_page')) {
                    render_form_page($config);
                } else {
                    throw new Exception('نظام النماذج غير متاح');
                }
                break;

            case 'report':
                if (function_exists('render_report_page')) {
                    render_report_page($config);
                } else {
                    throw new Exception('نظام التقارير غير متاح');
                }
                break;

            case 'mixed':
                render_mixed_page($config);
                break;

            default:
                render_simple_page($config);
                break;
        }
    } catch (Exception $e) {
        // عرض خطأ بسيط
        echo '<div class="alert alert-danger">خطأ: ' . $e->getMessage() . '</div>';
    }
}

/**
 * تحديد نوع الصفحة بناءً على المحتوى
 */
function detect_page_type($config)
{
    $has_table = !empty($config['columns']) && !empty($config['data']);
    $has_stats = !empty($config['stats']);
    $has_form = !empty($config['form']);
    $has_charts = !empty($config['charts']);
    $has_report_data = !empty($config['data']) && !empty($config['data']['headers']);
    
    // تحديد النوع بناءً على الأولوية
    if ($has_form) {
        return 'form';
    }
    
    if ($has_charts || $has_report_data) {
        return 'report';
    }
    
    if ($has_table && $has_stats) {
        return 'mixed';
    }
    
    if ($has_table) {
        return 'datatable';
    }
    
    if ($has_stats) {
        return 'cards';
    }
    
    return 'simple';
}

/**
 * عرض صفحة مختلطة (جداول + بطاقات)
 */
function render_mixed_page($config)
{
    try {
        // فصل البيانات
        $stats_config = [
            'title' => $config['title'] ?? 'الإحصائيات',
            'module' => $config['module'] ?? 'default',
            'entity' => $config['entity'] ?? 'items',
            'stats' => $config['stats'] ?? [],
            'breadcrumb' => $config['breadcrumb'] ?? []
        ];

        $table_config = [
            'title' => $config['title'] ?? 'البيانات',
            'module' => $config['module'] ?? 'default',
            'entity' => $config['entity'] ?? 'items',
            'columns' => $config['columns'] ?? [],
            'data' => $config['data'] ?? [],
            'pagination' => $config['pagination'] ?? [],
            'filters' => $config['filters'] ?? [],
            'filters_config' => $config['filters_config'] ?? [],
            'actions' => $config['actions'] ?? [],
            'breadcrumb' => $config['breadcrumb'] ?? [],
            'empty_state' => $config['empty_state'] ?? [
                'icon' => 'mdi mdi-database-outline',
                'message' => 'لا توجد بيانات'
            ]
        ];

        // عرض مخصص للصفحة المختلطة
        render_mixed_layout($stats_config, $table_config);
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">خطأ في الصفحة المختلطة: ' . $e->getMessage() . '</div>';
    }
}

/**
 * عرض تخطيط الصفحة المختلطة
 */
function render_mixed_layout($stats_config, $table_config)
{
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_page_header($stats_config['title'], $stats_config['breadcrumb']); ?>

        <!-- Stats Cards -->
        <?php if (!empty($stats_config['stats']) && function_exists('render_stats_cards')): ?>
            <?php render_stats_cards($stats_config['stats']); ?>
        <?php endif; ?>

        <!-- Table Section -->
        <?php if (!empty($table_config['columns'])): ?>
        <div class="row">
            <div class="col-12">
                <div class="datatable-container">
                    <div class="datatable-content">

                        <!-- Toolbar -->
                        <?php if ((!empty($table_config['actions']) || !empty($table_config['filters_config'])) && function_exists('render_datatable_toolbar')): ?>
                            <?php render_datatable_toolbar(
                                $table_config['module'],
                                $table_config['entity'],
                                $table_config['actions'],
                                $table_config['filters'],
                                $table_config['filters_config']
                            ); ?>
                        <?php endif; ?>

                        <!-- Table -->
                        <div class="table-responsive">
                            <?php if (function_exists('render_datatable_table')): ?>
                                <?php render_datatable_table(
                                    $table_config['columns'],
                                    $table_config['data'],
                                    $table_config['empty_state'],
                                    $table_config['module'],
                                    $table_config['entity']
                                ); ?>
                            <?php else: ?>
                                <p>دالة عرض الجدول غير متاحة</p>
                            <?php endif; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if (!empty($table_config['pagination']) && isset($table_config['pagination']['total_items']) && $table_config['pagination']['total_items'] > 0 && function_exists('render_datatable_pagination')): ?>
                            <?php render_datatable_pagination($table_config['pagination'], $table_config['filters']); ?>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Modals -->
    <?php if (!empty($table_config['filters_config']) && function_exists('render_filters_modal')): ?>
        <?php render_filters_modal(
            $table_config['module'],
            $table_config['entity'],
            $table_config['filters_config'],
            $table_config['filters'],
            $stats_config['stats'],
            $table_config['pagination']
        ); ?>
    <?php endif; ?>

    <?php if (!empty($table_config['columns']) && function_exists('render_delete_modal')): ?>
        <?php render_delete_modal(); ?>
    <?php endif; ?>

    <!-- JavaScript -->
    <?php if (function_exists('render_datatable_scripts')): ?>
        <?php render_datatable_scripts($table_config['module'], $table_config['entity'], $table_config['filters']); ?>
    <?php endif; ?>
    <?php
}

/**
 * عرض صفحة بسيطة (header فقط)
 */
function render_simple_page($config)
{
    $title = $config['title'] ?? 'صفحة بسيطة';
    $breadcrumb = $config['breadcrumb'] ?? [['title' => $title, 'active' => true]];
    $content = $config['content'] ?? '<p>محتوى الصفحة</p>';
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_page_header($title, $breadcrumb); ?>

        <!-- Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?= $content ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * دوال مساعدة للتحقق من توفر الأنظمة
 */

/**
 * التحقق من توفر نظام الجداول
 */
function is_datatable_available()
{
    return function_exists('render_datatable_page');
}

/**
 * التحقق من توفر نظام البطاقات
 */
function is_cards_available()
{
    return function_exists('render_cards_page');
}

/**
 * التحقق من توفر نظام النماذج
 */
function is_forms_available()
{
    return function_exists('render_form_page');
}

/**
 * التحقق من توفر نظام التقارير
 */
function is_reports_available()
{
    return function_exists('render_report_page');
}

/**
 * الحصول على معلومات الأنظمة المتاحة
 */
function get_available_systems()
{
    return [
        'datatable' => is_datatable_available(),
        'cards' => is_cards_available(),
        'forms' => is_forms_available(),
        'reports' => is_reports_available()
    ];
}

/**
 * دالة مساعدة لعرض رأس الصفحة (مشتركة)
 */
function render_page_header($title, $breadcrumb)
{
    // التحقق من وجود الدالة في ملف آخر
    if (function_exists('render_datatable_header')) {
        render_datatable_header($title, $breadcrumb);
        return;
    }

    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

} // إغلاق if (!function_exists('render_page'))
?>
