<?php
/**
 * Page Helper Functions
 * المتحكم الرئيسي في جميع أنواع الصفحات
 * 
 * يدير ويوجه إلى الأنظمة المنفصلة:
 * - datatable_helper.php (الجداول)
 * - cards_helper.php (البطاقات)
 * - forms_helper.php (النماذج)
 * - reports_helper.php (التقارير)
 */

/**
 * عرض صفحة ذكية - يحدد النوع تلقائياً
 * هذه هي الدالة الرئيسية التي تستدعي النظام المناسب
 *
 * @param array $config إعدادات الصفحة
 * @return void
 */
function render_page($config)
{
    // تحديد نوع الصفحة بناءً على المحتوى
    $page_type = detect_page_type($config);
    
    // توجيه إلى النظام المناسب
    switch ($page_type) {
        case 'datatable':
            render_datatable_page($config);
            break;
            
        case 'cards':
            render_cards_page($config);
            break;
            
        case 'form':
            render_form_page($config);
            break;
            
        case 'report':
            render_report_page($config);
            break;
            
        case 'mixed':
            render_mixed_page($config);
            break;
            
        default:
            render_simple_page($config);
            break;
    }
}

/**
 * تحديد نوع الصفحة بناءً على المحتوى
 */
function detect_page_type($config)
{
    $has_table = !empty($config['columns']) && !empty($config['data']);
    $has_stats = !empty($config['stats']);
    $has_form = !empty($config['form']);
    $has_charts = !empty($config['charts']);
    $has_report_data = !empty($config['data']) && !empty($config['data']['headers']);
    
    // تحديد النوع بناءً على الأولوية
    if ($has_form) {
        return 'form';
    }
    
    if ($has_charts || $has_report_data) {
        return 'report';
    }
    
    if ($has_table && $has_stats) {
        return 'mixed';
    }
    
    if ($has_table) {
        return 'datatable';
    }
    
    if ($has_stats) {
        return 'cards';
    }
    
    return 'simple';
}

/**
 * عرض صفحة مختلطة (جداول + بطاقات)
 */
function render_mixed_page($config)
{
    // فصل البيانات
    $stats_config = [
        'title' => $config['title'] ?? 'الإحصائيات',
        'module' => $config['module'] ?? 'default',
        'entity' => $config['entity'] ?? 'items',
        'stats' => $config['stats'] ?? [],
        'breadcrumb' => $config['breadcrumb'] ?? []
    ];
    
    $table_config = [
        'title' => $config['title'] ?? 'البيانات',
        'module' => $config['module'] ?? 'default',
        'entity' => $config['entity'] ?? 'items',
        'columns' => $config['columns'] ?? [],
        'data' => $config['data'] ?? [],
        'pagination' => $config['pagination'] ?? [],
        'filters' => $config['filters'] ?? [],
        'filters_config' => $config['filters_config'] ?? [],
        'actions' => $config['actions'] ?? [],
        'breadcrumb' => $config['breadcrumb'] ?? [],
        'empty_state' => $config['empty_state'] ?? []
    ];
    
    // عرض مخصص للصفحة المختلطة
    render_mixed_layout($stats_config, $table_config);
}

/**
 * عرض تخطيط الصفحة المختلطة
 */
function render_mixed_layout($stats_config, $table_config)
{
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_page_header($stats_config['title'], $stats_config['breadcrumb']); ?>

        <!-- Stats Cards -->
        <?php if (!empty($stats_config['stats'])): ?>
            <?php render_stats_cards($stats_config['stats']); ?>
        <?php endif; ?>

        <!-- Table Section -->
        <div class="row">
            <div class="col-12">
                <div class="datatable-container">
                    <div class="datatable-content">
                        
                        <!-- Toolbar -->
                        <?php if (!empty($table_config['actions']) || !empty($table_config['filters_config'])): ?>
                            <?php render_datatable_toolbar(
                                $table_config['module'], 
                                $table_config['entity'], 
                                $table_config['actions'], 
                                $table_config['filters'], 
                                $table_config['filters_config']
                            ); ?>
                        <?php endif; ?>

                        <!-- Table -->
                        <?php if (!empty($table_config['columns'])): ?>
                        <div class="table-responsive">
                            <?php render_datatable_table(
                                $table_config['columns'], 
                                $table_config['data'], 
                                $table_config['empty_state'], 
                                $table_config['module'], 
                                $table_config['entity']
                            ); ?>
                        </div>
                        <?php endif; ?>

                        <!-- Pagination -->
                        <?php if (!empty($table_config['pagination']) && $table_config['pagination']['total_items'] > 0): ?>
                            <?php render_datatable_pagination($table_config['pagination'], $table_config['filters']); ?>
                        <?php endif; ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <?php if (!empty($table_config['filters_config'])): ?>
        <?php render_filters_modal(
            $table_config['module'], 
            $table_config['entity'], 
            $table_config['filters_config'], 
            $table_config['filters'], 
            $stats_config['stats'], 
            $table_config['pagination']
        ); ?>
    <?php endif; ?>
    
    <?php if (!empty($table_config['columns'])): ?>
        <?php render_delete_modal(); ?>
    <?php endif; ?>

    <!-- JavaScript -->
    <?php render_datatable_scripts($table_config['module'], $table_config['entity'], $table_config['filters']); ?>
    <?php
}

/**
 * عرض صفحة بسيطة (header فقط)
 */
function render_simple_page($config)
{
    $title = $config['title'] ?? 'صفحة بسيطة';
    $breadcrumb = $config['breadcrumb'] ?? [['title' => $title, 'active' => true]];
    $content = $config['content'] ?? '<p>محتوى الصفحة</p>';
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_page_header($title, $breadcrumb); ?>

        <!-- Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?= $content ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * دوال مساعدة للتحقق من توفر الأنظمة
 */

/**
 * التحقق من توفر نظام الجداول
 */
function is_datatable_available()
{
    return function_exists('render_datatable_page');
}

/**
 * التحقق من توفر نظام البطاقات
 */
function is_cards_available()
{
    return function_exists('render_cards_page');
}

/**
 * التحقق من توفر نظام النماذج
 */
function is_forms_available()
{
    return function_exists('render_form_page');
}

/**
 * التحقق من توفر نظام التقارير
 */
function is_reports_available()
{
    return function_exists('render_report_page');
}

/**
 * الحصول على معلومات الأنظمة المتاحة
 */
function get_available_systems()
{
    return [
        'datatable' => is_datatable_available(),
        'cards' => is_cards_available(),
        'forms' => is_forms_available(),
        'reports' => is_reports_available()
    ];
}

/**
 * دالة مساعدة لعرض رأس الصفحة (مشتركة)
 */
function render_page_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}
?>
