<?php
/**
 * اختبار بسيط للاستيراد
 */

// تحميل النظام
require_once __DIR__ . '/loader.php';

echo "<h1>🧪 اختبار بسيط للاستيراد</h1>";

// محاكاة رفع ملف
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<h2>📁 معالجة الملف المرفوع</h2>";
    
    $file = $_FILES['test_file'];
    
    echo "<p><strong>اسم الملف:</strong> " . htmlspecialchars($file['name']) . "</p>";
    echo "<p><strong>نوع الملف:</strong> " . htmlspecialchars($file['type']) . "</p>";
    echo "<p><strong>حجم الملف:</strong> " . number_format($file['size']) . " بايت</p>";
    echo "<p><strong>كود الخطأ:</strong> " . $file['error'] . "</p>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        // نقل الملف إلى مجلد مؤقت
        $tempPath = BASE_PATH . '/storage/uploads/temp/';
        if (!is_dir($tempPath)) {
            mkdir($tempPath, 0755, true);
        }
        
        $tempFile = $tempPath . uniqid() . '_' . $file['name'];
        
        if (move_uploaded_file($file['tmp_name'], $tempFile)) {
            echo "<p>✅ تم رفع الملف بنجاح إلى: " . $tempFile . "</p>";
            
            // اختبار قراءة الملف
            echo "<h3>📖 اختبار قراءة الملف</h3>";
            
            if (function_exists('read_import_file')) {
                $result = read_import_file($tempFile);
                
                if ($result['success']) {
                    echo "<p>✅ تم قراءة الملف بنجاح!</p>";
                    echo "<p><strong>عدد الرؤوس:</strong> " . count($result['headers']) . "</p>";
                    echo "<p><strong>عدد الصفوف:</strong> " . $result['total_rows'] . "</p>";
                    
                    echo "<h4>رؤوس الأعمدة:</h4>";
                    echo "<ul>";
                    foreach ($result['headers'] as $header) {
                        echo "<li>" . htmlspecialchars($header) . "</li>";
                    }
                    echo "</ul>";
                    
                    if (!empty($result['data'])) {
                        echo "<h4>أول 3 صفوف:</h4>";
                        echo "<table border='1' style='border-collapse: collapse;'>";
                        echo "<tr>";
                        foreach ($result['headers'] as $header) {
                            echo "<th style='padding: 5px; background: #f0f0f0;'>" . htmlspecialchars($header) . "</th>";
                        }
                        echo "</tr>";
                        
                        $preview_data = array_slice($result['data'], 0, 3);
                        foreach ($preview_data as $row) {
                            echo "<tr>";
                            foreach ($result['headers'] as $header) {
                                echo "<td style='padding: 5px; border: 1px solid #ddd;'>" . htmlspecialchars($row[$header] ?? '') . "</td>";
                            }
                            echo "</tr>";
                        }
                        echo "</table>";
                    }
                    
                } else {
                    echo "<p>❌ فشل في قراءة الملف:</p>";
                    echo "<div style='background: #ffebee; padding: 10px; border-radius: 5px; color: #c62828;'>";
                    echo htmlspecialchars($result['error']);
                    echo "</div>";
                }
            } else {
                echo "<p>❌ دالة read_import_file غير متاحة</p>";
            }
            
            // حذف الملف المؤقت
            if (file_exists($tempFile)) {
                unlink($tempFile);
                echo "<p>🗑️ تم حذف الملف المؤقت</p>";
            }
            
        } else {
            echo "<p>❌ فشل في رفع الملف</p>";
        }
    } else {
        echo "<p>❌ خطأ في رفع الملف: " . $file['error'] . "</p>";
    }
}

?>

<h2>📤 رفع ملف للاختبار</h2>
<form method="POST" enctype="multipart/form-data" style="background: #f5f5f5; padding: 20px; border-radius: 8px;">
    <p>
        <label for="test_file"><strong>اختر ملف Excel أو CSV:</strong></label><br>
        <input type="file" id="test_file" name="test_file" accept=".xlsx,.xls,.csv" required>
    </p>
    <p>
        <button type="submit" style="background: #2196F3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            رفع واختبار الملف
        </button>
    </p>
</form>

<h2>📋 معلومات النظام</h2>
<div style="background: #f9f9f9; padding: 15px; border-radius: 8px;">
    <p><strong>مسار BASE_PATH:</strong> <?= BASE_PATH ?></p>
    <p><strong>مجلد vendor:</strong> <?= file_exists(BASE_PATH . '/vendor/autoload.php') ? '✅ موجود' : '❌ غير موجود' ?></p>
    <p><strong>مجلد التخزين المؤقت:</strong> <?= is_dir(BASE_PATH . '/storage/uploads/temp') ? '✅ موجود' : '❌ غير موجود' ?></p>
    <p><strong>PhpSpreadsheet:</strong> <?= class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet') ? '✅ محمل' : '❌ غير محمل' ?></p>
    <p><strong>دالة read_import_file:</strong> <?= function_exists('read_import_file') ? '✅ متاحة' : '❌ غير متاحة' ?></p>
</div>

<h2>💡 نصائح</h2>
<ul>
    <li>تأكد من أن الملف من نوع Excel (.xlsx, .xls) أو CSV (.csv)</li>
    <li>تأكد من أن الملف يحتوي على رؤوس أعمدة في الصف الأول</li>
    <li>تأكد من أن الملف ليس فارغاً</li>
    <li>تأكد من أن حجم الملف أقل من 5 ميجابايت</li>
</ul>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3, h4 { color: #333; }
table { width: 100%; margin: 10px 0; }
th, td { text-align: right; }
.success { color: #4CAF50; }
.error { color: #f44336; }
</style>
