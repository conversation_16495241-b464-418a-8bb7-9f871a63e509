<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار فلاتر التاريخ في صفحة الموردين</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <!-- Choices.js CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css">
    
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background: #f8fafc;
            padding: 2rem;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        .header h1 {
            color: #1e293b;
            margin-bottom: 0.5rem;
        }
        
        .filter-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .filter-section h2 {
            color: #374151;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-label {
            font-weight: 600;
            color: #4b5563;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border: none;
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            border: none;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .results-section {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .form-text {
            font-size: 0.875rem;
            color: #6b7280;
            margin-top: 0.25rem;
        }
        
        .alert {
            border-radius: 8px;
            border: none;
            padding: 1rem;
            margin-bottom: 1rem;
        }
        
        .alert-info {
            background: #dbeafe;
            color: #1e40af;
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🗓️ اختبار فلاتر التاريخ في صفحة الموردين</h1>
        <p>اختبار تكامل مكتبة Flatpickr مع نظام الفلاتر</p>
    </div>

    <!-- Filter Section -->
    <div class="filter-section">
        <h2><i class="fas fa-filter"></i> فلاتر البحث المتقدم</h2>
        
        <form id="filtersForm" method="POST" action="#">
            <div class="row g-3">
                
                <!-- البحث العام -->
                <div class="col-md-12">
                    <label for="search" class="form-label">
                        <i class="fas fa-search"></i>
                        البحث في الموردين
                    </label>
                    <input type="text" class="form-control" id="search" name="search"
                           placeholder="ابحث بالاسم أو اسم الشركة...">
                    <div class="form-text">البحث في الاسم العربي، الإنجليزي، أو اسم الشركة</div>
                </div>
                
                <!-- حالة المورد -->
                <div class="col-md-3">
                    <label for="status" class="form-label">
                        <i class="fas fa-check-circle"></i>
                        حالة المورد
                    </label>
                    <select class="form-select" id="status" name="status"
                            data-select="true"
                            data-select-library="choices">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                        <option value="suspended">معلق</option>
                    </select>
                </div>
                
                <!-- مجموعة الموردين -->
                <div class="col-md-3">
                    <label for="group_id" class="form-label">
                        <i class="fas fa-folder"></i>
                        مجموعة الموردين
                    </label>
                    <select class="form-select" id="group_id" name="group_id"
                            data-select="true"
                            data-select-library="choices">
                        <option value="">جميع المجموعات</option>
                        <option value="1">موردين محليين</option>
                        <option value="2">موردين دوليين</option>
                        <option value="3">موردين مواد خام</option>
                    </select>
                </div>
                
                <!-- تاريخ الإنشاء من -->
                <div class="col-md-3">
                    <label for="created_from" class="form-label">
                        <i class="fas fa-calendar-alt"></i>
                        تاريخ الإنشاء من
                    </label>
                    <input type="text" class="form-control" id="created_from" name="created_from"
                           placeholder="اختر تاريخ البداية"
                           data-date="true"
                           data-date-type="date">
                    <div class="form-text">فلترة الموردين من تاريخ معين</div>
                </div>
                
                <!-- تاريخ الإنشاء إلى -->
                <div class="col-md-3">
                    <label for="created_to" class="form-label">
                        <i class="fas fa-calendar-check"></i>
                        تاريخ الإنشاء إلى
                    </label>
                    <input type="text" class="form-control" id="created_to" name="created_to"
                           placeholder="اختر تاريخ النهاية"
                           data-date="true"
                           data-date-type="date">
                    <div class="form-text">فلترة الموردين حتى تاريخ معين</div>
                </div>
                
                <!-- نطاق التاريخ -->
                <div class="col-md-6">
                    <label for="date_range" class="form-label">
                        <i class="fas fa-calendar-week"></i>
                        نطاق التاريخ
                    </label>
                    <input type="text" class="form-control" id="date_range" name="date_range"
                           placeholder="اختر نطاق التواريخ"
                           data-date="true"
                           data-date-type="range">
                    <div class="form-text">اختيار نطاق من التواريخ</div>
                </div>
                
                <!-- آخر نشاط -->
                <div class="col-md-3">
                    <label for="last_activity" class="form-label">
                        <i class="fas fa-clock"></i>
                        آخر نشاط
                    </label>
                    <input type="text" class="form-control" id="last_activity" name="last_activity"
                           placeholder="اختر تاريخ آخر نشاط"
                           data-date="true"
                           data-date-type="datetime">
                    <div class="form-text">فلترة حسب آخر نشاط للمورد</div>
                </div>
                
                <!-- انتهاء العقد -->
                <div class="col-md-3">
                    <label for="contract_expiry" class="form-label">
                        <i class="fas fa-file-contract"></i>
                        انتهاء العقد
                    </label>
                    <input type="text" class="form-control" id="contract_expiry" name="contract_expiry"
                           placeholder="اختر تاريخ انتهاء العقد"
                           data-date="true"
                           data-date-type="date"
                           data-min-date="<?= date('Y-m-d') ?>">
                    <div class="form-text">فلترة حسب تاريخ انتهاء العقد</div>
                </div>
                
            </div>
            
            <!-- أزرار التحكم -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearAllFilters()">
                        <i class="fas fa-eraser"></i>
                        مسح جميع الفلاتر
                    </button>
                </div>
                <div class="col-md-6">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i>
                        تطبيق الفلاتر
                    </button>
                </div>
            </div>
        </form>
    </div>

    <!-- Results Section -->
    <div class="results-section">
        <h2><i class="fas fa-table"></i> نتائج البحث</h2>
        
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            هذا مثال لاختبار فلاتر التاريخ. في التطبيق الحقيقي، ستظهر هنا نتائج البحث من قاعدة البيانات.
        </div>
        
        <div id="filter-results">
            <p>قم بتطبيق الفلاتر لرؤية النتائج...</p>
        </div>
    </div>

</div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>
<script src="https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js"></script>

<!-- Components -->
<script src="public/js/components/date.js"></script>
<script src="public/js/components/select.js"></script>

<script>
// معالجة إرسال النموذج
document.getElementById('filtersForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const filters = {};
    
    for (let [key, value] of formData.entries()) {
        if (value.trim() !== '') {
            filters[key] = value;
        }
    }
    
    displayResults(filters);
});

// عرض النتائج
function displayResults(filters) {
    const resultsDiv = document.getElementById('filter-results');
    
    if (Object.keys(filters).length === 0) {
        resultsDiv.innerHTML = '<p>لم يتم تطبيق أي فلاتر.</p>';
        return;
    }
    
    let html = '<h4>الفلاتر المطبقة:</h4><ul>';
    
    for (let [key, value] of Object.entries(filters)) {
        const label = getFilterLabel(key);
        html += `<li><strong>${label}:</strong> ${value}</li>`;
    }
    
    html += '</ul>';
    html += '<p class="mt-3"><em>في التطبيق الحقيقي، ستتم معالجة هذه الفلاتر في الخادم وإرجاع النتائج من قاعدة البيانات.</em></p>';
    
    resultsDiv.innerHTML = html;
}

// الحصول على تسمية الفلتر
function getFilterLabel(key) {
    const labels = {
        'search': 'البحث',
        'status': 'الحالة',
        'group_id': 'المجموعة',
        'created_from': 'تاريخ الإنشاء من',
        'created_to': 'تاريخ الإنشاء إلى',
        'date_range': 'نطاق التاريخ',
        'last_activity': 'آخر نشاط',
        'contract_expiry': 'انتهاء العقد'
    };
    
    return labels[key] || key;
}

// مسح جميع الفلاتر
function clearAllFilters() {
    document.getElementById('filtersForm').reset();
    
    // مسح قيم التواريخ
    if (window.dateInstances) {
        Object.values(window.dateInstances).forEach(instance => {
            if (instance && instance.clear) {
                instance.clear();
            }
        });
    }
    
    // مسح قيم Select
    document.querySelectorAll('select').forEach(select => {
        select.value = '';
        // إذا كان هناك مثيل Choices.js
        if (select._choices) {
            select._choices.removeActiveItems();
        }
    });
    
    document.getElementById('filter-results').innerHTML = '<p>تم مسح جميع الفلاتر.</p>';
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل صفحة اختبار فلاتر الموردين');
    console.log('📅 تم تهيئة فلاتر التاريخ');
    console.log('🔽 تم تهيئة فلاتر Select');
});
</script>

</body>
</html>
