<?php
/**
 * DataTable Helper Functions
 * مساعد الجداول الموحدة - متوافق مع النظام الحالي
 */

/**
 * حساب معلومات pagination موحدة
 */
function calculate_pagination($current_page, $per_page, $total_items)
{
    $total_pages = ceil($total_items / $per_page);
    $offset = ($current_page - 1) * $per_page;

    return [
        'current_page' => $current_page,
        'per_page' => $per_page,
        'total_items' => $total_items,
        'total_pages' => $total_pages,
        'has_previous' => $current_page > 1,
        'has_next' => $current_page < $total_pages,
        'previous_page' => $current_page > 1 ? $current_page - 1 : null,
        'next_page' => $current_page < $total_pages ? $current_page + 1 : null,
        'start_item' => $total_items > 0 ? $offset + 1 : 0,
        'end_item' => min($offset + $per_page, $total_items)
    ];
}

/**
 * إعداد فلاتر قاعدة البيانات موحدة
 */
function prepare_database_filters($applied_filters, $per_page, $current_page, $filter_fields = [])
{
    $offset = ($current_page - 1) * $per_page;

    $filters = [
        'limit' => $per_page,
        'offset' => $offset
    ];

    // إضافة فلاتر البحث فقط إذا كانت غير فارغة
    foreach ($filter_fields as $field) {
        if (!empty($applied_filters[$field])) {
            $filters[$field] = $applied_filters[$field];
        }
    }

    return $filters;
}

/**
 * معالجة pagination من URL وتحديث الفلاتر
 */
function handle_pagination_from_url($applied_filters, $page_name)
{
    // إعدادات pagination - استخدام current_page من الفلاتر المحفوظة فقط
    // إذا كان هناك page في URL، استخدمه وحدث الفلاتر (للتوافق مع pagination links)
    $url_page = (int)($_GET['page'] ?? 0);
    if ($url_page > 0) {
        $page = max(1, $url_page);
        // تحديث current_page في الفلاتر المحفوظة
        $applied_filters['current_page'] = $page;
        save_filters_from_form($page_name, $applied_filters);
    } else {
        // استخدام current_page من الفلاتر المحفوظة
        $page = max(1, (int)($applied_filters['current_page'] ?? 1));
    }

    return [$applied_filters, $page];
}

/**
 * دالة موحدة لمعالجة index() في جميع Controllers
 */
function handle_datatable_index($config)
{
    $company_id = current_user()['current_company_id'];

    // إعداد الفلاتر الافتراضية
    $default_filters = array_merge([
        'search' => '',
        'per_page' => 2,
        'current_page' => 1
    ], $config['default_filters'] ?? []);

    // الحصول على الفلاتر مع دمج الفلاتر المحفوظة
    $applied_filters = prepare_filters($config['filter_name'], $default_filters);

    // معالجة pagination من URL
    [$applied_filters, $page] = handle_pagination_from_url($applied_filters, $config['filter_name']);

    $perPage = max(2, min(100, (int)($applied_filters['per_page'] ?? 20)));

    // إعداد فلاتر قاعدة البيانات
    $filters = prepare_database_filters($applied_filters, $perPage, $page, $config['filter_fields'] ?? []);

    // الحصول على البيانات
    $data = $config['model']->getByCompany($company_id, $filters);

    // الحصول على العدد الكلي
    $filtersForCount = $filters;
    unset($filtersForCount['limit'], $filtersForCount['offset']);
    $totalItems = $config['model']->getCountByCompany($company_id, $filtersForCount);

    // حساب معلومات pagination
    $pagination = calculate_pagination($page, $perPage, $totalItems);

    // الحصول على الإحصائيات
    $stats = $config['model']->getStats($company_id);

    // إعداد البيانات للعرض
    $viewData = [
        'title' => $config['title'],
        $config['data_key'] => $data,
        'stats' => $stats,
        'pagination' => $pagination,
        'filters' => $applied_filters,
        'breadcrumb' => $config['breadcrumb']
    ];

    // إضافة بيانات إضافية إذا وجدت
    if (isset($config['additional_data'])) {
        $viewData = array_merge($viewData, $config['additional_data']);
    }

    return $viewData;
}

/**
 * دالة موحدة لمعالجة applyFilters() في جميع Controllers
 */
function handle_apply_filters($filter_name, $filter_fields, $redirect_url)
{
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        redirect($redirect_url);
        return;
    }

    // إعداد بيانات الفلاتر الافتراضية
    $filter_data = [
        'search' => $_POST['search'] ?? '',
        'per_page' => $_POST['per_page'] ?? 20,
        'current_page' => $_POST['current_page'] ?? 1
    ];

    // إضافة الفلاتر المخصصة
    foreach ($filter_fields as $field) {
        $filter_data[$field] = $_POST[$field] ?? '';
    }

    // حفظ الفلاتر
    $success = save_filters_from_form($filter_name, $filter_data);

    if ($success) {
        flash('success', 'تم تطبيق الفلاتر بنجاح');
    } else {
        flash('error', 'فشل في حفظ الفلاتر');
    }

    // إعادة توجيه
    redirect($redirect_url);
}

/**
 * دالة موحدة لمعالجة clearFilters() في جميع Controllers
 */
function handle_clear_filters($filter_name, $redirect_url)
{
    // مسح الفلاتر المحفوظة للمستخدم
    clear_page_filters($filter_name);

    // إعادة توجيه بدون فلاتر (URL نظيف)
    redirect($redirect_url);
}

/**
 * عرض صفحة جدول بيانات موحدة
 *
 * @param array $config إعدادات الصفحة والجدول
 * @return void
 */
function render_datatable_page($config)
{
    // إعداد المتغيرات الافتراضية مع قيم آمنة
    $title = $config['title'] ?? 'صفحة البيانات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    $data = $config['data'] ?? [];
    $columns = $config['columns'] ?? [];
    $stats = $config['stats'] ?? [];
    $pagination = $config['pagination'] ?? [
        'current_page' => 1,
        'total_pages' => 1,
        'total_items' => 0,
        'start_item' => 0,
        'end_item' => 0,
        'has_previous' => false,
        'has_next' => false
    ];
    $filters = $config['filters'] ?? [
        'search' => '',
        'status' => '',
        'group_id' => '',
        'per_page' => 20,
        'current_page' => 1
    ];
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => 'الرئيسية', 'url' => 'dashboard'],
        ['title' => $title, 'active' => true]
    ];
    $actions = $config['actions'] ?? [];
    $filters_config = $config['filters_config'] ?? [];
    $empty_state = $config['empty_state'] ?? [
        'icon' => 'mdi mdi-database-outline',
        'message' => 'لا توجد بيانات'
    ];

    // عرض الصفحة
    render_datatable_layout($title, $module, $entity, $data, $columns, $stats, $pagination, $filters, $breadcrumb, $actions, $filters_config, $empty_state);
}

/**
 * عرض تخطيط الصفحة الكامل
 */
function render_datatable_layout($title, $module, $entity, $data, $columns, $stats, $pagination, $filters, $breadcrumb, $actions, $filters_config, $empty_state)
{
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_page_header($title, $breadcrumb); ?>

        <!-- Stats Cards -->
        <?php if (!empty($stats)): ?>
            <?php render_stats_cards($stats); ?>
        <?php endif; ?>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <!-- Toolbar -->
                        <?php render_datatable_toolbar($module, $entity, $actions, $filters, $filters_config); ?>

                        <!-- Table -->
                        <div class="table-responsive">
                            <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
                        </div>

                        <!-- Pagination -->
                        <?php if (!empty($pagination) && $pagination['total_items'] > 0): ?>
                            <?php render_datatable_pagination($pagination, $filters); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <?php if (!empty($filters_config)): ?>
        <?php render_filters_modal($module, $entity, $filters_config, $filters, $stats, $pagination); ?>
    <?php endif; ?>
    
    <?php render_delete_modal(); ?>

    <!-- JavaScript -->
    <?php render_datatable_scripts($module, $entity, $filters); ?>
    <?php
}

/**
 * عرض رأس الصفحة مع Breadcrumb
 */
function render_page_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض بطاقات الإحصائيات
 */
function render_stats_cards($stats)
{
    ?>
    <div class="row">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= $stat['value'] ?></h3>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض شريط الأدوات
 */
function render_datatable_toolbar($module, $entity, $actions, $filters, $filters_config)
{
    // تحويل entity name للتوافق مع filter system
    $filter_entity = str_replace('-', '_', $entity);
    $activeFilters = count_active_filters_with_saved_check($filter_entity, $filters);
    ?>
    <div class="datatable-toolbar">
        <div class="toolbar-left">
            <?php foreach ($actions as $action): ?>
                <?php if ($action['type'] === 'primary'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-primary">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>

        <div class="toolbar-right">
            <!-- عدد العناصر لكل صفحة -->
            <div class="toolbar-item">
                <label class="toolbar-label">عرض:</label>
                <form method="POST" action="<?= base_url($module . '/' . $entity . '/apply-filters') ?>" class="toolbar-form">
                    <select class="form-select form-select-sm" name="per_page" onchange="this.form.submit()">
                        <option value="10" <?= $filters['per_page'] == 10 ? 'selected' : '' ?>>10</option>
                        <option value="20" <?= $filters['per_page'] == 20 ? 'selected' : '' ?>>20</option>
                        <option value="50" <?= $filters['per_page'] == 50 ? 'selected' : '' ?>>50</option>
                        <option value="100" <?= $filters['per_page'] == 100 ? 'selected' : '' ?>>100</option>
                    </select>
                    <!-- الحفاظ على الفلاتر الحالية -->
                    <?php foreach ($filters as $key => $value): ?>
                        <?php if ($key !== 'per_page'): ?>
                            <input type="hidden" name="<?= $key ?>" value="<?= htmlspecialchars($value) ?>">
                        <?php endif; ?>
                    <?php endforeach; ?>
                </form>
            </div>

            <!-- زر الفلاتر -->
            <?php if (!empty($filters_config)): ?>
                <div class="toolbar-item">
                    <button type="button" class="btn btn-outline-primary filter-btn" data-toggle="modal" data-target="#filtersModal">
                        <i class="mdi mdi-filter-variant"></i>
                        <span>فلاتر</span>
                        <?php if ($activeFilters > 0): ?>
                            <span class="filter-badge"><?= $activeFilters ?></span>
                        <?php endif; ?>
                    </button>
                </div>
            <?php endif; ?>

            <!-- زر مسح الفلاتر -->
            <?php if ($activeFilters > 0): ?>
                <div class="toolbar-item">
                    <a href="<?= base_url($module . '/' . $entity . '/clear-filters') ?>" class="btn btn-outline-secondary">
                        <i class="mdi mdi-filter-remove"></i>
                        <span>مسح الفلاتر</span>
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
    <?php
}

/**
 * عرض جدول البيانات
 */
function render_datatable_table($columns, $data, $empty_state, $module, $entity)
{
    ?>
    <table class="table table-centered table-nowrap table-hover mb-0">
        <thead class="table-light">
            <tr>
                <?php foreach ($columns as $column): ?>
                    <th <?= isset($column['width']) ? 'style="width: ' . $column['width'] . '"' : '' ?>
                        <?= isset($column['sortable']) && $column['sortable'] ? 'data-sortable="true"' : '' ?>
                        <?= isset($column['data_type']) ? 'data-type="' . $column['data_type'] . '"' : '' ?>>
                        <?= $column['title'] ?>
                    </th>
                <?php endforeach; ?>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($data)): ?>
                <tr>
                    <td colspan="<?= count($columns) ?>" class="text-center py-4">
                        <div class="text-muted">
                            <i class="<?= $empty_state['icon'] ?? 'mdi mdi-database-outline' ?> mdi-48px"></i>
                            <p class="mt-2"><?= $empty_state['message'] ?? 'لا توجد بيانات' ?></p>
                            <?php if (isset($empty_state['action'])): ?>
                                <a href="<?= base_url($empty_state['action']['url']) ?>" class="btn btn-primary btn-sm">
                                    <?= $empty_state['action']['text'] ?>
                                </a>
                            <?php endif; ?>
                        </div>
                    </td>
                </tr>
            <?php else: ?>
                <?php foreach ($data as $row): ?>
                    <tr>
                        <?php foreach ($columns as $column): ?>
                            <td>
                                <?php render_table_cell($column, $row, $module, $entity); ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>
    <?php
}

/**
 * عرض خلية الجدول حسب نوعها
 */
function render_table_cell($column, $row, $module, $entity)
{
    $field = $column['field'];
    $value = $row[$field] ?? '';
    $type = $column['type'] ?? 'text';

    switch ($type) {
        case 'link':
            // دعم أنواع مختلفة من المعرفات
            $url = $column['url'];
            if (isset($row['entity_number'])) {
                $url = str_replace('{entity_number}', $row['entity_number'], $url);
            }
            if (isset($row['group_number'])) {
                $url = str_replace('{group_number}', $row['group_number'], $url);
            }
            echo '<a href="' . base_url($url) . '" class="text-body fw-bold">';
            echo htmlspecialchars($value);
            echo '</a>';
            if (isset($column['subtitle_field']) && !empty($row[$column['subtitle_field']])) {
                echo '<br><small class="text-muted">' . htmlspecialchars($row[$column['subtitle_field']]) . '</small>';
            }
            break;

        case 'badge':
            if (isset($column['status_config'])) {
                $config = $column['status_config'];
                $class = $config['classes'][$value] ?? 'secondary';
                $text = $config['texts'][$value] ?? $value;
                echo '<span class="badge bg-' . $class . '">' . htmlspecialchars($text) . '</span>';
            } else {
                echo '<span class="badge bg-info">' . htmlspecialchars($value) . '</span>';
            }
            break;

        case 'actions':
            echo '<div class="btn-group" role="group">';
            foreach ($column['buttons'] as $button) {
                if (isset($button['url'])) {
                    // دعم أنواع مختلفة من المعرفات
                    $url = $button['url'];
                    if (isset($row['entity_number'])) {
                        $url = str_replace('{entity_number}', $row['entity_number'], $url);
                    }
                    if (isset($row['group_number'])) {
                        $url = str_replace('{group_number}', $row['group_number'], $url);
                    }
                    if ($button['type'] === 'link') {
                        echo '<a href="' . base_url($url) . '" class="btn ' . $button['class'] . ' btn-sm" title="' . $button['title'] . '">';
                        echo '<i class="' . $button['icon'] . '"></i>';
                        echo '</a>';
                    }
                }
                if ($button['type'] === 'button' && isset($button['onclick'])) {
                    // دعم أنواع مختلفة من المعرفات
                    $onclick = $button['onclick'];
                    if (isset($row['entity_number'])) {
                        $onclick = str_replace('{entity_number}', $row['entity_number'], $onclick);
                    }
                    if (isset($row['group_number'])) {
                        $onclick = str_replace('{group_number}', $row['group_number'], $onclick);
                    }
                    echo '<button type="button" class="btn ' . $button['class'] . ' btn-sm" onclick="' . $onclick . '" title="' . $button['title'] . '">';
                    echo '<i class="' . $button['icon'] . '"></i>';
                    echo '</button>';
                }
            }
            echo '</div>';
            break;

        default: // text
            echo htmlspecialchars($value ?: '-');
            break;
    }
}

/**
 * عرض pagination
 */
function render_datatable_pagination($pagination, $filters)
{
    ?>
    <div class="datatable-pagination">
        <div class="pagination-info">
            <div class="info-text">
                <span class="info-label">عرض</span>
                <span class="info-numbers"><?= $pagination['start_item'] ?> - <?= $pagination['end_item'] ?></span>
                <span class="info-label">من</span>
                <span class="info-total"><?= $pagination['total_items'] ?></span>
                <span class="info-label">عنصر</span>
            </div>
        </div>

        <?php if ($pagination['total_pages'] > 1): ?>
            <div class="pagination-controls">
                <nav class="pagination-nav">
                    <ul class="pagination">
                        <!-- Previous Button -->
                        <li class="page-item <?= !$pagination['has_previous'] ? 'disabled' : '' ?>">
                            <?php if ($pagination['has_previous']): ?>
                                <a class="page-link page-prev" href="javascript:goToPage(<?= $pagination['previous_page'] ?>)" title="الصفحة السابقة">
                                    <i class="mdi mdi-chevron-right"></i>
                                    <span>السابق</span>
                                </a>
                            <?php else: ?>
                                <span class="page-link page-prev disabled">
                                    <i class="mdi mdi-chevron-right"></i>
                                    <span>السابق</span>
                                </span>
                            <?php endif; ?>
                        </li>

                        <?php
                        $start = max(1, $pagination['current_page'] - 2);
                        $end = min($pagination['total_pages'], $pagination['current_page'] + 2);

                        // إضافة الصفحة الأولى إذا لم تكن ضمن النطاق
                        if ($start > 1): ?>
                            <li class="page-item">
                                <a class="page-link" href="javascript:goToPage(1)">1</a>
                            </li>
                            <?php if ($start > 2): ?>
                                <li class="page-item page-dots">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- الصفحات الحالية -->
                        <?php for ($i = $start; $i <= $end; $i++): ?>
                            <li class="page-item <?= $i == $pagination['current_page'] ? 'active' : '' ?>">
                                <?php if ($i == $pagination['current_page']): ?>
                                    <span class="page-link current"><?= $i ?></span>
                                <?php else: ?>
                                    <a class="page-link" href="javascript:goToPage(<?= $i ?>)"><?= $i ?></a>
                                <?php endif; ?>
                            </li>
                        <?php endfor; ?>

                        <!-- إضافة الصفحة الأخيرة إذا لم تكن ضمن النطاق -->
                        <?php if ($end < $pagination['total_pages']): ?>
                            <?php if ($end < $pagination['total_pages'] - 1): ?>
                                <li class="page-item page-dots">
                                    <span class="page-link">...</span>
                                </li>
                            <?php endif; ?>
                            <li class="page-item">
                                <a class="page-link" href="javascript:goToPage(<?= $pagination['total_pages'] ?>)">
                                    <?= $pagination['total_pages'] ?>
                                </a>
                            </li>
                        <?php endif; ?>

                        <!-- Next Button -->
                        <li class="page-item <?= !$pagination['has_next'] ? 'disabled' : '' ?>">
                            <?php if ($pagination['has_next']): ?>
                                <a class="page-link page-next" href="javascript:goToPage(<?= $pagination['next_page'] ?>)" title="الصفحة التالية">
                                    <span>التالي</span>
                                    <i class="mdi mdi-chevron-left"></i>
                                </a>
                            <?php else: ?>
                                <span class="page-link page-next disabled">
                                    <span>التالي</span>
                                    <i class="mdi mdi-chevron-left"></i>
                                </span>
                            <?php endif; ?>
                        </li>
                    </ul>
                </nav>
            </div>
        <?php endif; ?>
    </div>
    <?php
}

/**
 * عرض نافذة الفلاتر
 */
function render_filters_modal($module, $entity, $filters_config, $filters, $stats, $pagination)
{
    ?>
    <div class="modal filter-modal" id="filtersModal" tabindex="-1" aria-hidden="true" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-filter me-2"></i>
                        فلاتر البحث المتقدم
                    </h5>
                    <button type="button" class="modal-close" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form id="filtersForm" method="POST" action="<?= base_url($module . '/' . $entity . '/apply-filters') ?>">
                    <div class="modal-body">
                        <div class="row g-3">
                            <?php foreach ($filters_config as $filter): ?>
                                <div class="col-md-<?= $filter['col_size'] ?? 6 ?>">
                                    <label for="<?= $filter['name'] ?>" class="form-label">
                                        <i class="<?= $filter['icon'] ?> me-1"></i>
                                        <?= $filter['label'] ?>
                                    </label>

                                    <?php if ($filter['type'] === 'text' || $filter['type'] === 'search'): ?>
                                        <input type="text" class="form-control" id="<?= $filter['name'] ?>" name="<?= $filter['name'] ?>"
                                               placeholder="<?= $filter['placeholder'] ?>"
                                               value="<?= htmlspecialchars($filters[$filter['name']] ?? '') ?>">
                                        <?php if (isset($filter['help'])): ?>
                                            <div class="form-text"><?= $filter['help'] ?></div>
                                        <?php endif; ?>

                                    <?php elseif ($filter['type'] === 'select'): ?>
                                        <select class="form-select" id="<?= $filter['name'] ?>" name="<?= $filter['name'] ?>">
                                            <option value=""><?= $filter['placeholder'] ?></option>
                                            <?php foreach ($filter['options'] as $value => $text): ?>
                                                <option value="<?= $value ?>" <?= ($filters[$filter['name']] ?? '') == $value ? 'selected' : '' ?>>
                                                    <?= htmlspecialchars($text) ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>

                            <!-- معلومات إضافية -->
                            <div class="col-md-6">
                                <label class="form-label">
                                    <i class="mdi mdi-information me-1"></i>
                                    معلومات الفلترة
                                </label>
                                <div class="bg-light p-3 rounded">
                                    <small class="text-muted">
                                        <strong>النتائج الحالية:</strong> <?= $pagination['total_items'] ?? 0 ?><br>
                                        <?php if (has_active_filters($filters)): ?>
                                            <span class="text-primary">
                                                <i class="mdi mdi-filter-check me-1"></i>
                                                <strong>الفلاتر محفوظة تلقائياً</strong>
                                            </span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer bg-light">
                        <div class="row w-100">
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary w-100" onclick="clearAllFilters()">
                                    <i class="mdi mdi-filter-remove me-1"></i>
                                    مسح جميع الفلاتر
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="mdi mdi-filter-check me-1"></i>
                                    تطبيق الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- الحفاظ على عدد العناصر لكل صفحة -->
                    <input type="hidden" id="per_page_hidden" name="per_page" value="<?= $filters['per_page'] ?? 20 ?>">
                </form>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض نافذة تأكيد الحذف
 */
function render_delete_modal()
{
    ?>
    <div class="modal confirmation-modal" id="deleteModal" tabindex="-1" aria-hidden="true" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">تأكيد الحذف</h4>
                    <button type="button" class="modal-close" data-dismiss="modal">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <div class="modal-icon danger">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <p>هل أنت متأكد من حذف هذا العنصر؟</p>
                        <p class="text-muted">لا يمكن التراجع عن هذا الإجراء.</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                    <form id="deleteForm" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-danger">حذف</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript المطلوب
 */
function render_datatable_scripts($module, $entity, $filters)
{
    ?>
    <script>
    function confirmDelete(entityNumber) {
        document.getElementById('deleteForm').action = '<?= base_url($module . '/' . $entity . '/') ?>' + entityNumber + '/delete';
        const modal = document.getElementById('deleteModal');
        if (modal) {
            // استخدام النظام المخصص بدلاً من Bootstrap
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.body.classList.add('modal-open');
        }
    }

    function clearAllFilters() {
        if (confirm('هل أنت متأكد من مسح جميع الفلاتر؟')) {
            window.location.href = '<?= base_url($module . '/' . $entity . '/clear-filters') ?>';
        }
    }

    function goToPage(page) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?= base_url($module . '/' . $entity . '/apply-filters') ?>';

        const pageInput = document.createElement('input');
        pageInput.type = 'hidden';
        pageInput.name = 'current_page';
        pageInput.value = page;
        form.appendChild(pageInput);

        const currentFilters = <?= json_encode($filters) ?>;
        for (const [key, value] of Object.entries(currentFilters)) {
            if (key !== 'current_page') {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }
        }

        document.body.appendChild(form);
        form.submit();
    }

    // فرز الجدول بـ JavaScript
    let currentSort = { column: null, direction: 'asc' };

    function sortTable(columnIndex, dataType = 'text') {
        const table = document.querySelector('.table tbody');
        const rows = Array.from(table.querySelectorAll('tr'));

        // تحديد اتجاه الفرز
        if (currentSort.column === columnIndex) {
            currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            currentSort.direction = 'asc';
        }
        currentSort.column = columnIndex;

        // فرز الصفوف
        rows.sort((a, b) => {
            const aCell = a.cells[columnIndex];
            const bCell = b.cells[columnIndex];

            if (!aCell || !bCell) return 0;

            let aValue = aCell.textContent.trim();
            let bValue = bCell.textContent.trim();

            // معالجة أنواع البيانات المختلفة
            if (dataType === 'number') {
                aValue = parseFloat(aValue.replace(/[^\d.-]/g, '')) || 0;
                bValue = parseFloat(bValue.replace(/[^\d.-]/g, '')) || 0;
            } else if (dataType === 'date') {
                aValue = new Date(aValue);
                bValue = new Date(bValue);
            } else {
                // نص عادي - دعم العربية
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            let comparison = 0;
            if (aValue > bValue) {
                comparison = 1;
            } else if (aValue < bValue) {
                comparison = -1;
            }

            return currentSort.direction === 'desc' ? comparison * -1 : comparison;
        });

        // إعادة ترتيب الصفوف في الجدول
        rows.forEach(row => table.appendChild(row));

        // تحديث أيقونات الفرز
        updateSortIcons(columnIndex);
    }

function updateSortIcons(activeColumn) {
    // إزالة جميع حالات الفرز
    document.querySelectorAll('th[data-sortable="true"]').forEach(th => {
        th.classList.remove('sorted-asc', 'sorted-desc');
        const icon = th.querySelector('.sort-icon');
        if (icon) {
            icon.innerHTML = '<i class="fas fa-sort text-muted"></i>';
        }
    });

    // إضافة حالة الفرز للعمود النشط
    const activeHeader = document.querySelector(`th[data-column="${activeColumn}"]`);
    if (activeHeader) {
        const activeIcon = activeHeader.querySelector('.sort-icon');
        if (activeIcon) {
            if (currentSort.direction === 'asc') {
                activeIcon.innerHTML = '<i class="fas fa-sort-up text-primary"></i>';
                activeHeader.classList.add('sorted-asc');
            } else {
                activeIcon.innerHTML = '<i class="fas fa-sort-down text-primary"></i>';
                activeHeader.classList.add('sorted-desc');
            }
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const filterButton = document.querySelector('[data-target="#filtersModal"]');
    const activeFiltersCount = <?= count_active_filters_with_saved_check($entity, $filters) ?>;

    // تهيئة الأعمدة للفرز
    document.querySelectorAll('th[data-sortable="true"]').forEach((th, index) => {
        th.style.cursor = 'pointer';
        th.style.userSelect = 'none';

        // إضافة أيقونة الفرز الافتراضية إن لم تكن موجودة
        if (!th.querySelector('.sort-icon')) {
            th.innerHTML += ' <span class="sort-icon"><i class="fas fa-sort text-muted"></i></span>';
        }

        th.setAttribute('data-column', index);

        // عند الضغط على العمود يتم الفرز
        th.addEventListener('click', function() {
            const dataType = th.getAttribute('data-type') || 'text';
            sortTable(index, dataType);
        });
    });


        // إضافة CSS للأيقونات الذكية (مع متغيرات النظام)
        const style = document.createElement('style');
        style.textContent = `
            .sort-icon {
                position: absolute;
                right: 12px;
                top: 50%;
                transform: translateY(-50%);
                opacity: 0;
                visibility: hidden;
                transition: opacity 0.3s ease, visibility 0.3s ease;
            }

            .sort-icon i {
                font-size: 12px;
                color: var(--light-text-muted, #6c757d);
                transition: color 0.2s ease;
            }

            /* إظهار عند التمرير على العمود */
            th[data-sortable="true"]:hover .sort-icon {
                opacity: 1;
                visibility: visible;
            }

            /* تغيير لون عند التمرير */
            th[data-sortable="true"]:hover .sort-icon i {
                color: var(--light-text-color, #495057);
            }

            /* الأيقونة النشطة - تبقى ظاهرة دائماً */
            th.sorted-asc .sort-icon,
            th.sorted-desc .sort-icon {
                opacity: 1 !important;
                visibility: visible !important;
            }

            /* لون الأيقونة النشطة */
            th.sorted-asc .sort-icon i,
            th.sorted-desc .sort-icon i {
                color: var(--primary-color, #6366f1) !important;
            }

            /* تحسين موضع العمود */
            th[data-sortable="true"] {
                position: relative;
                padding-right: 40px;
            }

            /* تأثير خفيف عند التمرير */
            th[data-sortable="true"]:hover {
                background: var(--gray-200, #f3f4f6);
                transition: background-color 0.2s ease;
            }

            /* إخفاء النصوص الافتراضية */
            .text-muted {
                color: transparent !important;
            }

            /* دعم الثيم الداكن */
            body.dark-theme .sort-icon i {
                color: var(--dark-text-muted, #9ca3af);
            }

            body.dark-theme th[data-sortable="true"]:hover .sort-icon i {
                color: var(--dark-text-color, #f9fafb);
            }

            body.dark-theme th.sorted-asc .sort-icon i,
            body.dark-theme th.sorted-desc .sort-icon i {
                color: var(--primary-color, #6366f1) !important;
            }

            body.dark-theme th[data-sortable="true"]:hover {
                background: var(--gray-700, #4b5563);
            }
        `;
        document.head.appendChild(style);
    });
    </script>
    <?php
}
