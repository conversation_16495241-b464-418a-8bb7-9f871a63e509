/**
 * مكون الفلاتر المتقدم - يدعم Select المتقدم
 */

(function() {
    'use strict';

    // إعدادات الفلاتر
    const FILTER_CONFIG = {
        selectLibrary: 'select2', // select2, choices, tomselect
        autoSubmit: false,
        debounceTime: 500,
        animationDuration: 300
    };

    // كلاس إدارة الفلاتر
    class FilterManager {
        constructor(formSelector, options = {}) {
            this.form = document.querySelector(formSelector);
            this.config = { ...FILTER_CONFIG, ...options };
            this.selectInstances = new Map();
            this.debounceTimers = new Map();
            
            if (this.form) {
                this.init();
            }
        }

        init() {
            this.initializeSelects();
            this.attachEvents();
            this.setupAutoSubmit();
            console.log('🎯 تم تهيئة مدير الفلاتر');
        }

        initializeSelects() {
            // تهيئة جميع select في الفلاتر
            const selects = this.form.querySelectorAll('select');
            
            selects.forEach(select => {
                const config = this.getSelectConfig(select);
                
                // إضافة data attribute للتهيئة التلقائية
                select.setAttribute('data-select', 'true');
                select.setAttribute('data-select-config', JSON.stringify(config));
                
                // تهيئة Select المتقدم
                if (window.initAdvancedSelect) {
                    const instance = window.initAdvancedSelect(select, config);
                    this.selectInstances.set(select, instance);
                }
            });
        }

        getSelectConfig(select) {
            const baseConfig = {
                library: this.config.selectLibrary,
                rtl: document.dir === 'rtl' || document.documentElement.lang === 'ar',
                searchable: true,
                clearable: true,
                placeholder: select.getAttribute('placeholder') || 'اختر...'
            };

            // إعدادات خاصة حسب نوع الفلتر
            const filterType = select.getAttribute('data-filter-type');
            
            switch (filterType) {
                case 'status':
                    return {
                        ...baseConfig,
                        searchable: false,
                        placeholder: 'جميع الحالات'
                    };
                    
                case 'group':
                    return {
                        ...baseConfig,
                        placeholder: 'جميع المجموعات',
                        ajax: this.getAjaxConfig(select)
                    };
                    
                case 'user':
                    return {
                        ...baseConfig,
                        placeholder: 'جميع المستخدمين',
                        ajax: this.getAjaxConfig(select)
                    };
                    
                case 'category':
                    return {
                        ...baseConfig,
                        placeholder: 'جميع الفئات',
                        allowCreate: true
                    };
                    
                default:
                    return baseConfig;
            }
        }

        getAjaxConfig(select) {
            const ajaxUrl = select.getAttribute('data-ajax-url');
            if (!ajaxUrl) return null;

            return {
                url: ajaxUrl,
                dataType: 'json',
                delay: 250,
                data: function(params) {
                    return {
                        search: params.term,
                        page: params.page || 1,
                        per_page: 20
                    };
                },
                processResults: function(data, params) {
                    params.page = params.page || 1;
                    
                    return {
                        results: data.items || data.data || [],
                        pagination: {
                            more: data.has_more || false
                        }
                    };
                },
                cache: true
            };
        }

        attachEvents() {
            // أحداث النموذج
            this.form.addEventListener('submit', (e) => this.handleSubmit(e));
            this.form.addEventListener('reset', (e) => this.handleReset(e));
            
            // أحداث الحقول
            const inputs = this.form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('change', (e) => this.handleFieldChange(e));
                
                if (input.type === 'text' || input.type === 'search') {
                    input.addEventListener('input', (e) => this.handleFieldInput(e));
                }
            });

            // أحداث الأزرار
            const clearBtn = this.form.querySelector('[data-action="clear"]');
            if (clearBtn) {
                clearBtn.addEventListener('click', (e) => this.clearAllFilters(e));
            }

            const resetBtn = this.form.querySelector('[data-action="reset"]');
            if (resetBtn) {
                resetBtn.addEventListener('click', (e) => this.resetFilters(e));
            }
        }

        setupAutoSubmit() {
            if (!this.config.autoSubmit) return;

            const inputs = this.form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                const eventType = input.type === 'text' || input.type === 'search' ? 'input' : 'change';
                
                input.addEventListener(eventType, (e) => {
                    this.debounceSubmit(e.target);
                });
            });
        }

        debounceSubmit(element) {
            const key = element.name || element.id;
            
            // إلغاء المؤقت السابق
            if (this.debounceTimers.has(key)) {
                clearTimeout(this.debounceTimers.get(key));
            }
            
            // إنشاء مؤقت جديد
            const timer = setTimeout(() => {
                this.submitForm();
                this.debounceTimers.delete(key);
            }, this.config.debounceTime);
            
            this.debounceTimers.set(key, timer);
        }

        handleSubmit(e) {
            e.preventDefault();
            this.submitForm();
        }

        handleReset(e) {
            e.preventDefault();
            this.resetFilters();
        }

        handleFieldChange(e) {
            const field = e.target;
            
            // إضافة تأثير بصري
            field.classList.add('filter-changed');
            setTimeout(() => {
                field.classList.remove('filter-changed');
            }, this.config.animationDuration);

            // تحديث عداد الفلاتر النشطة
            this.updateActiveFiltersCount();
        }

        handleFieldInput(e) {
            const field = e.target;
            
            // إضافة مؤشر الكتابة
            field.classList.add('filter-typing');
            
            setTimeout(() => {
                field.classList.remove('filter-typing');
            }, 1000);
        }

        submitForm() {
            // إضافة loading state
            this.setLoadingState(true);
            
            // جمع بيانات النموذج
            const formData = new FormData(this.form);
            const data = Object.fromEntries(formData.entries());
            
            // إرسال الطلب
            fetch(this.form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (response.ok) {
                    // إعادة تحميل الصفحة أو تحديث النتائج
                    window.location.reload();
                } else {
                    throw new Error('فشل في تطبيق الفلاتر');
                }
            })
            .catch(error => {
                console.error('خطأ في الفلاتر:', error);
                if (window.toastr) {
                    toastr.error('حدث خطأ في تطبيق الفلاتر');
                }
            })
            .finally(() => {
                this.setLoadingState(false);
            });
        }

        clearAllFilters(e) {
            e.preventDefault();
            
            // مسح جميع الحقول
            const inputs = this.form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                if (input.type === 'checkbox' || input.type === 'radio') {
                    input.checked = false;
                } else {
                    input.value = '';
                }
                
                // مسح Select المتقدم
                const selectInstance = this.selectInstances.get(input);
                if (selectInstance) {
                    selectInstance.clear();
                }
            });
            
            // تحديث العداد
            this.updateActiveFiltersCount();
            
            // إرسال النموذج إذا كان auto-submit مفعل
            if (this.config.autoSubmit) {
                this.submitForm();
            }
        }

        resetFilters(e) {
            e.preventDefault();
            this.form.reset();
            
            // إعادة تعيين Select المتقدم
            this.selectInstances.forEach(instance => {
                instance.clear();
            });
            
            this.updateActiveFiltersCount();
        }

        updateActiveFiltersCount() {
            const activeCount = this.getActiveFiltersCount();
            const badge = document.querySelector('.datatable-filter-badge');
            const filterBtn = document.querySelector('.datatable-filter-icon');
            
            if (badge) {
                if (activeCount > 0) {
                    badge.textContent = activeCount;
                    badge.style.display = 'inline-block';
                } else {
                    badge.style.display = 'none';
                }
            }
            
            if (filterBtn) {
                filterBtn.classList.toggle('has-active-filters', activeCount > 0);
            }
        }

        getActiveFiltersCount() {
            let count = 0;
            const inputs = this.form.querySelectorAll('input, select, textarea');
            
            inputs.forEach(input => {
                if (input.name === 'per_page' || input.name === 'current_page') {
                    return; // تجاهل هذه الحقول
                }
                
                if (input.type === 'checkbox' || input.type === 'radio') {
                    if (input.checked) count++;
                } else if (input.value && input.value.trim() !== '') {
                    count++;
                }
            });
            
            return count;
        }

        setLoadingState(loading) {
            const submitBtn = this.form.querySelector('button[type="submit"]');
            const filterBtn = document.querySelector('.datatable-filter-icon');
            
            if (submitBtn) {
                submitBtn.disabled = loading;
                submitBtn.classList.toggle('loading', loading);
            }
            
            if (filterBtn) {
                filterBtn.classList.toggle('loading', loading);
            }
        }

        // API عام
        getFilterValue(name) {
            const field = this.form.querySelector(`[name="${name}"]`);
            if (!field) return null;
            
            const selectInstance = this.selectInstances.get(field);
            if (selectInstance) {
                return selectInstance.getValue();
            }
            
            return field.value;
        }

        setFilterValue(name, value) {
            const field = this.form.querySelector(`[name="${name}"]`);
            if (!field) return;
            
            const selectInstance = this.selectInstances.get(field);
            if (selectInstance) {
                selectInstance.setValue(value);
            } else {
                field.value = value;
            }
        }

        destroy() {
            // تنظيف المؤقتات
            this.debounceTimers.forEach(timer => clearTimeout(timer));
            this.debounceTimers.clear();
            
            // تنظيف Select instances
            this.selectInstances.forEach(instance => {
                if (instance.destroy) {
                    instance.destroy();
                }
            });
            this.selectInstances.clear();
        }
    }

    // تصدير للاستخدام العام
    window.FilterManager = FilterManager;

    // تهيئة تلقائية
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة فلاتر الجداول
        const filterForm = document.querySelector('#filtersForm');
        if (filterForm) {
            window.filtersManager = new FilterManager('#filtersForm', {
                selectLibrary: 'select2',
                autoSubmit: false
            });
        }
        
        console.log('🎯 تم تهيئة مكون الفلاتر');
    });

})();
