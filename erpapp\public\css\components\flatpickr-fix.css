/**
 * إصلاحات Flatpickr للعمل مع Bootstrap RTL
 */

/* إصلاح z-index للتأكد من ظهور التقويم فوق العناصر الأخرى */
.flatpickr-calendar {
    z-index: 9999 !important;
    position: absolute !important;
    pointer-events: auto !important;
}

/* إصلاح التفاعل مع الأيام */
.flatpickr-calendar .flatpickr-day {
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: none !important;
}

.flatpickr-calendar .flatpickr-day:hover {
    background: #e3f2fd !important;
    border-color: #2196f3 !important;
}

.flatpickr-calendar .flatpickr-day.selected {
    background: #2196f3 !important;
    border-color: #2196f3 !important;
    color: white !important;
}

.flatpickr-calendar .flatpickr-day.today {
    border-color: #ff9800 !important;
}

.flatpickr-calendar .flatpickr-day.today.selected {
    background: #2196f3 !important;
    border-color: #2196f3 !important;
}

/* إصلاح أزرار التنقل */
.flatpickr-calendar .flatpickr-prev-month,
.flatpickr-calendar .flatpickr-next-month {
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: none !important;
}

.flatpickr-calendar .flatpickr-prev-month:hover,
.flatpickr-calendar .flatpickr-next-month:hover {
    color: #2196f3 !important;
}

/* إصلاح قائمة الشهور والسنوات */
.flatpickr-calendar .flatpickr-monthDropdown-months,
.flatpickr-calendar .numInputWrapper {
    pointer-events: auto !important;
}

.flatpickr-calendar .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
    pointer-events: auto !important;
    cursor: pointer !important;
}

.flatpickr-calendar .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month:hover {
    background: #e3f2fd !important;
}

/* إصلاح input الوقت */
.flatpickr-calendar .flatpickr-time input {
    pointer-events: auto !important;
    cursor: text !important;
}

.flatpickr-calendar .flatpickr-time .flatpickr-time-separator,
.flatpickr-calendar .flatpickr-time .flatpickr-am-pm {
    pointer-events: auto !important;
    cursor: pointer !important;
}

/* إصلاح أزرار الوقت */
.flatpickr-calendar .flatpickr-time .arrowUp,
.flatpickr-calendar .flatpickr-time .arrowDown {
    pointer-events: auto !important;
    cursor: pointer !important;
    user-select: none !important;
}

.flatpickr-calendar .flatpickr-time .arrowUp:hover,
.flatpickr-calendar .flatpickr-time .arrowDown:hover {
    color: #2196f3 !important;
}

/* إصلاح للـ RTL */
.flatpickr-calendar[dir="rtl"] {
    direction: rtl !important;
}

.flatpickr-calendar[dir="rtl"] .flatpickr-prev-month {
    right: auto !important;
    left: 12px !important;
}

.flatpickr-calendar[dir="rtl"] .flatpickr-next-month {
    left: auto !important;
    right: 12px !important;
}

/* إصلاح تضارب مع Bootstrap */
.flatpickr-calendar * {
    box-sizing: border-box !important;
}

.flatpickr-calendar .flatpickr-day {
    border-radius: 0 !important;
    border: 1px solid transparent !important;
    display: inline-block !important;
    width: 39px !important;
    height: 39px !important;
    line-height: 39px !important;
    text-align: center !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* إصلاح الخط */
.flatpickr-calendar {
    font-family: 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-size: 14px !important;
}

/* إصلاح الألوان للثيم الداكن */
.dark-theme .flatpickr-calendar {
    background: #2d3748 !important;
    border-color: #4a5568 !important;
    color: #e2e8f0 !important;
}

.dark-theme .flatpickr-calendar .flatpickr-day {
    color: #e2e8f0 !important;
}

.dark-theme .flatpickr-calendar .flatpickr-day:hover {
    background: #4a5568 !important;
    border-color: #63b3ed !important;
}

.dark-theme .flatpickr-calendar .flatpickr-day.selected {
    background: #3182ce !important;
    border-color: #3182ce !important;
}

.dark-theme .flatpickr-calendar .flatpickr-months {
    background: #2d3748 !important;
    border-bottom-color: #4a5568 !important;
}

.dark-theme .flatpickr-calendar .flatpickr-current-month {
    color: #e2e8f0 !important;
}

.dark-theme .flatpickr-calendar .flatpickr-weekdays {
    background: #2d3748 !important;
}

.dark-theme .flatpickr-calendar .flatpickr-weekday {
    color: #a0aec0 !important;
}

/* إصلاح مشكلة التداخل مع Modal */
.modal .flatpickr-calendar {
    z-index: 10000 !important;
}

/* إصلاح مشكلة التداخل مع Dropdown */
.dropdown-menu .flatpickr-calendar {
    z-index: 10001 !important;
}

/* إصلاح مشكلة التداخل مع Navbar */
.navbar .flatpickr-calendar {
    z-index: 10002 !important;
}

/* إصلاح للتأكد من عدم تأثر التقويم بـ CSS الخارجي */
.flatpickr-calendar,
.flatpickr-calendar * {
    -webkit-touch-callout: default !important;
    -webkit-user-select: auto !important;
    -khtml-user-select: auto !important;
    -moz-user-select: auto !important;
    -ms-user-select: auto !important;
    user-select: auto !important;
}

/* إصلاح مشكلة عدم الاستجابة للنقر */
.flatpickr-input {
    cursor: pointer !important;
}

.flatpickr-input:focus {
    outline: none !important;
    border-color: #2196f3 !important;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2) !important;
}

/* إصلاح للتأكد من ظهور التقويم */
.flatpickr-calendar.open {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* إصلاح animation */
.flatpickr-calendar.animate {
    transition: opacity 0.3s ease !important;
}

/* إصلاح مشكلة الـ positioning */
.flatpickr-wrapper {
    position: relative !important;
}

/* إصلاح للتأكد من عمل الـ range mode */
.flatpickr-calendar .flatpickr-day.inRange {
    background: rgba(33, 150, 243, 0.1) !important;
    border-color: rgba(33, 150, 243, 0.3) !important;
}

.flatpickr-calendar .flatpickr-day.startRange,
.flatpickr-calendar .flatpickr-day.endRange {
    background: #2196f3 !important;
    border-color: #2196f3 !important;
    color: white !important;
}

/* إصلاح للتأكد من عمل الـ multiple mode */
.flatpickr-calendar .flatpickr-day.selected.nextMonthDay,
.flatpickr-calendar .flatpickr-day.selected.prevMonthDay {
    background: #2196f3 !important;
    border-color: #2196f3 !important;
    color: white !important;
}

/* إصلاح مشكلة الـ disabled days */
.flatpickr-calendar .flatpickr-day.flatpickr-disabled {
    color: #ccc !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.flatpickr-calendar .flatpickr-day.flatpickr-disabled:hover {
    background: transparent !important;
    border-color: transparent !important;
}
