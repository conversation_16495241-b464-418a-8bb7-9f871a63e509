# 📊 دليل استيراد ملفات CSV

## 🎯 المشكلة والحل

**المشكلة:** رسالة "حدث خطأ في قراءة الملف" عند استيراد Excel

**الحل:** تم تحديث النظام ليدعم ملفات CSV بشكل مثالي مع دعم كامل للعربية

## 📝 كيفية إنشاء ملف CSV صحيح

### 1️⃣ **باستخدام Microsoft Excel:**
1. افتح Excel وأنشئ جدول جديد
2. أضف رؤوس الأعمدة في الصف الأول:
   ```
   اسم المورد | البريد الإلكتروني | رقم الهاتف | اسم الشركة | الرقم الضريبي
   ```
3. أضف البيانات في الصفوف التالية
4. اذهب إلى **ملف > حفظ باسم**
5. اختر نوع الملف: **CSV UTF-8 (Comma delimited)**
6. احفظ الملف

### 2️⃣ **باستخدام Google Sheets:**
1. افتح Google Sheets وأنشئ جدول جديد
2. أضف رؤوس الأعمدة والبيانات
3. اذهب إلى **ملف > تنزيل > قيم مفصولة بفواصل (.csv)**

### 3️⃣ **باستخدام محرر النصوص:**
```csv
اسم المورد,البريد الإلكتروني,رقم الهاتف,اسم الشركة,الرقم الضريبي
مورد تجريبي 1,<EMAIL>,*********,شركة تجريبية 1,123456
مورد تجريبي 2,<EMAIL>,*********,شركة تجريبية 2,654321
```

## 🔧 الأعمدة المدعومة

| عمود الملف | يطابق في النظام | مطلوب |
|------------|-----------------|-------|
| اسم المورد | G_name_ar | ✅ نعم |
| الشخص المسؤول | S_contact_person | ❌ لا |
| البريد الإلكتروني | S_email | ❌ لا |
| رقم الهاتف | G_phone | ❌ لا |
| اسم الشركة | S_company_name | ❌ لا |
| الرقم الضريبي | S_tax_number | ❌ لا |
| الحد الائتماني | S_credit_limit | ❌ لا |
| شروط الدفع | S_payment_terms | ❌ لا |
| الحالة | G_status | ❌ لا |

## 🚀 خطوات الاستيراد

### 1️⃣ **الوصول للنظام:**
- انتقل إلى: `/purchases/suppliers`
- انقر على زر **"استيراد"** في شريط الأدوات

### 2️⃣ **رفع الملف:**
- اختر ملف CSV من جهازك
- أو اسحب الملف إلى منطقة الرفع
- تأكد من أن الملف بصيغة `.csv`

### 3️⃣ **معاينة البيانات:**
- سيعرض النظام أول 10 صفوف للمراجعة
- تحقق من صحة البيانات

### 4️⃣ **تطابق الأعمدة:**
- سيقترح النظام تطابق تلقائي للأعمدة
- راجع التطابق وعدّل إذا لزم الأمر

### 5️⃣ **الاستيراد:**
- انقر **"استيراد البيانات"**
- انتظر رسالة النجاح

## 🧪 اختبار النظام

### **ملف تجريبي جاهز:**
```
erpapp/storage/uploads/temp/test_suppliers.csv
```

### **صفحات الاختبار:**
- **اختبار شامل:** `/erpapp/test_csv_import.php`
- **اختبار بسيط:** `/erpapp/test_simple_import.php`
- **تشخيص المشاكل:** `/erpapp/debug_import.php`

## ⚠️ نصائح مهمة

### ✅ **افعل:**
- استخدم ترميز UTF-8 للعربية
- تأكد من وجود رؤوس أعمدة في الصف الأول
- استخدم فواصل (,) لفصل الأعمدة
- احفظ الملف بصيغة .csv

### ❌ **لا تفعل:**
- لا تستخدم ملفات Excel (.xlsx) حالياً
- لا تترك الصف الأول فارغاً
- لا تستخدم فواصل منقوطة (;)
- لا تحفظ بترميز ANSI

## 🔍 حل المشاكل الشائعة

### **المشكلة:** "نوع الملف غير مدعوم"
**الحل:** تأكد من أن الملف بصيغة .csv

### **المشكلة:** "لم يتم العثور على رؤوس أعمدة"
**الحل:** تأكد من وجود رؤوس في الصف الأول

### **المشكلة:** "الملف فارغ"
**الحل:** تأكد من وجود بيانات بعد رؤوس الأعمدة

### **المشكلة:** "أحرف غريبة في النص العربي"
**الحل:** احفظ الملف بترميز UTF-8

## 📞 الدعم الفني

إذا واجهت أي مشاكل:

1. **جرب صفحة الاختبار:** `/erpapp/test_csv_import.php`
2. **استخدم الملف التجريبي** المرفق
3. **تحقق من تنسيق الملف** حسب التعليمات أعلاه

## 🎉 مميزات النظام الحالي

- ✅ **دعم كامل للعربية** في ملفات CSV
- ✅ **معاينة البيانات** قبل الاستيراد
- ✅ **تطابق تلقائي للأعمدة**
- ✅ **التحقق من صحة البيانات**
- ✅ **تقارير مفصلة** للنجاح والأخطاء
- ✅ **واجهة سهلة الاستخدام**

النظام جاهز للاستخدام مع ملفات CSV! 🚀
