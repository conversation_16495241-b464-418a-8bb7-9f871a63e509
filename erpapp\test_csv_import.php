<?php
/**
 * اختبار استيراد CSV
 */

// تحميل النظام
require_once __DIR__ . '/loader.php';

echo "<h1>🧪 اختبار استيراد CSV</h1>";

// اختبار الملف التجريبي
$test_file = BASE_PATH . '/storage/uploads/temp/test_suppliers.csv';

echo "<h2>📁 اختبار الملف التجريبي</h2>";
echo "<p><strong>مسار الملف:</strong> " . $test_file . "</p>";

if (file_exists($test_file)) {
    echo "<p>✅ الملف التجريبي موجود</p>";
    
    // اختبار قراءة الملف
    echo "<h3>📖 اختبار قراءة الملف</h3>";
    
    if (function_exists('read_import_file')) {
        $result = read_import_file($test_file);
        
        if ($result['success']) {
            echo "<p>✅ تم قراءة الملف بنجاح!</p>";
            echo "<p><strong>عدد الرؤوس:</strong> " . count($result['headers']) . "</p>";
            echo "<p><strong>عدد الصفوف:</strong> " . $result['total_rows'] . "</p>";
            
            echo "<h4>رؤوس الأعمدة:</h4>";
            echo "<ul>";
            foreach ($result['headers'] as $header) {
                echo "<li>" . htmlspecialchars($header) . "</li>";
            }
            echo "</ul>";
            
            echo "<h4>البيانات:</h4>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr>";
            foreach ($result['headers'] as $header) {
                echo "<th style='padding: 8px; background: #f0f0f0; text-align: right;'>" . htmlspecialchars($header) . "</th>";
            }
            echo "</tr>";
            
            foreach ($result['data'] as $row) {
                echo "<tr>";
                foreach ($result['headers'] as $header) {
                    echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: right;'>" . htmlspecialchars($row[$header] ?? '') . "</td>";
                }
                echo "</tr>";
            }
            echo "</table>";
            
            // اختبار تطابق الأعمدة
            echo "<h3>🔗 اختبار تطابق الأعمدة</h3>";
            
            $table_columns = [
                ['field' => 'G_name_ar', 'title' => 'اسم المورد'],
                ['field' => 'S_email', 'title' => 'البريد الإلكتروني'],
                ['field' => 'G_phone', 'title' => 'رقم الهاتف'],
                ['field' => 'S_company_name', 'title' => 'اسم الشركة'],
                ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي']
            ];
            
            if (function_exists('match_import_columns')) {
                $matches = match_import_columns($result['headers'], $table_columns);
                
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr>";
                echo "<th style='padding: 8px; background: #e3f2fd;'>عمود الملف</th>";
                echo "<th style='padding: 8px; background: #e3f2fd;'>عمود الجدول المقترح</th>";
                echo "<th style='padding: 8px; background: #e3f2fd;'>نسبة التطابق</th>";
                echo "</tr>";
                
                foreach ($result['headers'] as $header) {
                    $suggested_field = $matches['matches'][$header] ?? 'لا يوجد';
                    $score = $matches['suggestions'][$header] ?? 0;
                    
                    echo "<tr>";
                    echo "<td style='padding: 8px; text-align: right;'>" . htmlspecialchars($header) . "</td>";
                    echo "<td style='padding: 8px; text-align: right;'>" . htmlspecialchars($suggested_field) . "</td>";
                    echo "<td style='padding: 8px; text-align: center;'>" . round($score, 1) . "%</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            
        } else {
            echo "<p>❌ فشل في قراءة الملف:</p>";
            echo "<div style='background: #ffebee; padding: 10px; border-radius: 5px; color: #c62828;'>";
            echo htmlspecialchars($result['error']);
            echo "</div>";
        }
    } else {
        echo "<p>❌ دالة read_import_file غير متاحة</p>";
    }
    
} else {
    echo "<p>❌ الملف التجريبي غير موجود</p>";
}

// اختبار رفع ملف جديد
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['csv_file'])) {
    echo "<h2>📤 اختبار الملف المرفوع</h2>";
    
    $file = $_FILES['csv_file'];
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        $tempPath = BASE_PATH . '/storage/uploads/temp/';
        $tempFile = $tempPath . uniqid() . '_' . $file['name'];
        
        if (move_uploaded_file($file['tmp_name'], $tempFile)) {
            echo "<p>✅ تم رفع الملف بنجاح</p>";
            
            $result = read_import_file($tempFile);
            
            if ($result['success']) {
                echo "<p>✅ تم قراءة الملف المرفوع بنجاح!</p>";
                echo "<p><strong>عدد الرؤوس:</strong> " . count($result['headers']) . "</p>";
                echo "<p><strong>عدد الصفوف:</strong> " . $result['total_rows'] . "</p>";
                
                echo "<h4>معاينة البيانات:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr>";
                foreach ($result['headers'] as $header) {
                    echo "<th style='padding: 8px; background: #f0f0f0; text-align: right;'>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                
                $preview_data = array_slice($result['data'], 0, 5);
                foreach ($preview_data as $row) {
                    echo "<tr>";
                    foreach ($result['headers'] as $header) {
                        echo "<td style='padding: 8px; border: 1px solid #ddd; text-align: right;'>" . htmlspecialchars($row[$header] ?? '') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
                
            } else {
                echo "<p>❌ فشل في قراءة الملف المرفوع:</p>";
                echo "<div style='background: #ffebee; padding: 10px; border-radius: 5px; color: #c62828;'>";
                echo htmlspecialchars($result['error']);
                echo "</div>";
            }
            
            // حذف الملف المؤقت
            unlink($tempFile);
            
        } else {
            echo "<p>❌ فشل في رفع الملف</p>";
        }
    } else {
        echo "<p>❌ خطأ في رفع الملف: " . $file['error'] . "</p>";
    }
}

?>

<h2>📤 رفع ملف CSV للاختبار</h2>
<form method="POST" enctype="multipart/form-data" style="background: #f5f5f5; padding: 20px; border-radius: 8px;">
    <p>
        <label for="csv_file"><strong>اختر ملف CSV:</strong></label><br>
        <input type="file" id="csv_file" name="csv_file" accept=".csv" required>
    </p>
    <p>
        <button type="submit" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
            رفع واختبار الملف
        </button>
    </p>
</form>

<h2>💡 تعليمات إنشاء ملف CSV</h2>
<div style="background: #e8f5e9; padding: 15px; border-radius: 8px;">
    <p><strong>لإنشاء ملف CSV صحيح:</strong></p>
    <ol>
        <li>افتح برنامج Excel أو Google Sheets</li>
        <li>أضف رؤوس الأعمدة في الصف الأول مثل: اسم المورد، البريد الإلكتروني، رقم الهاتف</li>
        <li>أضف البيانات في الصفوف التالية</li>
        <li>احفظ الملف بصيغة CSV (UTF-8)</li>
        <li>تأكد من أن الملف يحتوي على ترميز UTF-8 لدعم العربية</li>
    </ol>
</div>

<style>
body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
h1, h2, h3, h4 { color: #333; }
table { width: 100%; margin: 10px 0; }
th, td { text-align: right; }
.success { color: #4CAF50; }
.error { color: #f44336; }
</style>
