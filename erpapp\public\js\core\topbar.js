/**
 * نظام الشريط العلوي - وظائف إدارة التوب بار
 * Topbar System - Topbar management functions
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTopbarApp);
    } else {
        initTopbarApp();
    }

    function initTopbarApp() {
        console.log('🚀 بدء تهيئة نظام التوب بار...');

        // تهيئة جميع مكونات التوب بار
        initKeyboardShortcuts();
        initTopbarDropdowns();
        initNotificationsSystem();
        initMessagesSystem();
        initQuickSettingsSystem();
        initTopbarActions();

        console.log('✅ تم تهيئة نظام التوب بار بنجاح');
    }

    // تصدير الوظائف للاستخدام الخارجي (اختياري)
    window.TopbarSystem = {
        init: initTopbarSystem,
        initKeyboardShortcuts: initKeyboardShortcuts,
        initDropdowns: initTopbarDropdowns,
        initNotifications: initNotificationsSystem,
        initMessages: initMessagesSystem,
        initQuickSettings: initQuickSettingsSystem
    };

    // ===== تهيئة نظام التوب بار الرئيسي ===== //
    function initTopbarSystem() {
        // تهيئة جميع مكونات التوب بار
        initKeyboardShortcuts();
        initTopbarDropdowns();
        initNotificationsSystem();
        initMessagesSystem();
        initQuickSettingsSystem();
        initTopbarActions();
    }

    // ===== اختصارات لوحة المفاتيح ===== //
    function initKeyboardShortcuts() {
        const shortcutsModal = document.getElementById('keyboard-shortcuts-modal');
        const shortcutsBtn = document.getElementById('keyboard-shortcuts-btn');
        const closeShortcutsBtn = document.getElementById('close-keyboard-shortcuts');

        // فتح مودال الاختصارات
        if (shortcutsBtn && shortcutsModal) {
            shortcutsBtn.addEventListener('click', () => {
                shortcutsModal.classList.add('show');
            });
        }

        // إغلاق مودال الاختصارات
        if (closeShortcutsBtn && shortcutsModal) {
            closeShortcutsBtn.addEventListener('click', () => shortcutsModal.classList.remove('show'));
            shortcutsModal.addEventListener('click', e => {
                if (e.target === shortcutsModal) shortcutsModal.classList.remove('show');
            });
        }

        // دالة موحدة للتعامل مع ضغطات المفاتيح
        document.addEventListener('keydown', function(e) {
            const tag = e.target.tagName;
            const editable = e.target.isContentEditable;

            // 1) إغلاق المودال بالـ Escape
            if (e.key === 'Escape' && shortcutsModal && shortcutsModal.classList.contains('show')) {
                shortcutsModal.classList.remove('show');
                return;
            }

            // 2) فتح/إغلاق المودال بالـ '?' (Shift + '/')
            if (!e.altKey && !e.ctrlKey && !e.metaKey && e.key === '?') {
                e.preventDefault();
                if (shortcutsModal) {
                    shortcutsModal.classList.toggle('show');
                }
                return;
            }

            // 3) بقية الاختصارات (تجاهل الحقول القابلة للتعديل)
            if (tag === 'INPUT' || tag === 'TEXTAREA' || editable) return;

            // 4) اختصارات Alt + مفتاح (باستخدام e.code للغة مستقلّة)
            if (e.altKey && !e.ctrlKey && !e.metaKey) {
                switch (e.code) {
                    case 'KeyT': // Alt + T: تبديل الثيم
                        e.preventDefault();
                        document.querySelector('[data-toggle="theme"]')?.click();
                        break;

                    case 'KeyL': // Alt + L: تبديل اللغة
                        e.preventDefault();
                        document.querySelector('.language-switch')?.click();
                        break;

                    case 'KeyS': // Alt + S: تصغير/توسيع السايدبار
                        e.preventDefault();
                        document.getElementById('sidebarToggle')?.click();
                        break;

                    case 'KeyC': // Alt + C: تغيير عرض المحتوى
                        e.preventDefault();
                        document.querySelector('[data-toggle="content-mode"]')?.click();
                        break;

                    case 'KeyH': // Alt + H: الصفحة الرئيسية
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/home';
                        break;

                    case 'KeyD': // Alt + D: لوحة التحكم (Dashboard)
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/dashboard';
                        break;

                    case 'KeyP': // Alt + P: الملف الشخصي
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/profile';
                        break;

                    case 'KeyN': // Alt + N: الإشعارات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/notifications';
                        break;

                    case 'KeyM': // Alt + M: الرسائل
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/chat';
                        break;

                    case 'KeyI': // Alt + I: الإعدادات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/settings';
                        break;

                    case 'ArrowUp': // Alt + ↑: العودة لأعلى الصفحة
                        e.preventDefault();
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        break;
                }
            }
        });

        console.log('✅ تم تهيئة اختصارات لوحة المفاتيح');
    }

    // ===== نظام القوائم المنسدلة للتوب بار ===== //
    function initTopbarDropdowns() {
        // البحث عن قوائم التوب بار فقط
        const topbarDropdowns = document.querySelectorAll('.topbar-user[data-toggle="topbar-dropdown"]');

        topbarDropdowns.forEach(function(trigger) {
            const dropdown = trigger.closest('.topbar-dropdown');
            const menu = dropdown.querySelector('.topbar-dropdown-menu');

            if (!menu) return;

            // إضافة event listener للنقر
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // إغلاق جميع قوائم التوب بار الأخرى
                closeAllTopbarDropdowns();

                // تبديل حالة القائمة الحالية
                toggleTopbarDropdown(dropdown, menu, trigger);
            });

            // إضافة event listener للوحة المفاتيح
            trigger.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    trigger.click();
                }
            });
        });

        // إغلاق قوائم التوب بار عند النقر خارجها
        document.addEventListener('click', function(e) {
            const clickedTopbarDropdown = e.target.closest('.topbar-user');
            if (!clickedTopbarDropdown) {
                closeAllTopbarDropdowns();
            }
        });

        // إغلاق قوائم التوب بار عند الضغط على Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeAllTopbarDropdowns();
            }
        });

        console.log('✅ تم تهيئة قوائم التوب بار المنسدلة');
    }

    // وظائف مساعدة لقوائم التوب بار
    function toggleTopbarDropdown(dropdown, menu, trigger) {
        const isOpen = dropdown.classList.contains('show');

        if (isOpen) {
            closeTopbarDropdown(dropdown, menu, trigger);
        } else {
            openTopbarDropdown(dropdown, menu, trigger);
        }
    }

    function openTopbarDropdown(dropdown, menu, trigger) {
        // إضافة class للعرض
        dropdown.classList.add('show');
        menu.classList.add('show');

        // تحديث aria attributes
        trigger.setAttribute('aria-expanded', 'true');

        // تحديث السهم
        const arrow = trigger.querySelector('.topbar-user-dropdown');
        if (arrow) {
            arrow.classList.remove('fa-chevron-down');
            arrow.classList.add('fa-chevron-up');
        }

        // إضافة animation
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px)';

        requestAnimationFrame(function() {
            menu.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
            menu.style.opacity = '1';
            menu.style.transform = 'translateY(0)';
        });

        // إضافة تأثيرات للعناصر
        const items = menu.querySelectorAll('.topbar-dropdown-item');
        items.forEach(function(item, index) {
            // إضافة تأخير للظهور التدريجي
            item.style.transitionDelay = (index * 0.03) + 's';

            // إضافة معالج النقر لإغلاق القائمة
            item.addEventListener('click', function() {
                setTimeout(function() {
                    closeTopbarDropdown(dropdown, menu, trigger);
                }, 100);
            });
        });
    }

    function closeTopbarDropdown(dropdown, menu, trigger) {
        // إزالة classes
        dropdown.classList.remove('show');
        menu.classList.remove('show');

        // تحديث aria attributes
        trigger.setAttribute('aria-expanded', 'false');

        // تحديث السهم
        const arrow = trigger.querySelector('.topbar-user-dropdown');
        if (arrow) {
            arrow.classList.remove('fa-chevron-up');
            arrow.classList.add('fa-chevron-down');
        }

        // إزالة animation styles
        menu.style.transition = '';
        menu.style.opacity = '';
        menu.style.transform = '';
    }

    function closeAllTopbarDropdowns() {
        const openTopbarDropdowns = document.querySelectorAll('.topbar-user[data-toggle="topbar-dropdown"]');

        openTopbarDropdowns.forEach(function(trigger) {
            const dropdown = trigger.closest('.topbar-dropdown');
            const menu = dropdown.querySelector('.topbar-dropdown-menu');

            if (dropdown.classList.contains('show') && menu && trigger) {
                closeTopbarDropdown(dropdown, menu, trigger);
            }
        });
    }

    // ===== نظام الإشعارات ===== //
    function initNotificationsSystem() {
        const notificationsBtn = document.getElementById('notifications-btn');
        
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الإشعارات هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/notifications';
            });
        }

        console.log('✅ تم تهيئة نظام الإشعارات');
    }

    // ===== نظام الرسائل ===== //
    function initMessagesSystem() {
        const messagesBtn = document.getElementById('messages-btn');
        
        if (messagesBtn) {
            messagesBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الرسائل هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/chat';
            });
        }

        console.log('✅ تم تهيئة نظام الرسائل');
    }

    // ===== نظام الإعدادات السريعة ===== //
    function initQuickSettingsSystem() {
        const quickSettingsBtn = document.getElementById('quick-settings-btn');
        
        if (quickSettingsBtn) {
            quickSettingsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الإعدادات السريعة هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/settings';
            });
        }

        console.log('✅ تم تهيئة نظام الإعدادات السريعة');
    }

    // ===== إجراءات التوب بار العامة ===== //
    function initTopbarActions() {
        // إضافة تأثيرات hover للأزرار
        const topbarActions = document.querySelectorAll('.topbar-action');
        
        topbarActions.forEach(action => {
            action.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            
            action.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        console.log('✅ تم تهيئة إجراءات التوب بار العامة');
    }

})();
