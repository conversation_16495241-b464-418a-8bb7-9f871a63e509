/**
 * نظام الشريط العلوي - وظائف إدارة التوب بار
 * Topbar System - Topbar management functions
 */

(function() {
    'use strict';

    // انتظار تحميل DOM
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTopbarApp);
    } else {
        initTopbarApp();
    }

    function initTopbarApp() {
        console.log('🚀 بدء تهيئة نظام التوب بار...');

        // تهيئة جميع مكونات التوب بار
        initKeyboardShortcuts();
        initDropdownSystem();
        initNotificationsSystem();
        initMessagesSystem();
        initQuickSettingsSystem();
        initTopbarActions();

        console.log('✅ تم تهيئة نظام التوب بار بنجاح');
    }

    // تصدير الوظائف للاستخدام الخارجي (اختياري)
    window.TopbarSystem = {
        init: initTopbarSystem,
        initKeyboardShortcuts: initKeyboardShortcuts,
        initDropdowns: initDropdownSystem,
        initNotifications: initNotificationsSystem,
        initMessages: initMessagesSystem,
        initQuickSettings: initQuickSettingsSystem
    };

    // ===== تهيئة نظام التوب بار الرئيسي ===== //
    function initTopbarSystem() {
        // تهيئة جميع مكونات التوب بار
        initKeyboardShortcuts();
        initDropdownSystem();
        initNotificationsSystem();
        initMessagesSystem();
        initQuickSettingsSystem();
        initTopbarActions();
    }

    // ===== اختصارات لوحة المفاتيح ===== //
    function initKeyboardShortcuts() {
        const shortcutsModal = document.getElementById('keyboard-shortcuts-modal');
        const shortcutsBtn = document.getElementById('keyboard-shortcuts-btn');
        const closeShortcutsBtn = document.getElementById('close-keyboard-shortcuts');

        // فتح مودال الاختصارات
        if (shortcutsBtn && shortcutsModal) {
            shortcutsBtn.addEventListener('click', () => {
                shortcutsModal.classList.add('show');
            });
        }

        // إغلاق مودال الاختصارات
        if (closeShortcutsBtn && shortcutsModal) {
            closeShortcutsBtn.addEventListener('click', () => shortcutsModal.classList.remove('show'));
            shortcutsModal.addEventListener('click', e => {
                if (e.target === shortcutsModal) shortcutsModal.classList.remove('show');
            });
        }

        // دالة موحدة للتعامل مع ضغطات المفاتيح
        document.addEventListener('keydown', function(e) {
            const tag = e.target.tagName;
            const editable = e.target.isContentEditable;

            // 1) إغلاق المودال بالـ Escape
            if (e.key === 'Escape' && shortcutsModal && shortcutsModal.classList.contains('show')) {
                shortcutsModal.classList.remove('show');
                return;
            }

            // 2) فتح/إغلاق المودال بالـ '?' (Shift + '/')
            if (!e.altKey && !e.ctrlKey && !e.metaKey && e.key === '?') {
                e.preventDefault();
                if (shortcutsModal) {
                    shortcutsModal.classList.toggle('show');
                }
                return;
            }

            // 3) بقية الاختصارات (تجاهل الحقول القابلة للتعديل)
            if (tag === 'INPUT' || tag === 'TEXTAREA' || editable) return;

            // 4) اختصارات Alt + مفتاح (باستخدام e.code للغة مستقلّة)
            if (e.altKey && !e.ctrlKey && !e.metaKey) {
                switch (e.code) {
                    case 'KeyT': // Alt + T: تبديل الثيم
                        e.preventDefault();
                        document.querySelector('[data-toggle="theme"]')?.click();
                        break;

                    case 'KeyL': // Alt + L: تبديل اللغة
                        e.preventDefault();
                        document.querySelector('.language-switch')?.click();
                        break;

                    case 'KeyS': // Alt + S: تصغير/توسيع السايدبار
                        e.preventDefault();
                        document.getElementById('sidebarToggle')?.click();
                        break;

                    case 'KeyC': // Alt + C: تغيير عرض المحتوى
                        e.preventDefault();
                        document.querySelector('[data-toggle="content-mode"]')?.click();
                        break;

                    case 'KeyH': // Alt + H: الصفحة الرئيسية
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/home';
                        break;

                    case 'KeyD': // Alt + D: لوحة التحكم (Dashboard)
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/dashboard';
                        break;

                    case 'KeyP': // Alt + P: الملف الشخصي
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/profile';
                        break;

                    case 'KeyN': // Alt + N: الإشعارات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/notifications';
                        break;

                    case 'KeyM': // Alt + M: الرسائل
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/chat';
                        break;

                    case 'KeyI': // Alt + I: الإعدادات
                        e.preventDefault();
                        window.location.href = window.APP_CONFIG.APP_URL + '/settings';
                        break;

                    case 'ArrowUp': // Alt + ↑: العودة لأعلى الصفحة
                        e.preventDefault();
                        window.scrollTo({ top: 0, behavior: 'smooth' });
                        break;
                }
            }
        });

        console.log('✅ تم تهيئة اختصارات لوحة المفاتيح');
    }

    // ===== نظام القوائم المنسدلة ===== //
    function initDropdownSystem() {
        // تفعيل القوائم المنسدلة بشكل مخصص
        const dropdowns = document.querySelectorAll('.dropdown');

        dropdowns.forEach(function(dropdown) {
            const toggle = dropdown.querySelector('.topbar-user');
            const menu = dropdown.querySelector('.dropdown-menu');

            if (toggle && menu) {
                // إضافة معالج النقر
                toggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const isCurrentlyOpen = dropdown.classList.contains('show');

                    // إغلاق جميع القوائم المنسدلة الأخرى
                    dropdowns.forEach(function(otherDropdown) {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('show')) {
                            const otherToggle = otherDropdown.querySelector('.topbar-user');
                            otherDropdown.classList.remove('show');
                            if (otherToggle) otherToggle.setAttribute('aria-expanded', 'false');
                        }
                    });

                    // تبديل حالة القائمة المنسدلة الحالية
                    if (isCurrentlyOpen) {
                        dropdown.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                    } else {
                        dropdown.classList.add('show');
                        toggle.setAttribute('aria-expanded', 'true');
                    }
                });

                // إضافة تأثيرات للعناصر
                const items = menu.querySelectorAll('.dropdown-item');
                items.forEach(function(item, index) {
                    // إضافة تأخير للظهور التدريجي
                    item.style.transitionDelay = (index * 0.03) + 's';

                    // إضافة معالج النقر
                    item.addEventListener('click', function() {
                        // إغلاق القائمة المنسدلة بعد النقر
                        setTimeout(function() {
                            dropdown.classList.remove('show');
                            toggle.setAttribute('aria-expanded', 'false');
                        }, 100);
                    });
                });
            }
        });

        // إغلاق القائمة المنسدلة عند النقر خارجها
        document.addEventListener('click', function(e) {
            dropdowns.forEach(function(dropdown) {
                if (!dropdown.contains(e.target) && dropdown.classList.contains('show')) {
                    const toggle = dropdown.querySelector('.topbar-user');
                    dropdown.classList.remove('show');
                    if (toggle) toggle.setAttribute('aria-expanded', 'false');
                }
            });
        });

        console.log('✅ تم تهيئة نظام القوائم المنسدلة');
    }

    // ===== نظام الإشعارات ===== //
    function initNotificationsSystem() {
        const notificationsBtn = document.getElementById('notifications-btn');
        
        if (notificationsBtn) {
            notificationsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الإشعارات هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/notifications';
            });
        }

        console.log('✅ تم تهيئة نظام الإشعارات');
    }

    // ===== نظام الرسائل ===== //
    function initMessagesSystem() {
        const messagesBtn = document.getElementById('messages-btn');
        
        if (messagesBtn) {
            messagesBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الرسائل هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/chat';
            });
        }

        console.log('✅ تم تهيئة نظام الرسائل');
    }

    // ===== نظام الإعدادات السريعة ===== //
    function initQuickSettingsSystem() {
        const quickSettingsBtn = document.getElementById('quick-settings-btn');
        
        if (quickSettingsBtn) {
            quickSettingsBtn.addEventListener('click', function(e) {
                e.preventDefault();
                // يمكن إضافة منطق فتح نافذة الإعدادات السريعة هنا
                window.location.href = window.APP_CONFIG.APP_URL + '/settings';
            });
        }

        console.log('✅ تم تهيئة نظام الإعدادات السريعة');
    }

    // ===== إجراءات التوب بار العامة ===== //
    function initTopbarActions() {
        // إضافة تأثيرات hover للأزرار
        const topbarActions = document.querySelectorAll('.topbar-action');
        
        topbarActions.forEach(action => {
            action.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.05)';
            });
            
            action.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
            });
        });

        console.log('✅ تم تهيئة إجراءات التوب بار العامة');
    }

})();
