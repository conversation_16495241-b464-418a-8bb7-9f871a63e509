<?php

namespace App\Controllers\Purchases;

use App\Modules\Purchases\Models\Supplier;

class SuppliersImportExportController
{
    protected $params = [];
    protected $supplierModel;

    public function __construct($params = [])
    {
        $this->params = $params;
        $this->supplierModel = new Supplier();

        // التحقق من تسجيل الدخول
        if (!is_logged_in()) {
            if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'غير مصرح']);
                exit;
            } else {
                redirect(base_url('login'));
            }
        }
    }
    
    /**
     * تصدير الموردين إلى Excel
     */
    public function exportExcel()
    {
        try {
            $company_id = current_user()['current_company_id'];

            // الحصول على البيانات
            $suppliers = $this->supplierModel->getByCompany($company_id, []);
            
            // تحديد الأعمدة للتصدير
            $columns = [
                ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'type' => 'text'],
                ['field' => 'S_contact_person', 'title' => 'الشخص المسؤول', 'type' => 'text'],
                ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'type' => 'email'],
                ['field' => 'G_phone', 'title' => 'رقم الهاتف', 'type' => 'text'],
                ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'type' => 'text'],
                ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'type' => 'text'],
                ['field' => 'S_credit_limit', 'title' => 'الحد الائتماني', 'type' => 'currency'],
                ['field' => 'S_payment_terms', 'title' => 'شروط الدفع', 'type' => 'text'],
                ['field' => 'G_status', 'title' => 'الحالة', 'type' => 'text'],
                ['field' => 'created_at', 'title' => 'تاريخ الإنشاء', 'type' => 'date']
            ];
            
            // تصدير البيانات
            export_to_excel(
                $suppliers, 
                $columns, 
                'suppliers_' . date('Y-m-d'), 
                'قائمة الموردين'
            );
            
        } catch (\Exception $e) {
            flash('error', 'خطأ في تصدير البيانات: ' . $e->getMessage());
            redirect('/purchases/suppliers');
        }
    }
    
    /**
     * تصدير الموردين إلى CSV
     */
    public function exportCsv()
    {
        try {
            $company_id = current_user()['current_company_id'];

            // الحصول على البيانات
            $suppliers = $this->supplierModel->getByCompany($company_id, []);

            // تحديد الأعمدة للتصدير
            $columns = [
                ['field' => 'G_name_ar', 'title' => 'اسم المورد'],
                ['field' => 'S_contact_person', 'title' => 'الشخص المسؤول'],
                ['field' => 'S_email', 'title' => 'البريد الإلكتروني'],
                ['field' => 'G_phone', 'title' => 'رقم الهاتف'],
                ['field' => 'S_company_name', 'title' => 'اسم الشركة'],
                ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي'],
                ['field' => 'S_credit_limit', 'title' => 'الحد الائتماني'],
                ['field' => 'S_payment_terms', 'title' => 'شروط الدفع'],
                ['field' => 'G_status', 'title' => 'الحالة']
            ];
            
            // تصدير البيانات
            export_to_csv(
                $suppliers, 
                $columns, 
                'suppliers_' . date('Y-m-d')
            );
            
        } catch (\Exception $e) {
            flash('error', 'خطأ في تصدير البيانات: ' . $e->getMessage());
            redirect('/purchases/suppliers');
        }
    }
    
    /**
     * تحميل قالب الاستيراد
     */
    public function downloadTemplate()
    {
        try {
            // تحديد أعمدة القالب
            $columns = [
                ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true],
                ['field' => 'S_contact_person', 'title' => 'الشخص المسؤول', 'required' => false],
                ['field' => 'S_email', 'title' => 'البريد الإلكتروني', 'required' => false],
                ['field' => 'G_phone', 'title' => 'رقم الهاتف', 'required' => false],
                ['field' => 'S_company_name', 'title' => 'اسم الشركة', 'required' => false],
                ['field' => 'S_tax_number', 'title' => 'الرقم الضريبي', 'required' => false],
                ['field' => 'S_credit_limit', 'title' => 'الحد الائتماني', 'required' => false],
                ['field' => 'S_payment_terms', 'title' => 'شروط الدفع', 'required' => false],
                ['field' => 'G_status', 'title' => 'الحالة', 'required' => false]
            ];
            
            // إنشاء القالب
            create_import_template(
                $columns, 
                'suppliers_import_template', 
                'قالب استيراد الموردين'
            );
            
        } catch (\Exception $e) {
            flash('error', 'خطأ في إنشاء القالب: ' . $e->getMessage());
            redirect('/purchases/suppliers');
        }
    }
    
    /**
     * معاينة ملف الاستيراد
     */
    public function importPreview()
    {
        try {
            $file = $_FILES['import_file'] ?? null;

            if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'لم يتم رفع ملف صحيح. كود الخطأ: ' . ($file['error'] ?? 'غير محدد')
                ]);
                exit;
            }

            // التحقق من نوع الملف
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['csv']; // نركز على CSV حالياً

            if (!in_array($file_extension, $allowed_extensions)) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'نوع الملف غير مدعوم حالياً. يرجى استخدام ملفات CSV (.csv) فقط'
                ]);
                exit;
            }

            // التحقق من حجم الملف (5MB كحد أقصى)
            if ($file['size'] > 5 * 1024 * 1024) {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => 'حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت'
                ]);
                exit;
            }
            
            // نقل الملف إلى مجلد مؤقت
            $tempPath = BASE_PATH . '/storage/uploads/temp/';
            if (!is_dir($tempPath)) {
                mkdir($tempPath, 0755, true);
            }

            $tempFile = $tempPath . uniqid() . '_' . $file['name'];
            move_uploaded_file($file['tmp_name'], $tempFile);
            
            // قراءة الملف
            $result = read_import_file($tempFile);

            // حذف الملف المؤقت
            if (file_exists($tempFile)) {
                unlink($tempFile);
            }

            if ($result['success']) {
                // معاينة البيانات
                $preview = preview_import_data($result['data'], 10);

                header('Content-Type: application/json');
                echo json_encode([
                    'success' => true,
                    'headers' => $result['headers'],
                    'data' => $result['data'],
                    'total_rows' => $result['total_rows'],
                    'preview' => $preview
                ]);
                exit;
            } else {
                header('Content-Type: application/json');
                echo json_encode([
                    'success' => false,
                    'error' => $result['error']
                ]);
                exit;
            }
            
        } catch (\Exception $e) {
            // تنظيف الملف المؤقت في حالة الخطأ
            if (isset($tempFile) && file_exists($tempFile)) {
                unlink($tempFile);
            }

            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في معاينة الملف: ' . $e->getMessage(),
                'debug_info' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ]);
            exit;
        }
    }
    
    /**
     * الحصول على أعمدة الجدول
     */
    public function getTableColumns()
    {
        try {
            $columns = [
                ['field' => 'supplier_name', 'title' => 'اسم المورد', 'required' => true],
                ['field' => 'contact_person', 'title' => 'الشخص المسؤول', 'required' => false],
                ['field' => 'email', 'title' => 'البريد الإلكتروني', 'required' => false],
                ['field' => 'phone', 'title' => 'رقم الهاتف', 'required' => false],
                ['field' => 'address', 'title' => 'العنوان', 'required' => false],
                ['field' => 'tax_number', 'title' => 'الرقم الضريبي', 'required' => false],
                ['field' => 'credit_limit', 'title' => 'الحد الائتماني', 'required' => false],
                ['field' => 'payment_terms', 'title' => 'شروط الدفع', 'required' => false],
                ['field' => 'status', 'title' => 'الحالة', 'required' => false]
            ];
            
            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'columns' => $columns
            ]);
            exit;
            
        } catch (\Exception $e) {
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'خطأ في تحميل أعمدة الجدول: ' . $e->getMessage()
            ]);
            exit;
        }
    }
    
    /**
     * استيراد البيانات
     */
    public function import()
    {
        try {
            $file = $_FILES['import_file'] ?? null;
            $columnMapping = $_POST['column_mapping'] ?? [];
            
            if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
                flash('error', 'لم يتم رفع ملف صحيح');
                redirect('/purchases/suppliers');
                return;
            }

            // نقل الملف إلى مجلد مؤقت
            $tempPath = BASE_PATH . '/storage/uploads/temp/';
            if (!is_dir($tempPath)) {
                mkdir($tempPath, 0755, true);
            }

            $tempFile = $tempPath . uniqid() . '_' . $file['name'];
            move_uploaded_file($file['tmp_name'], $tempFile);
            
            // قراءة الملف
            $result = read_import_file($tempFile);
            
            // حذف الملف المؤقت
            unlink($tempFile);
            
            if (!$result['success']) {
                flash('error', 'خطأ في قراءة الملف: ' . $result['error']);
                redirect('/purchases/suppliers');
                return;
            }
            
            // تطبيق تطابق الأعمدة
            $mappedData = [];
            foreach ($result['data'] as $row) {
                $mappedRow = [];
                foreach ($columnMapping as $sourceField => $targetField) {
                    if (!empty($targetField) && isset($row[$sourceField])) {
                        $mappedRow[$targetField] = $row[$sourceField];
                    }
                }
                if (!empty($mappedRow)) {
                    $mappedData[] = $mappedRow;
                }
            }
            
            // التحقق من صحة البيانات
            $validationRules = [
                'supplier_name' => ['required' => true],
                'email' => ['type' => 'email']
            ];
            
            $validation = validate_import_data($mappedData, $columnMapping, $validationRules);
            
            if (!empty($validation['errors'])) {
                flash('error', 'أخطاء في البيانات: ' . implode(', ', $validation['errors']));
                redirect('/purchases/suppliers');
                return;
            }
            
            // حفظ البيانات
            $saveResult = save_imported_data(
                $this->supplierModel,
                $validation['valid_data'],
                current_user()['current_company_id'],
                current_user()['id']
            );

            $message = "تم استيراد {$saveResult['success_count']} مورد بنجاح";
            if ($saveResult['error_count'] > 0) {
                $message .= " مع {$saveResult['error_count']} أخطاء";
            }

            flash('success', $message);
            redirect('/purchases/suppliers');
            
        } catch (\Exception $e) {
            flash('error', 'خطأ في الاستيراد: ' . $e->getMessage());
            redirect('/purchases/suppliers');
        }
    }
}
