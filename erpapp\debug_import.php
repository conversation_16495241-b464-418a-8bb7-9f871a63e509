<?php
/**
 * ملف تشخيص مشاكل الاستيراد
 */

// تحميل النظام
require_once __DIR__ . '/loader.php';

echo "<h1>🔍 تشخيص مشاكل الاستيراد</h1>";

// فحص المتطلبات الأساسية
echo "<h2>📋 فحص المتطلبات</h2>";

// فحص مجلد vendor
$vendor_path = BASE_PATH . '/vendor/autoload.php';
echo "<p><strong>مسار vendor:</strong> " . $vendor_path . "</p>";
if (file_exists($vendor_path)) {
    echo "✅ مجلد vendor موجود<br>";
    require_once $vendor_path;
    
    // فحص PhpSpreadsheet
    if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        echo "✅ مكتبة PhpSpreadsheet محملة<br>";
        
        // فحص IOFactory
        if (class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
            echo "✅ IOFactory متاح<br>";
        } else {
            echo "❌ IOFactory غير متاح<br>";
        }
    } else {
        echo "❌ مكتبة PhpSpreadsheet غير محملة<br>";
    }
} else {
    echo "❌ مجلد vendor غير موجود<br>";
}

// فحص مجلد التخزين المؤقت
$temp_path = BASE_PATH . '/storage/uploads/temp';
echo "<p><strong>مجلد التخزين المؤقت:</strong> " . $temp_path . "</p>";
if (is_dir($temp_path)) {
    echo "✅ مجلد التخزين موجود<br>";
    if (is_writable($temp_path)) {
        echo "✅ مجلد التخزين قابل للكتابة<br>";
    } else {
        echo "❌ مجلد التخزين غير قابل للكتابة<br>";
    }
} else {
    echo "❌ مجلد التخزين غير موجود<br>";
}

// فحص دالة import_export_helper
echo "<h2>🔧 فحص الدوال</h2>";
if (function_exists('read_import_file')) {
    echo "✅ دالة read_import_file متاحة<br>";
} else {
    echo "❌ دالة read_import_file غير متاحة<br>";
}

// إنشاء ملف Excel تجريبي
echo "<h2>📄 إنشاء ملف تجريبي</h2>";

try {
    if (class_exists('\PhpOffice\PhpSpreadsheet\Spreadsheet')) {
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // إضافة رؤوس الأعمدة
        $sheet->setCellValue('A1', 'اسم المورد');
        $sheet->setCellValue('B1', 'البريد الإلكتروني');
        $sheet->setCellValue('C1', 'رقم الهاتف');
        
        // إضافة بيانات تجريبية
        $sheet->setCellValue('A2', 'مورد تجريبي 1');
        $sheet->setCellValue('B2', '<EMAIL>');
        $sheet->setCellValue('C2', '123456789');
        
        $sheet->setCellValue('A3', 'مورد تجريبي 2');
        $sheet->setCellValue('B3', '<EMAIL>');
        $sheet->setCellValue('C3', '987654321');
        
        // حفظ الملف
        $test_file = $temp_path . '/test_suppliers.xlsx';
        $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
        $writer->save($test_file);
        
        echo "✅ تم إنشاء ملف تجريبي: " . $test_file . "<br>";
        
        // اختبار قراءة الملف
        echo "<h2>📖 اختبار قراءة الملف</h2>";
        
        if (function_exists('read_import_file')) {
            $result = read_import_file($test_file);
            
            if ($result['success']) {
                echo "✅ تم قراءة الملف بنجاح<br>";
                echo "<strong>عدد الرؤوس:</strong> " . count($result['headers']) . "<br>";
                echo "<strong>عدد الصفوف:</strong> " . $result['total_rows'] . "<br>";
                
                echo "<h3>رؤوس الأعمدة:</h3>";
                echo "<ul>";
                foreach ($result['headers'] as $header) {
                    echo "<li>" . htmlspecialchars($header) . "</li>";
                }
                echo "</ul>";
                
                echo "<h3>البيانات:</h3>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr>";
                foreach ($result['headers'] as $header) {
                    echo "<th style='padding: 8px; background: #f0f0f0;'>" . htmlspecialchars($header) . "</th>";
                }
                echo "</tr>";
                
                foreach ($result['data'] as $row) {
                    echo "<tr>";
                    foreach ($result['headers'] as $header) {
                        echo "<td style='padding: 8px; border: 1px solid #ddd;'>" . htmlspecialchars($row[$header] ?? '') . "</td>";
                    }
                    echo "</tr>";
                }
                echo "</table>";
                
            } else {
                echo "❌ فشل في قراءة الملف: " . $result['error'] . "<br>";
            }
        }
        
        // تنظيف الملف التجريبي
        if (file_exists($test_file)) {
            unlink($test_file);
            echo "<br>🗑️ تم حذف الملف التجريبي<br>";
        }
        
    } else {
        echo "❌ لا يمكن إنشاء ملف تجريبي - PhpSpreadsheet غير متاح<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في إنشاء الملف التجريبي: " . $e->getMessage() . "<br>";
}

// فحص إعدادات PHP
echo "<h2>⚙️ إعدادات PHP</h2>";
echo "<p><strong>إصدار PHP:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>حد الذاكرة:</strong> " . ini_get('memory_limit') . "</p>";
echo "<p><strong>حد رفع الملفات:</strong> " . ini_get('upload_max_filesize') . "</p>";
echo "<p><strong>حد POST:</strong> " . ini_get('post_max_size') . "</p>";
echo "<p><strong>مجلد مؤقت:</strong> " . sys_get_temp_dir() . "</p>";

// فحص الامتدادات المطلوبة
echo "<h2>🔌 الامتدادات المطلوبة</h2>";
$required_extensions = ['zip', 'xml', 'gd', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "✅ امتداد {$ext} محمل<br>";
    } else {
        echo "❌ امتداد {$ext} غير محمل<br>";
    }
}

echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>💡 نصائح لحل المشاكل:</h3>";
echo "<ol>";
echo "<li>تأكد من تثبيت PhpSpreadsheet: <code>composer require phpoffice/phpspreadsheet</code></li>";
echo "<li>تأكد من صلاحيات مجلد التخزين: <code>chmod 755 storage/uploads/temp</code></li>";
echo "<li>تأكد من تحميل الامتدادات المطلوبة في PHP</li>";
echo "<li>تحقق من حدود رفع الملفات في php.ini</li>";
echo "<li>تأكد من أن الملف المرفوع صحيح وغير تالف</li>";
echo "</ol>";
echo "</div>";
?>
