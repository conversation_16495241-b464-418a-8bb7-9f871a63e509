/**
 * ERP Select - مكتبة Select متقدمة ومخصصة
 * مكتبة احترافية مصممة خصيص<|im_start|> لأنظمة ERP
 * 
 * @version 1.0.0
 * <AUTHOR> Team
 * @license MIT
 */

(function(window, document) {
    'use strict';

    // إعدادات افتراضية
    const DEFAULT_OPTIONS = {
        // الأساسيات
        placeholder: 'اختر...',
        searchPlaceholder: 'ابحث...',
        noResultsText: 'لا توجد نتائج',
        loadingText: 'جاري التحميل...',
        clearText: 'مسح الكل',
        
        // الوظائف
        searchable: true,
        clearable: true,
        multiple: false,
        disabled: false,
        
        // التصميم
        theme: 'default',
        size: 'medium', // small, medium, large
        rtl: true,
        
        // البحث والفلترة
        searchMinLength: 0,
        searchDelay: 300,
        maxResults: 100,
        
        // AJAX
        ajax: null,
        
        // الأحداث
        onChange: null,
        onOpen: null,
        onClose: null,
        onSearch: null,
        onClear: null,
        
        // متقدم
        allowCreate: false,
        createText: 'إضافة "{value}"',
        groupBy: null,
        sortBy: null,
        
        // الأداء
        virtualScroll: false,
        lazyLoad: false,
        cacheResults: true
    };

    // كلاس ERP Select الرئيسي
    class ERPSelect {
        constructor(element, options = {}) {
            // التحقق من صحة العنصر
            if (!element || element.tagName !== 'SELECT') {
                throw new Error('ERP Select: يجب تمرير عنصر select صحيح');
            }

            this.originalSelect = element;
            this.options = { ...DEFAULT_OPTIONS, ...options };
            this.isOpen = false;
            this.isDisabled = this.options.disabled;
            this.selectedValues = [];
            this.filteredOptions = [];
            this.searchTerm = '';
            this.cache = new Map();
            
            // عناصر DOM
            this.container = null;
            this.trigger = null;
            this.dropdown = null;
            this.searchInput = null;
            this.optionsList = null;
            this.clearButton = null;
            
            // معرفات فريدة
            this.id = 'erp-select-' + Math.random().toString(36).substr(2, 9);
            this.searchTimeout = null;
            
            this.init();
        }

        init() {
            try {
                this.parseOriginalOptions();
                this.createStructure();
                this.bindEvents();
                this.updateSelectedValues();
                this.hide();
                
                // إضافة إلى السجل العام
                ERPSelect.instances.set(this.originalSelect, this);
                
                console.log('✅ تم تهيئة ERP Select بنجاح:', this.id);
            } catch (error) {
                console.error('❌ خطأ في تهيئة ERP Select:', error);
                throw error;
            }
        }

        parseOriginalOptions() {
            this.originalOptions = [];
            const options = this.originalSelect.querySelectorAll('option');
            
            options.forEach(option => {
                if (option.value === '') return; // تجاهل الخيار الفارغ
                
                this.originalOptions.push({
                    value: option.value,
                    text: option.textContent.trim(),
                    selected: option.selected,
                    disabled: option.disabled,
                    data: { ...option.dataset }
                });
            });
            
            this.filteredOptions = [...this.originalOptions];
        }

        createStructure() {
            // إنشاء الحاوي الرئيسي
            this.container = document.createElement('div');
            this.container.className = this.getContainerClasses();
            this.container.id = this.id;
            
            // إنشاء الزر المحفز
            this.createTrigger();
            
            // إنشاء القائمة المنسدلة
            this.createDropdown();
            
            // إدراج في DOM
            this.originalSelect.parentNode.insertBefore(this.container, this.originalSelect);
            this.originalSelect.style.display = 'none';
            
            // إضافة الأنماط المخصصة
            this.addCustomStyles();
        }

        getContainerClasses() {
            const classes = ['erp-select'];
            
            if (this.options.theme !== 'default') {
                classes.push(`erp-select--${this.options.theme}`);
            }
            
            if (this.options.size !== 'medium') {
                classes.push(`erp-select--${this.options.size}`);
            }
            
            if (this.options.rtl) {
                classes.push('erp-select--rtl');
            }
            
            if (this.options.multiple) {
                classes.push('erp-select--multiple');
            }
            
            if (this.isDisabled) {
                classes.push('erp-select--disabled');
            }
            
            return classes.join(' ');
        }

        createTrigger() {
            this.trigger = document.createElement('div');
            this.trigger.className = 'erp-select__trigger';
            this.trigger.setAttribute('tabindex', this.isDisabled ? '-1' : '0');
            this.trigger.setAttribute('role', 'combobox');
            this.trigger.setAttribute('aria-expanded', 'false');
            this.trigger.setAttribute('aria-haspopup', 'listbox');
            
            // النص المعروض
            const display = document.createElement('span');
            display.className = 'erp-select__display';
            display.textContent = this.options.placeholder;
            
            // أيقونة السهم
            const arrow = document.createElement('span');
            arrow.className = 'erp-select__arrow';
            arrow.innerHTML = this.getArrowIcon();
            
            // زر المسح (إذا كان مفعل)
            if (this.options.clearable) {
                this.clearButton = document.createElement('button');
                this.clearButton.className = 'erp-select__clear';
                this.clearButton.type = 'button';
                this.clearButton.innerHTML = this.getClearIcon();
                this.clearButton.style.display = 'none';
                this.trigger.appendChild(this.clearButton);
            }
            
            this.trigger.appendChild(display);
            this.trigger.appendChild(arrow);
            this.container.appendChild(this.trigger);
        }

        createDropdown() {
            this.dropdown = document.createElement('div');
            this.dropdown.className = 'erp-select__dropdown';
            this.dropdown.setAttribute('role', 'listbox');
            
            // حقل البحث (إذا كان مفعل)
            if (this.options.searchable) {
                this.createSearchInput();
            }
            
            // قائمة الخيارات
            this.optionsList = document.createElement('div');
            this.optionsList.className = 'erp-select__options';
            
            this.dropdown.appendChild(this.optionsList);
            this.container.appendChild(this.dropdown);
            
            // تحديث الخيارات
            this.updateOptions();
        }

        createSearchInput() {
            const searchContainer = document.createElement('div');
            searchContainer.className = 'erp-select__search';
            
            this.searchInput = document.createElement('input');
            this.searchInput.type = 'text';
            this.searchInput.className = 'erp-select__search-input';
            this.searchInput.placeholder = this.options.searchPlaceholder;
            this.searchInput.autocomplete = 'off';
            
            const searchIcon = document.createElement('span');
            searchIcon.className = 'erp-select__search-icon';
            searchIcon.innerHTML = this.getSearchIcon();
            
            searchContainer.appendChild(searchIcon);
            searchContainer.appendChild(this.searchInput);
            this.dropdown.appendChild(searchContainer);
        }

        updateOptions() {
            this.optionsList.innerHTML = '';
            
            if (this.filteredOptions.length === 0) {
                this.showNoResults();
                return;
            }
            
            // تجميع الخيارات (إذا كان مفعل)
            if (this.options.groupBy) {
                this.renderGroupedOptions();
            } else {
                this.renderFlatOptions();
            }
        }

        renderFlatOptions() {
            this.filteredOptions.forEach((option, index) => {
                const optionElement = this.createOptionElement(option, index);
                this.optionsList.appendChild(optionElement);
            });
        }

        createOptionElement(option, index) {
            const element = document.createElement('div');
            element.className = 'erp-select__option';
            element.setAttribute('role', 'option');
            element.setAttribute('data-value', option.value);
            element.setAttribute('data-index', index);
            
            if (option.disabled) {
                element.classList.add('erp-select__option--disabled');
                element.setAttribute('aria-disabled', 'true');
            }
            
            if (this.isSelected(option.value)) {
                element.classList.add('erp-select__option--selected');
                element.setAttribute('aria-selected', 'true');
            }
            
            // محتوى الخيار
            const content = document.createElement('span');
            content.className = 'erp-select__option-text';
            content.textContent = option.text;
            
            // أيقونة التحديد (للخيارات المتعددة)
            if (this.options.multiple) {
                const checkbox = document.createElement('span');
                checkbox.className = 'erp-select__option-checkbox';
                checkbox.innerHTML = this.getCheckboxIcon(this.isSelected(option.value));
                element.appendChild(checkbox);
            }
            
            element.appendChild(content);
            
            return element;
        }

        showNoResults() {
            const noResults = document.createElement('div');
            noResults.className = 'erp-select__no-results';
            noResults.textContent = this.options.noResultsText;
            this.optionsList.appendChild(noResults);
        }

        bindEvents() {
            // أحداث الزر المحفز
            this.trigger.addEventListener('click', (e) => this.handleTriggerClick(e));
            this.trigger.addEventListener('keydown', (e) => this.handleTriggerKeydown(e));
            
            // أحداث البحث
            if (this.searchInput) {
                this.searchInput.addEventListener('input', (e) => this.handleSearch(e));
                this.searchInput.addEventListener('keydown', (e) => this.handleSearchKeydown(e));
            }
            
            // أحداث الخيارات
            this.optionsList.addEventListener('click', (e) => this.handleOptionClick(e));
            
            // زر المسح
            if (this.clearButton) {
                this.clearButton.addEventListener('click', (e) => this.handleClear(e));
            }
            
            // إغلاق عند النقر خارج العنصر
            document.addEventListener('click', (e) => this.handleOutsideClick(e));
            
            // أحداث لوحة المفاتيح العامة
            document.addEventListener('keydown', (e) => this.handleGlobalKeydown(e));
        }

        // الأيقونات المخصصة
        getArrowIcon() {
            return `<svg width="12" height="8" viewBox="0 0 12 8" fill="none">
                <path d="M1 1.5L6 6.5L11 1.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>`;
        }

        getClearIcon() {
            return `<svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                <path d="M10.5 3.5L3.5 10.5M3.5 3.5L10.5 10.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>`;
        }

        getSearchIcon() {
            return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path d="M7.33333 12.6667C10.2789 12.6667 12.6667 10.2789 12.6667 7.33333C12.6667 4.38781 10.2789 2 7.33333 2C4.38781 2 2 4.38781 2 7.33333C2 10.2789 4.38781 12.6667 7.33333 12.6667Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M14 14L11.1 11.1" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>`;
        }

        getCheckboxIcon(checked) {
            if (checked) {
                return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect width="16" height="16" rx="3" fill="currentColor"/>
                    <path d="M4.5 8L7 10.5L11.5 6" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>`;
            } else {
                return `<svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <rect width="16" height="16" rx="3" stroke="currentColor" stroke-width="1.5" fill="none"/>
                </svg>`;
            }
        }
    }

    // سجل عام للمثيلات
    ERPSelect.instances = new Map();

        // معالجات الأحداث
        handleTriggerClick(e) {
            e.preventDefault();
            e.stopPropagation();

            if (this.isDisabled) return;

            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }

        handleTriggerKeydown(e) {
            if (this.isDisabled) return;

            switch (e.key) {
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    this.toggle();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.open();
                    this.focusNextOption();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.open();
                    this.focusPreviousOption();
                    break;
                case 'Escape':
                    this.close();
                    break;
            }
        }

        handleSearch(e) {
            const value = e.target.value;
            this.searchTerm = value;

            // إلغاء البحث السابق
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }

            // بحث مع تأخير
            this.searchTimeout = setTimeout(() => {
                this.performSearch(value);
            }, this.options.searchDelay);
        }

        handleSearchKeydown(e) {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.focusNextOption();
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.focusPreviousOption();
                    break;
                case 'Enter':
                    e.preventDefault();
                    this.selectFocusedOption();
                    break;
                case 'Escape':
                    this.close();
                    this.trigger.focus();
                    break;
            }
        }

        handleOptionClick(e) {
            const option = e.target.closest('.erp-select__option');
            if (!option || option.classList.contains('erp-select__option--disabled')) {
                return;
            }

            const value = option.getAttribute('data-value');
            this.selectOption(value);

            if (!this.options.multiple) {
                this.close();
            }
        }

        handleClear(e) {
            e.preventDefault();
            e.stopPropagation();
            this.clear();
        }

        handleOutsideClick(e) {
            if (!this.container.contains(e.target)) {
                this.close();
            }
        }

        handleGlobalKeydown(e) {
            if (!this.isOpen) return;

            if (e.key === 'Tab') {
                this.close();
            }
        }

        // وظائف التحكم
        open() {
            if (this.isOpen || this.isDisabled) return;

            this.isOpen = true;
            this.container.classList.add('erp-select--open');
            this.trigger.setAttribute('aria-expanded', 'true');

            // تركيز على البحث إذا كان متاح
            if (this.searchInput) {
                setTimeout(() => this.searchInput.focus(), 10);
            }

            // تحديث الموقع
            this.updateDropdownPosition();

            // إطلاق حدث
            this.triggerEvent('open');
        }

        close() {
            if (!this.isOpen) return;

            this.isOpen = false;
            this.container.classList.remove('erp-select--open');
            this.trigger.setAttribute('aria-expanded', 'false');

            // مسح البحث
            if (this.searchInput) {
                this.searchInput.value = '';
                this.searchTerm = '';
                this.performSearch('');
            }

            // إطلاق حدث
            this.triggerEvent('close');
        }

        toggle() {
            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }

        selectOption(value) {
            if (this.options.multiple) {
                this.toggleMultipleSelection(value);
            } else {
                this.setSingleSelection(value);
            }

            this.updateDisplay();
            this.updateOriginalSelect();
            this.triggerEvent('change', { value: this.getValue() });
        }

        toggleMultipleSelection(value) {
            const index = this.selectedValues.indexOf(value);
            if (index > -1) {
                this.selectedValues.splice(index, 1);
            } else {
                this.selectedValues.push(value);
            }
        }

        setSingleSelection(value) {
            this.selectedValues = [value];
        }

        clear() {
            this.selectedValues = [];
            this.updateDisplay();
            this.updateOriginalSelect();
            this.triggerEvent('clear');
            this.triggerEvent('change', { value: this.getValue() });
        }

        // وظائف البحث
        performSearch(term) {
            if (this.options.ajax) {
                this.performAjaxSearch(term);
            } else {
                this.performLocalSearch(term);
            }
        }

        performLocalSearch(term) {
            if (!term || term.length < this.options.searchMinLength) {
                this.filteredOptions = [...this.originalOptions];
            } else {
                this.filteredOptions = this.originalOptions.filter(option =>
                    option.text.toLowerCase().includes(term.toLowerCase())
                );
            }

            this.updateOptions();
            this.triggerEvent('search', { term });
        }

        performAjaxSearch(term) {
            // إظهار مؤشر التحميل
            this.showLoading();

            const ajaxOptions = {
                url: this.options.ajax.url,
                data: {
                    search: term,
                    ...this.options.ajax.data
                },
                ...this.options.ajax
            };

            // تنفيذ طلب AJAX
            this.makeAjaxRequest(ajaxOptions)
                .then(data => {
                    this.handleAjaxSuccess(data, term);
                })
                .catch(error => {
                    this.handleAjaxError(error);
                });
        }

        makeAjaxRequest(options) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                const url = new URL(options.url, window.location.origin);

                // إضافة المعاملات للURL
                Object.keys(options.data || {}).forEach(key => {
                    url.searchParams.append(key, options.data[key]);
                });

                xhr.open('GET', url.toString());
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');

                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            const data = JSON.parse(xhr.responseText);
                            resolve(data);
                        } catch (e) {
                            reject(new Error('استجابة JSON غير صحيحة'));
                        }
                    } else {
                        reject(new Error(`خطأ HTTP: ${xhr.status}`));
                    }
                };

                xhr.onerror = () => reject(new Error('خطأ في الشبكة'));
                xhr.send();
            });
        }

        handleAjaxSuccess(data, term) {
            this.hideLoading();

            // معالجة البيانات
            const results = data.results || data.data || data;
            this.filteredOptions = results.map(item => ({
                value: item.id || item.value,
                text: item.text || item.name || item.title,
                data: item
            }));

            // حفظ في الكاش
            if (this.options.cacheResults) {
                this.cache.set(term, this.filteredOptions);
            }

            this.updateOptions();
            this.triggerEvent('search', { term, results: this.filteredOptions });
        }

        handleAjaxError(error) {
            this.hideLoading();
            console.error('خطأ في البحث:', error);
            this.showError('حدث خطأ في البحث');
        }

        // وظائف العرض
        updateDisplay() {
            const display = this.trigger.querySelector('.erp-select__display');

            if (this.selectedValues.length === 0) {
                display.textContent = this.options.placeholder;
                display.classList.add('erp-select__display--placeholder');
                this.hideClearButton();
            } else {
                display.classList.remove('erp-select__display--placeholder');

                if (this.options.multiple) {
                    this.updateMultipleDisplay(display);
                } else {
                    this.updateSingleDisplay(display);
                }

                this.showClearButton();
            }
        }

        updateSingleDisplay(display) {
            const selectedOption = this.getOptionByValue(this.selectedValues[0]);
            display.textContent = selectedOption ? selectedOption.text : this.selectedValues[0];
        }

        updateMultipleDisplay(display) {
            const count = this.selectedValues.length;
            if (count === 1) {
                const selectedOption = this.getOptionByValue(this.selectedValues[0]);
                display.textContent = selectedOption ? selectedOption.text : this.selectedValues[0];
            } else {
                display.textContent = `تم اختيار ${count} عناصر`;
            }
        }

        updateOriginalSelect() {
            // مسح التحديدات السابقة
            Array.from(this.originalSelect.options).forEach(option => {
                option.selected = false;
            });

            // تطبيق التحديدات الجديدة
            this.selectedValues.forEach(value => {
                const option = this.originalSelect.querySelector(`option[value="${value}"]`);
                if (option) {
                    option.selected = true;
                }
            });

            // إطلاق حدث change على العنصر الأصلي
            this.originalSelect.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // وظائف مساعدة
        isSelected(value) {
            return this.selectedValues.includes(value);
        }

        getOptionByValue(value) {
            return this.originalOptions.find(option => option.value === value);
        }

        showClearButton() {
            if (this.clearButton && this.options.clearable) {
                this.clearButton.style.display = 'block';
            }
        }

        hideClearButton() {
            if (this.clearButton) {
                this.clearButton.style.display = 'none';
            }
        }

        showLoading() {
            this.optionsList.innerHTML = `
                <div class="erp-select__loading">
                    <span class="erp-select__loading-spinner"></span>
                    <span class="erp-select__loading-text">${this.options.loadingText}</span>
                </div>
            `;
        }

        hideLoading() {
            const loading = this.optionsList.querySelector('.erp-select__loading');
            if (loading) {
                loading.remove();
            }
        }

        showError(message) {
            this.optionsList.innerHTML = `
                <div class="erp-select__error">
                    <span class="erp-select__error-text">${message}</span>
                </div>
            `;
        }

        updateDropdownPosition() {
            // حساب الموقع الأمثل للقائمة المنسدلة
            const rect = this.trigger.getBoundingClientRect();
            const dropdownHeight = this.dropdown.offsetHeight;
            const viewportHeight = window.innerHeight;

            // تحديد ما إذا كان يجب فتح القائمة لأعلى أم لأسفل
            const spaceBelow = viewportHeight - rect.bottom;
            const spaceAbove = rect.top;

            if (spaceBelow < dropdownHeight && spaceAbove > dropdownHeight) {
                this.dropdown.classList.add('erp-select__dropdown--up');
            } else {
                this.dropdown.classList.remove('erp-select__dropdown--up');
            }
        }

        triggerEvent(eventName, data = {}) {
            const event = new CustomEvent(`erp-select:${eventName}`, {
                detail: { instance: this, ...data }
            });

            this.originalSelect.dispatchEvent(event);

            // استدعاء callback إذا كان موجود
            const callback = this.options[`on${eventName.charAt(0).toUpperCase() + eventName.slice(1)}`];
            if (typeof callback === 'function') {
                callback.call(this, data);
            }
        }

        // API عام
        getValue() {
            if (this.options.multiple) {
                return this.selectedValues;
            } else {
                return this.selectedValues[0] || null;
            }
        }

        setValue(value) {
            if (this.options.multiple) {
                this.selectedValues = Array.isArray(value) ? value : [value];
            } else {
                this.selectedValues = value ? [value] : [];
            }

            this.updateDisplay();
            this.updateOriginalSelect();
            this.updateOptions();
        }

        addOption(option) {
            this.originalOptions.push(option);
            this.filteredOptions = [...this.originalOptions];
            this.updateOptions();
        }

        removeOption(value) {
            this.originalOptions = this.originalOptions.filter(opt => opt.value !== value);
            this.filteredOptions = [...this.originalOptions];
            this.selectedValues = this.selectedValues.filter(val => val !== value);
            this.updateDisplay();
            this.updateOriginalSelect();
            this.updateOptions();
        }

        enable() {
            this.isDisabled = false;
            this.container.classList.remove('erp-select--disabled');
            this.trigger.setAttribute('tabindex', '0');
        }

        disable() {
            this.isDisabled = true;
            this.container.classList.add('erp-select--disabled');
            this.trigger.setAttribute('tabindex', '-1');
            this.close();
        }

        destroy() {
            // إزالة من السجل العام
            ERPSelect.instances.delete(this.originalSelect);

            // إزالة الأحداث
            this.container.removeEventListener('click', this.handleTriggerClick);
            document.removeEventListener('click', this.handleOutsideClick);
            document.removeEventListener('keydown', this.handleGlobalKeydown);

            // إزالة من DOM
            this.container.remove();
            this.originalSelect.style.display = '';

            // تنظيف المؤقتات
            if (this.searchTimeout) {
                clearTimeout(this.searchTimeout);
            }
        }

        updateSelectedValues() {
            this.selectedValues = [];
            Array.from(this.originalSelect.options).forEach(option => {
                if (option.selected && option.value !== '') {
                    this.selectedValues.push(option.value);
                }
            });
            this.updateDisplay();
        }

        hide() {
            this.container.style.display = 'none';
        }

        show() {
            this.container.style.display = 'block';
        }
    }

    // دوال مساعدة عامة
    ERPSelect.init = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const instances = [];

        elements.forEach(element => {
            if (element.tagName === 'SELECT' && !ERPSelect.instances.has(element)) {
                const instance = new ERPSelect(element, options);
                instances.push(instance);
            }
        });

        return instances.length === 1 ? instances[0] : instances;
    };

    ERPSelect.destroy = function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            const instance = ERPSelect.instances.get(element);
            if (instance) {
                instance.destroy();
            }
        });
    };

    // سجل عام للمثيلات
    ERPSelect.instances = new Map();

    // تصدير للاستخدام العام
    window.ERPSelect = ERPSelect;

})(window, document);
