/**
 * مكون التواريخ المبسط
 * يستخدم Flatpickr مع دعم اللغة العربية
 */

(function() {
    'use strict';

    // إعدادات افتراضية
    const DEFAULT_CONFIG = {
        locale: 'ar',
        dateFormat: 'Y-m-d',
        altInput: true,
        altFormat: 'd/m/Y',
        allowInput: true,
        clickOpens: true,
        time_24hr: true,
        enableTime: false,
        noCalendar: false,
        mode: 'single', // single, multiple, range
        minDate: null,
        maxDate: null,
        defaultDate: null,
        disable: [],
        enable: [],
        inline: false,
        static: false,
        position: 'auto',
        theme: 'default' // default, dark, material_blue, etc.
    };

    // كلاس التواريخ المبسط
    class SimpleDatePicker {
        constructor(element, options = {}) {
            this.element = element;
            this.config = { ...DEFAULT_CONFIG, ...options };
            this.instance = null;
            
            this.init();
        }

        init() {
            if (this.isFlatpickrAvailable()) {
                this.initFlatpickr();
            } else {
                console.warn('⚠️ Flatpickr غير متاح، استخدام input عادي');
                this.fallbackToNative();
            }
        }

        isFlatpickrAvailable() {
            const available = typeof window.flatpickr !== 'undefined';
            console.log('🔍 فحص Flatpickr:', {
                available: available,
                flatpickr: typeof window.flatpickr,
                arabicLocale: window.flatpickr?.l10ns?.ar ? 'متاح' : 'غير متاح'
            });
            return available;
        }

        initFlatpickr() {
            try {
                // تحديد نوع التاريخ من data attributes
                this.detectDateType();

                // إعداد التكوين النهائي
                const config = this.buildConfig();

                console.log('🔧 إعداد Flatpickr:', {
                    element: this.element.id || this.element.name || 'unnamed',
                    config: config
                });

                // تهيئة Flatpickr
                this.instance = window.flatpickr(this.element, config);

                if (this.instance) {
                    console.log('✅ تم تهيئة Flatpickr بنجاح للعنصر:', this.element.id || this.element.name);

                    // إضافة event listeners للتشخيص
                    this.instance.config.onReady = () => {
                        console.log('📅 Flatpickr جاهز للاستخدام');
                    };
                } else {
                    console.error('❌ فشل في تهيئة Flatpickr');
                    this.fallbackToNative();
                }

            } catch (error) {
                console.error('❌ خطأ في تهيئة Flatpickr:', error);
                this.fallbackToNative();
            }
        }

        detectDateType() {
            const type = this.element.dataset.dateType;
            
            switch (type) {
                case 'datetime':
                    this.config.enableTime = true;
                    this.config.dateFormat = 'Y-m-d H:i';
                    this.config.altFormat = 'd/m/Y H:i';
                    break;
                    
                case 'time':
                    this.config.enableTime = true;
                    this.config.noCalendar = true;
                    this.config.dateFormat = 'H:i';
                    this.config.altFormat = 'H:i';
                    break;
                    
                case 'range':
                    this.config.mode = 'range';
                    this.config.dateFormat = 'Y-m-d';
                    this.config.altFormat = 'd/m/Y';
                    break;
                    
                case 'multiple':
                    this.config.mode = 'multiple';
                    this.config.dateFormat = 'Y-m-d';
                    this.config.altFormat = 'd/m/Y';
                    break;
                    
                default: // date
                    this.config.enableTime = false;
                    this.config.dateFormat = 'Y-m-d';
                    this.config.altFormat = 'd/m/Y';
                    break;
            }
        }

        buildConfig() {
            const config = { ...this.config };
            
            // إعدادات اللغة العربية
            if (config.locale === 'ar' && window.flatpickr.l10ns.ar) {
                config.locale = window.flatpickr.l10ns.ar;
            }
            
            // إعدادات من data attributes
            if (this.element.dataset.minDate) {
                config.minDate = this.element.dataset.minDate;
            }
            
            if (this.element.dataset.maxDate) {
                config.maxDate = this.element.dataset.maxDate;
            }
            
            if (this.element.dataset.defaultDate) {
                config.defaultDate = this.element.dataset.defaultDate;
            }
            
            if (this.element.dataset.dateFormat) {
                config.dateFormat = this.element.dataset.dateFormat;
            }
            
            // إعدادات الثيم
            if (this.element.dataset.dateTheme) {
                config.theme = this.element.dataset.dateTheme;
            }
            
            // إعدادات خاصة
            if (this.element.dataset.dateInline === 'true') {
                config.inline = true;
            }
            
            if (this.element.dataset.dateStatic === 'true') {
                config.static = true;
            }
            
            // أحداث مخصصة
            config.onChange = (selectedDates, dateStr, instance) => {
                this.triggerEvent('change', { selectedDates, dateStr, instance });
            };
            
            config.onOpen = (selectedDates, dateStr, instance) => {
                this.triggerEvent('open', { selectedDates, dateStr, instance });
            };
            
            config.onClose = (selectedDates, dateStr, instance) => {
                this.triggerEvent('close', { selectedDates, dateStr, instance });
            };
            
            return config;
        }

        triggerEvent(eventName, data) {
            const event = new CustomEvent(`date:${eventName}`, {
                detail: data
            });
            this.element.dispatchEvent(event);
        }

        // API موحد
        getValue() {
            if (this.instance) {
                return this.instance.selectedDates;
            }
            return this.element.value;
        }

        getFormattedValue() {
            if (this.instance) {
                return this.instance.input.value;
            }
            return this.element.value;
        }

        setValue(date) {
            if (this.instance) {
                this.instance.setDate(date);
            } else {
                this.element.value = date;
            }
        }

        clear() {
            if (this.instance) {
                this.instance.clear();
            } else {
                this.element.value = '';
            }
        }

        open() {
            if (this.instance) {
                this.instance.open();
            }
        }

        close() {
            if (this.instance) {
                this.instance.close();
            }
        }

        destroy() {
            if (this.instance) {
                this.instance.destroy();
                this.instance = null;
            }
        }

        setMinDate(date) {
            if (this.instance) {
                this.instance.set('minDate', date);
            }
        }

        setMaxDate(date) {
            if (this.instance) {
                this.instance.set('maxDate', date);
            }
        }

        fallbackToNative() {
            console.warn('⚠️ تم الرجوع إلى input عادي');
            this.element.classList.add('form-control');
            
            // تحديد نوع input حسب النوع
            const type = this.element.dataset.dateType;
            switch (type) {
                case 'datetime':
                    this.element.type = 'datetime-local';
                    break;
                case 'time':
                    this.element.type = 'time';
                    break;
                default:
                    this.element.type = 'date';
                    break;
            }
        }
    }

    // دالة تهيئة عامة
    window.initSimpleDatePicker = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const instances = [];

        elements.forEach(element => {
            const instance = new SimpleDatePicker(element, options);
            instances.push(instance);
        });

        return instances.length === 1 ? instances[0] : instances;
    };

    // دالة للتحقق من توفر المكتبات
    function waitForLibraries(callback, maxAttempts = 50) {
        let attempts = 0;

        function check() {
            attempts++;

            if (typeof window.flatpickr !== 'undefined') {
                console.log('✅ Flatpickr متاح، بدء التهيئة...');
                callback();
            } else if (attempts < maxAttempts) {
                console.log(`⏳ انتظار Flatpickr... المحاولة ${attempts}`);
                setTimeout(check, 100);
            } else {
                console.warn('⚠️ انتهت مهلة انتظار Flatpickr، استخدام fallback');
                callback();
            }
        }

        check();
    }

    // تهيئة تلقائية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        waitForLibraries(function() {
            // إنشاء مخزن عام للمثيلات
            if (!window.dateInstances) {
                window.dateInstances = {};
            }

            // تهيئة جميع input مع data-date
            document.querySelectorAll('input[data-date]').forEach(element => {
                const config = element.dataset.dateConfig ?
                    JSON.parse(element.dataset.dateConfig) : {};

                const instance = new SimpleDatePicker(element, config);

                // حفظ المثيل للوصول إليه لاحقاً
                if (element.id) {
                    window.dateInstances[element.id] = instance;
                }
            });

            console.log('🗓️ تم تهيئة جميع منتقيات التواريخ');
        });
    });

})();
