<?php
/**
 * مثال على استخدام النظام الذكي الجديد
 * page_helper.php يحدد النوع تلقائياً
 */

require_once 'loader.php';

echo "<h1>🧠 اختبار النظام الذكي</h1>";

// مثال 1: بطاقات فقط (سيحدد تلقائياً: cards)
echo "<h2>1️⃣ بطاقات فقط:</h2>";
render_page([
    'title' => 'إحصائيات المبيعات',
    'stats' => [
        [
            'title' => 'إجمالي المبيعات',
            'value' => 125000,
            'icon' => 'fas fa-dollar-sign',
            'color' => 'success'
        ],
        [
            'title' => 'عدد الطلبات',
            'value' => 450,
            'icon' => 'fas fa-shopping-cart',
            'color' => 'info'
        ]
    ],
    'actions' => [
        [
            'type' => 'primary',
            'url' => 'sales/create',
            'icon' => 'fas fa-plus',
            'text' => 'إضافة مبيعة'
        ]
    ],
    'breadcrumb' => [
        ['title' => 'المبيعات', 'url' => 'sales'],
        ['title' => 'الإحصائيات', 'active' => true]
    ]
]);

echo "<hr><h2>2️⃣ جدول فقط:</h2>";
// مثال 2: جدول فقط (سيحدد تلقائياً: datatable)
render_page([
    'title' => 'قائمة المنتجات',
    'columns' => [
        ['title' => 'الاسم', 'field' => 'name', 'type' => 'text'],
        ['title' => 'السعر', 'field' => 'price', 'type' => 'text'],
        ['title' => 'الحالة', 'field' => 'status', 'type' => 'badge']
    ],
    'data' => [
        ['name' => 'منتج 1', 'price' => '100 ريال', 'status' => 'active'],
        ['name' => 'منتج 2', 'price' => '200 ريال', 'status' => 'inactive'],
        ['name' => 'منتج 3', 'price' => '150 ريال', 'status' => 'active']
    ],
    'actions' => [
        [
            'type' => 'primary',
            'url' => 'products/create',
            'icon' => 'fas fa-plus',
            'text' => 'إضافة منتج'
        ]
    ],
    'breadcrumb' => [
        ['title' => 'المنتجات', 'active' => true]
    ]
]);

echo "<hr><h2>3️⃣ نموذج:</h2>";
// مثال 3: نموذج (سيحدد تلقائياً: form)
render_page([
    'title' => 'إضافة عميل جديد',
    'form' => [
        'action' => 'customers/store',
        'method' => 'POST',
        'fields' => [
            [
                'name' => 'name',
                'label' => 'اسم العميل',
                'type' => 'text',
                'required' => true,
                'placeholder' => 'أدخل اسم العميل'
            ],
            [
                'name' => 'email',
                'label' => 'البريد الإلكتروني',
                'type' => 'email',
                'required' => true,
                'placeholder' => '<EMAIL>'
            ],
            [
                'name' => 'phone',
                'label' => 'رقم الهاتف',
                'type' => 'text',
                'placeholder' => '+966 50 123 4567'
            ]
        ]
    ],
    'breadcrumb' => [
        ['title' => 'العملاء', 'url' => 'customers'],
        ['title' => 'إضافة عميل', 'active' => true]
    ]
]);

echo "<hr><h2>4️⃣ تقرير:</h2>";
// مثال 4: تقرير (سيحدد تلقائياً: report)
render_page([
    'title' => 'تقرير المبيعات اليومي',
    'stats' => [
        [
            'title' => 'مبيعات اليوم',
            'value' => 15000,
            'icon' => 'fas fa-chart-line',
            'color' => 'success'
        ]
    ],
    'charts' => [
        [
            'id' => 'dailySales',
            'title' => 'المبيعات اليومية',
            'col_size' => 12,
            'data' => [
                'type' => 'line',
                'data' => [
                    'labels' => ['9 ص', '12 ظ', '3 م', '6 م', '9 م'],
                    'datasets' => [[
                        'label' => 'المبيعات',
                        'data' => [1000, 2500, 4000, 3500, 4000],
                        'borderColor' => 'rgb(75, 192, 192)',
                        'tension' => 0.1
                    ]]
                ]
            ]
        ]
    ],
    'data' => [
        'headers' => ['الوقت', 'المبلغ', 'العدد'],
        'rows' => [
            ['9:00 ص', '1,000 ريال', '5'],
            ['12:00 ظ', '2,500 ريال', '12'],
            ['3:00 م', '4,000 ريال', '18']
        ]
    ],
    'breadcrumb' => [
        ['title' => 'التقارير', 'url' => 'reports'],
        ['title' => 'المبيعات اليومية', 'active' => true]
    ]
]);

echo "<hr><h2>5️⃣ مختلط (بطاقات + جدول):</h2>";
// مثال 5: مختلط (سيحدد تلقائياً: mixed)
render_page([
    'title' => 'إدارة الموردين',
    'stats' => [
        [
            'title' => 'إجمالي الموردين',
            'value' => 25,
            'icon' => 'fas fa-truck',
            'color' => 'primary'
        ],
        [
            'title' => 'الموردين النشطين',
            'value' => 20,
            'icon' => 'fas fa-check-circle',
            'color' => 'success'
        ]
    ],
    'columns' => [
        ['title' => 'اسم المورد', 'field' => 'name', 'type' => 'text'],
        ['title' => 'الهاتف', 'field' => 'phone', 'type' => 'text'],
        ['title' => 'الحالة', 'field' => 'status', 'type' => 'badge']
    ],
    'data' => [
        ['name' => 'مورد 1', 'phone' => '0501234567', 'status' => 'active'],
        ['name' => 'مورد 2', 'phone' => '0507654321', 'status' => 'inactive']
    ],
    'actions' => [
        [
            'type' => 'primary',
            'url' => 'suppliers/create',
            'icon' => 'fas fa-plus',
            'text' => 'إضافة مورد'
        ]
    ],
    'breadcrumb' => [
        ['title' => 'المشتريات', 'url' => 'purchases'],
        ['title' => 'الموردين', 'active' => true]
    ]
]);

echo "<hr><h2>6️⃣ صفحة بسيطة:</h2>";
// مثال 6: صفحة بسيطة (سيحدد تلقائياً: simple)
render_page([
    'title' => 'حول النظام',
    'content' => '<h4>مرحباً بك في نظام إدارة الموارد</h4><p>هذا النظام يساعدك في إدارة جميع موارد شركتك بكفاءة عالية.</p>',
    'breadcrumb' => [
        ['title' => 'الرئيسية', 'url' => 'dashboard'],
        ['title' => 'حول النظام', 'active' => true]
    ]
]);

// عرض معلومات الأنظمة المتاحة
echo "<hr><h2>🔍 الأنظمة المتاحة:</h2>";
$systems = get_available_systems();
echo "<ul>";
foreach ($systems as $system => $available) {
    $status = $available ? '✅ متاح' : '❌ غير متاح';
    echo "<li><strong>$system:</strong> $status</li>";
}
echo "</ul>";
?>
