/* ===== FILTERS MODAL DESIGN (تصميم نافذة الفلاتر) ===== */

/* Filter Modal Container */
.filter-modal .modal-dialog {
    max-width: 600px;
    margin: 30px auto;
}

.filter-modal .modal-content {
    border-radius: 24px;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 8px 16px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

body.dark-theme .filter-modal .modal-content {
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.4),
        0 8px 16px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Filter Modal Header */
.filter-modal .modal-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 24px 32px 20px;
    border-bottom: none;
    position: relative;
    overflow: hidden;
}

.filter-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
    pointer-events: none;
}

.filter-modal .modal-title {
    font-size: 20px;
    font-weight: 700;
    color: white;
    margin: 0;
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    gap: 12px;
}

.filter-modal .modal-title i {
    font-size: 18px;
    opacity: 0.9;
}

.filter-modal .modal-close {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: none;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.filter-modal .modal-close:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: scale(1.05);
}

/* Filter Modal Body */
.filter-modal .modal-body {
    padding: 32px;
    background: rgba(255, 255, 255, 0.98);
}

body.dark-theme .filter-modal .modal-body {
    background: rgba(31, 41, 55, 0.98);
}

/* Form Groups */
.filter-modal .form-label {
    font-weight: 600;
    font-size: 14px;
    color: var(--light-text-color);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
}

body.dark-theme .filter-modal .form-label {
    color: var(--dark-text-color);
}

.filter-modal .form-label i {
    font-size: 13px;
    color: var(--primary-color);
    opacity: 0.8;
}

/* Form Controls */
.filter-modal .form-control,
.filter-modal .form-select {
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    background: rgba(255, 255, 255, 0.9);
    color: var(--light-text-color);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

body.dark-theme .filter-modal .form-control,
body.dark-theme .filter-modal .form-select {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--dark-text-color);
}

.filter-modal .form-control:focus,
.filter-modal .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1);
    background: rgba(255, 255, 255, 1);
    outline: none;
}

body.dark-theme .filter-modal .form-control:focus,
body.dark-theme .filter-modal .form-select:focus {
    background: rgba(55, 65, 81, 1);
    border-color: var(--primary-color);
}

.filter-modal .form-control::placeholder {
    color: var(--light-text-muted);
    opacity: 0.7;
}

body.dark-theme .filter-modal .form-control::placeholder {
    color: var(--dark-text-muted);
}

/* Form Text (Help) */
.filter-modal .form-text {
    font-size: 12px;
    color: var(--light-text-muted);
    margin-top: 6px;
    padding-left: 4px;
}

body.dark-theme .filter-modal .form-text {
    color: var(--dark-text-muted);
}