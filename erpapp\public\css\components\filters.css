/* ===== ADVANCED SELECT INTEGRATION ===== */

/* Select2 Custom Styling */
.filter-modal .select2-container {
    width: 100% !important;
}

.filter-modal .select2-container--default .select2-selection--single {
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    height: 50px;
    padding: 0 18px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body.dark-theme .filter-modal .select2-container--default .select2-selection--single {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
}

.filter-modal .select2-container--default .select2-selection--single:focus,
.filter-modal .select2-container--default.select2-container--focus .select2-selection--single {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1);
    outline: none;
}

.filter-modal .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--light-text-color);
    line-height: 46px;
    padding: 0;
    font-size: 14px;
    font-weight: 500;
}

body.dark-theme .filter-modal .select2-container--default .select2-selection--single .select2-selection__rendered {
    color: var(--dark-text-color);
}

.filter-modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--light-text-muted);
    opacity: 0.7;
}

body.dark-theme .filter-modal .select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: var(--dark-text-muted);
}

.filter-modal .select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 46px;
    right: 18px;
}

[dir="rtl"] .filter-modal .select2-container--default .select2-selection--single .select2-selection__arrow {
    right: auto;
    left: 18px;
}

/* Select2 Dropdown */
.select2-dropdown {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(20px);
    overflow: hidden;
}

body.dark-theme .select2-dropdown {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
}

.select2-dropdown .select2-search--dropdown .select2-search__field {
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
}

body.dark-theme .select2-dropdown .select2-search--dropdown .select2-search__field {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--dark-text-color);
}

.select2-dropdown .select2-results__option {
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.select2-dropdown .select2-results__option--highlighted {
    background: var(--primary-color) !important;
    color: white !important;
}

/* Choices.js Custom Styling */
.filter-modal .choices {
    margin-bottom: 0;
}

.filter-modal .choices__inner {
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 12px 18px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    min-height: 50px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body.dark-theme .filter-modal .choices__inner {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
}

.filter-modal .choices.is-focused .choices__inner {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1);
}

.filter-modal .choices__placeholder {
    color: var(--light-text-muted);
    opacity: 0.7;
}

body.dark-theme .filter-modal .choices__placeholder {
    color: var(--dark-text-muted);
}

/* Tom Select Custom Styling */
.filter-modal .ts-control {
    border: 2px solid rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    padding: 12px 18px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    min-height: 50px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body.dark-theme .filter-modal .ts-control {
    background: rgba(55, 65, 81, 0.9);
    border-color: rgba(255, 255, 255, 0.1);
}

.filter-modal .ts-control.focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(var(--primary-rgb), 0.1);
}

/* Filter Animation States */
.filter-changed {
    animation: filterChanged 0.3s ease;
}

@keyframes filterChanged {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.filter-typing {
    border-color: var(--primary-color) !important;
    box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1) !important;
}

/* Loading States */
.datatable-filter-icon.loading {
    animation: filterLoading 1s linear infinite;
}

@keyframes filterLoading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.has-active-filters {
    color: var(--primary-color) !important;
    background: rgba(var(--primary-rgb), 0.1) !important;
}