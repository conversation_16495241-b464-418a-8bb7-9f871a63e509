<?php
/**
 * اختبار نظام البطاقات فقط
 */

// تحميل الملفات الأساسية فقط
require_once 'App/Config/config.php';
require_once 'App/Helpers/functions.php';

// تحميل cards_helper.php فقط
require_once 'App/Helpers/cards_helper.php';

echo "<h1>🧪 اختبار نظام البطاقات</h1>";

// اختبار 1: التحقق من وجود الدوال
echo "<h2>1️⃣ فحص الدوال:</h2>";
$functions = [
    'render_cards_page',
    'render_cards_layout', 
    'render_cards_header',
    'render_stats_cards',
    'render_cards_toolbar',
    'render_cards_scripts',
    'create_stat_card',
    'create_stats_group',
    'create_card_action'
];

foreach ($functions as $func) {
    $status = function_exists($func) ? '✅' : '❌';
    echo "<p>$status $func</p>";
}

// اختبار 2: إنشاء بطاقات بسيطة
echo "<h2>2️⃣ اختبار إنشاء البطاقات:</h2>";

try {
    $stats = [
        [
            'title' => 'اختبار 1',
            'value' => 100,
            'icon' => 'fas fa-test',
            'color' => 'primary'
        ],
        [
            'title' => 'اختبار 2', 
            'value' => 200,
            'icon' => 'fas fa-check',
            'color' => 'success'
        ]
    ];
    
    $actions = [
        [
            'type' => 'primary',
            'url' => 'test',
            'icon' => 'fas fa-plus',
            'text' => 'إضافة'
        ]
    ];
    
    $breadcrumb = [
        ['title' => 'الرئيسية', 'url' => 'home'],
        ['title' => 'اختبار البطاقات', 'active' => true]
    ];
    
    echo "<p>✅ تم إنشاء البيانات بنجاح</p>";
    
    // اختبار 3: عرض البطاقات
    echo "<h2>3️⃣ عرض البطاقات:</h2>";
    
    render_cards_page([
        'title' => 'اختبار نظام البطاقات',
        'module' => 'test',
        'entity' => 'cards',
        'stats' => $stats,
        'actions' => $actions,
        'breadcrumb' => $breadcrumb
    ]);
    
    echo "<p>✅ تم عرض البطاقات بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ خطأ فادح: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>4️⃣ اختبار الدوال المساعدة:</h2>";

try {
    // اختبار create_stat_card
    $card = create_stat_card('اختبار', 500, 'fas fa-star', 'warning', 'وصف الاختبار');
    echo "<p>✅ create_stat_card: " . json_encode($card) . "</p>";
    
    // اختبار create_card_action
    $action = create_card_action('info', 'test/action', 'fas fa-info', 'معلومات');
    echo "<p>✅ create_card_action: " . json_encode($action) . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في الدوال المساعدة: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr><p><strong>انتهى الاختبار</strong></p>";
?>
