/* ===== CARD STYLES ===== */

/* ===== BASE CARD ===== */
.card {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    margin-bottom: var(--spacing-6);
    transition: all var(--transition-normal) var(--transition-bezier);
    overflow: hidden;
    position: relative;
}

.card:hover {
    box-shadow: var(--box-shadow-md);
}

/* ===== CARD HEADER ===== */
.card-header {
    padding: var(--spacing-5) var(--spacing-6);
    background-color: transparent;
    border-bottom: 1px solid var(--light-border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.card-header h5, 
.card-header .h5 {
    margin-bottom: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.card-header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius);
    background: var(--primary-color);
    color: white;
    font-size: 1rem;
}

/* ===== CARD BODY ===== */
.card-body {
    padding: var(--spacing-6);
}

.card-body:first-child {
    border-top-left-radius: calc(var(--border-radius) - 1px);
    border-top-right-radius: calc(var(--border-radius) - 1px);
}

.card-body:last-child {
    border-bottom-left-radius: calc(var(--border-radius) - 1px);
    border-bottom-right-radius: calc(var(--border-radius) - 1px);
}

/* ===== CARD FOOTER ===== */
.card-footer {
    padding: var(--spacing-5) var(--spacing-6);
    background-color: transparent;
    border-top: 1px solid var(--light-border-color);
}

/* ===== CARD TITLE & TEXT ===== */
.card-title {
    margin-bottom: var(--spacing-3);
    font-weight: 600;
}

.card-subtitle {
    margin-top: calc(var(--spacing-3) * -0.5);
    margin-bottom: 0;
    font-weight: 400;
    color: var(--gray-600);
}

.card-text:last-child {
    margin-bottom: 0;
}

/* ===== CARD VARIANTS ===== */
.card-primary {
    border-color: var(--primary-color);
}

.card-primary .card-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom-color: var(--primary-color);
}

.card-secondary {
    border-color: var(--secondary-color);
}

.card-secondary .card-header {
    background-color: var(--secondary-color);
    color: white;
    border-bottom-color: var(--secondary-color);
}

.card-success {
    border-color: var(--success-color);
}

.card-success .card-header {
    background-color: var(--success-color);
    color: white;
    border-bottom-color: var(--success-color);
}

.card-info {
    border-color: var(--info-color);
}

.card-info .card-header {
    background-color: var(--info-color);
    color: white;
    border-bottom-color: var(--info-color);
}

.card-warning {
    border-color: var(--warning-color);
}

.card-warning .card-header {
    background-color: var(--warning-color);
    color: white;
    border-bottom-color: var(--warning-color);
}

.card-danger {
    border-color: var(--danger-color);
}

.card-danger .card-header {
    background-color: var(--danger-color);
    color: white;
    border-bottom-color: var(--danger-color);
}

/* ===== CARD GROUPS ===== */
.card-group {
    display: flex;
    flex-direction: column;
}

.card-group .card {
    margin-bottom: 0;
}

.card-group .card:not(:last-child) {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
}

.card-group .card:not(:first-child) {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

@media (min-width: 576px) {
    .card-group {
        flex-direction: row;
    }
    
    .card-group .card {
        flex: 1 0 0%;
    }
    
    .card-group .card:not(:last-child) {
        border-right: 0;
        border-bottom: 1px solid var(--light-card-border);
        border-bottom-right-radius: 0;
        border-bottom-left-radius: calc(var(--border-radius) - 1px);
    }
    
    .card-group .card:not(:first-child) {
        border-top-left-radius: 0;
        border-top-right-radius: calc(var(--border-radius) - 1px);
    }
}

/* ===== CARD DECK ===== */
.card-deck {
    display: flex;
    flex-direction: column;
}

.card-deck .card {
    margin-bottom: var(--spacing-4);
}

@media (min-width: 576px) {
    .card-deck {
        flex-direction: row;
        margin-left: calc(var(--spacing-4) * -1);
        margin-right: calc(var(--spacing-4) * -1);
    }
    
    .card-deck .card {
        flex: 1 0 0%;
        margin-right: var(--spacing-4);
        margin-left: var(--spacing-4);
        margin-bottom: 0;
    }
}

/* ===== CARD COLUMNS ===== */
@media (min-width: 576px) {
    .card-columns {
        column-count: 3;
        column-gap: var(--spacing-4);
        orphans: 1;
        widows: 1;
    }
    
    .card-columns .card {
        display: inline-block;
        width: 100%;
    }
}

/* ===== DARK THEME ===== */
body.dark-theme .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

body.dark-theme .card-header,
body.dark-theme .card-footer {
    border-color: var(--dark-border-color);
}

body.dark-theme .card-subtitle {
    color: var(--dark-text-muted);
}

body.dark-theme .card-group .card:not(:last-child) {
    border-bottom-color: var(--dark-card-border);
}

@media (min-width: 576px) {
    body.dark-theme .card-group .card:not(:last-child) {
        border-right-color: var(--dark-card-border);
        border-bottom-color: var(--dark-card-border);
    }
}
