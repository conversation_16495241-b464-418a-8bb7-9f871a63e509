/* ===== PAGE HEADER COMPONENT ===== */

/* حاوي الهيدر الرئيسي */
.page-header {
    margin-bottom: 1.5rem;
}

.page-header-box {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-sm);
}



/* ===== الصف الأول: الروابط والأزرار ===== */

.header-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    gap: 1rem;
    min-height: 2.5rem;
}

/* LTR Layout: Breadcrumb Left, Actions Right */
.header-breadcrumb-container {
    order: 1;
    flex: 1;
    display: flex;
    justify-content: flex-start;
}

.header-actions-container {
    order: 2;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
}


.rtl .header-actions-container {
    order: 1;
    flex-shrink: 0;
    justify-content: flex-start;
}

/* ===== الصف الثاني: عنوان الصفحة ===== */

.header-title-row {
    display: flex;
    align-items: center;
}

/* LTR: Title on Left */
.header-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    text-align: left;
    width: 100%;
}

/* RTL: Title on Right */
.rtl .header-title {
    text-align: right;
}

/* ===== تصميم الـ Breadcrumb ===== */

.header-breadcrumb {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.header-breadcrumb-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
}

.header-breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-fast) ease;
}

.header-breadcrumb-item a:hover {
    color: var(--primary-color);
}

.header-breadcrumb-item.active {
    color: var(--text-primary);
    font-weight: 500;
}

/* فاصل الـ breadcrumb */
.header-breadcrumb-item:not(:last-child)::after {
    content: ">";
    margin-left: 0.5rem;
    margin-right: 0.5rem;
    color: var(--text-muted);
    font-size: 0.75rem;
}

.rtl .header-breadcrumb-item:not(:last-child)::after {
    content: "<";
}

/* ===== تصميم الأزرار ===== */

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.header-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
    font-weight: 500;
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    border: 1px solid transparent;
    white-space: nowrap;
}

.header-actions .btn i {
    font-size: 0.875rem;
}

/* أزرار ملونة */
.header-actions .btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.header-actions .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-actions .btn-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.header-actions .btn-success:hover {
    background: var(--success-dark);
    border-color: var(--success-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-actions .btn-info {
    background: var(--info-color);
    color: white;
    border-color: var(--info-color);
}

.header-actions .btn-info:hover {
    background: var(--info-dark);
    border-color: var(--info-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-actions .btn-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.header-actions .btn-warning:hover {
    background: var(--warning-dark);
    border-color: var(--warning-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-actions .btn-danger {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

.header-actions .btn-danger:hover {
    background: var(--danger-dark);
    border-color: var(--danger-dark);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-actions .btn-secondary {
    background: var(--gray-500);
    color: white;
    border-color: var(--gray-500);
}

.header-actions .btn-secondary:hover {
    background: var(--gray-600);
    border-color: var(--gray-600);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ===== تحسين للشاشات الصغيرة ===== */

@media (max-width: 768px) {
    .header-top-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .header-breadcrumb-container,
    .header-actions-container {
        order: unset !important;
        width: 100%;
        justify-content: center !important;
    }

    .header-actions {
        justify-content: center;
    }

    .header-title {
        text-align: center;
    }

    .rtl .header-title {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .header-actions .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        gap: 0.25rem;
    }
    
    .header-actions .btn span {
        display: none;
    }
    
    .header-actions .btn i {
        font-size: 0.8rem;
    }
}
