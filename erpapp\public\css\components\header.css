/* ===== PAGE HEADER COMPONENT ===== */

/* حاوي الهيدر الرئيسي */
.page-header {
    margin-bottom: 1.5rem;
}

.page-header-box {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(255,255,255,0.95) 100%);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.page-header-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg,
        var(--primary-color) 0%,
        var(--success-color) 33%,
        var(--info-color) 66%,
        var(--warning-color) 100%);
    border-radius: 12px 12px 0 0;
    animation: gradientShift 3s ease-in-out infinite;
}

@keyframes gradientShift {
    0%, 100% {
        background: linear-gradient(90deg,
            var(--primary-color) 0%,
            var(--success-color) 33%,
            var(--info-color) 66%,
            var(--warning-color) 100%);
    }
    50% {
        background: linear-gradient(90deg,
            var(--warning-color) 0%,
            var(--info-color) 33%,
            var(--success-color) 66%,
            var(--primary-color) 100%);
    }
}



/* ===== الصف الأول: الروابط والأزرار ===== */

.header-top-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    gap: 1rem;
    min-height: 2.5rem;
}

/* LTR Layout: Breadcrumb Left, Actions Right */
.header-breadcrumb-container {
    order: 1;
    flex: 1;
    display: flex;
    justify-content: flex-start;
}

.header-actions-container {
    order: 2;
    flex-shrink: 0;
    display: flex;
    justify-content: flex-end;
}


.rtl .header-actions-container {
    order: 1;
    flex-shrink: 0;
    justify-content: flex-start;
}

/* ===== الصف الثاني: عنوان الصفحة ===== */

.header-title-row {
    display: flex;
    align-items: center;
}

/* ===== تصميم عنوان الصفحة الاحترافي ===== */

.header-title {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--text-primary), var(--primary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-align: left;
    width: 100%;
    display: block;
    position: relative;
    line-height: 1.2;
}

.header-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: 2px;
    transition: width 0.3s ease;
}

.header-title:hover::after {
    width: 120px;
}

/* RTL: Title on Right */
.rtl .header-title,
body.rtl .header-title {
    text-align: right;
}

.rtl .header-title::after,
body.rtl .header-title::after {
    left: auto;
    right: 0;
}

/* ===== تصميم الـ Breadcrumb الاحترافي ===== */

.header-breadcrumb {
    display: flex;
    align-items: center;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.header-breadcrumb-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    position: relative;
}

.header-breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    background: rgba(var(--primary-rgb), 0.08);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(5px);
}

.header-breadcrumb-item a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s ease;
}

.header-breadcrumb-item a:hover {
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-color: var(--primary-color);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 20px rgba(var(--primary-rgb), 0.3);
}

.header-breadcrumb-item a:hover::before {
    left: 100%;
}

.header-breadcrumb-item.active {
    color: white;
    font-weight: 700;
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 20px;
    box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.4);
    border: 2px solid rgba(255,255,255,0.2);
    position: relative;
}

.header-breadcrumb-item.active::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    right: 2px;
    bottom: 2px;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), transparent);
    border-radius: 18px;
    pointer-events: none;
}

/* فاصل الـ breadcrumb الاحترافي */
.header-breadcrumb-item:not(:last-child)::after {
    content: "›";
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0 0.75rem;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.header-breadcrumb-item:hover + .header-breadcrumb-item::after,
.header-breadcrumb-item:not(:last-child):hover::after {
    color: var(--primary-dark);
    opacity: 1;
    transform: scale(1.2);
}

.rtl .header-breadcrumb-item:not(:last-child)::after {
    content: "‹";
}

/* ===== تصميم الأزرار الاحترافي ===== */

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.header-actions .btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
    font-weight: 600;
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-actions .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.6s ease;
}

.header-actions .btn:hover::before {
    left: 100%;
}

.header-actions .btn i {
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.header-actions .btn:hover i {
    transform: scale(1.1);
}

/* ===== أزرار ملونة احترافية ===== */

.header-actions .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
}

.header-actions .btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    border-color: var(--primary-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--primary-rgb), 0.4);
}

.header-actions .btn-success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    color: white;
    border-color: var(--success-color);
    box-shadow: 0 4px 15px rgba(var(--success-rgb), 0.3);
}

.header-actions .btn-success:hover {
    background: linear-gradient(135deg, var(--success-dark), var(--success-color));
    border-color: var(--success-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--success-rgb), 0.4);
}

.header-actions .btn-info {
    background: linear-gradient(135deg, var(--info-color), var(--info-dark));
    color: white;
    border-color: var(--info-color);
    box-shadow: 0 4px 15px rgba(var(--info-rgb), 0.3);
}

.header-actions .btn-info:hover {
    background: linear-gradient(135deg, var(--info-dark), var(--info-color));
    border-color: var(--info-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--info-rgb), 0.4);
}

.header-actions .btn-warning {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    color: white;
    border-color: var(--warning-color);
    box-shadow: 0 4px 15px rgba(var(--warning-rgb), 0.3);
}

.header-actions .btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-dark), var(--warning-color));
    border-color: var(--warning-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--warning-rgb), 0.4);
}

.header-actions .btn-danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    color: white;
    border-color: var(--danger-color);
    box-shadow: 0 4px 15px rgba(var(--danger-rgb), 0.3);
}

.header-actions .btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-dark), var(--danger-color));
    border-color: var(--danger-dark);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(var(--danger-rgb), 0.4);
}

.header-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
    border-color: #6c757d;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.header-actions .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #6c757d);
    border-color: #5a6268;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

/* ===== تحسين للشاشات الصغيرة ===== */

@media (max-width: 768px) {
    .page-header-box {
        padding: 1rem;
        border-radius: 8px;
    }

    .header-top-row {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .header-breadcrumb-container,
    .header-actions-container {
        order: unset !important;
        width: 100%;
        justify-content: center !important;
    }

    .header-actions {
        justify-content: center;
        gap: 0.5rem;
    }

    .header-actions .btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .header-title {
        text-align: center;
        font-size: 1.5rem;
    }

    .header-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .rtl .header-title {
        text-align: center;
    }

    .rtl .header-title::after {
        right: 50%;
        left: 50%;
        transform: translateX(-50%);
    }

    .header-breadcrumb-item a {
        padding: 0.375rem 0.75rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .header-actions .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        gap: 0.25rem;
    }
    
    .header-actions .btn span {
        display: none;
    }
    
    .header-actions .btn i {
        font-size: 0.8rem;
    }
}
