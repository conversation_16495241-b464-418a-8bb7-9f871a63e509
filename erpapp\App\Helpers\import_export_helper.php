<?php
/**
 * Import/Export Helper Functions
 * مساعد الاستيراد والتصدير - نظام احترافي
 */

/**
 * تصدير البيانات إلى Excel
 */
function export_to_excel($data, $columns, $filename, $title = '')
{
    require_once BASE_PATH . '/vendor/autoload.php';
    
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // إعداد العنوان
    if ($title) {
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:' . chr(65 + count($columns) - 1) . '1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        $headerRow = 3;
    } else {
        $headerRow = 1;
    }
    
    // إعداد رؤوس الأعمدة
    $col = 'A';
    foreach ($columns as $column) {
        $sheet->setCellValue($col . $headerRow, $column['title']);
        $sheet->getStyle($col . $headerRow)->getFont()->setBold(true);
        $sheet->getStyle($col . $headerRow)->getFill()
              ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
              ->getStartColor()->setRGB('E3F2FD');
        $col++;
    }
    
    // إضافة البيانات
    $row = $headerRow + 1;
    foreach ($data as $item) {
        $col = 'A';
        foreach ($columns as $column) {
            $value = $item[$column['field']] ?? '';
            
            // معالجة أنواع البيانات المختلفة
            switch ($column['type'] ?? 'text') {
                case 'number':
                case 'currency':
                    $sheet->setCellValue($col . $row, (float)$value);
                    break;
                case 'date':
                    if ($value) {
                        $sheet->setCellValue($col . $row, \PhpOffice\PhpSpreadsheet\Shared\Date::PHPToExcel(strtotime($value)));
                        $sheet->getStyle($col . $row)->getNumberFormat()->setFormatCode('yyyy-mm-dd');
                    }
                    break;
                default:
                    $sheet->setCellValue($col . $row, $value);
            }
            $col++;
        }
        $row++;
    }
    
    // تنسيق الأعمدة
    foreach (range('A', chr(65 + count($columns) - 1)) as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // إعداد الاستجابة
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
}

/**
 * تصدير البيانات إلى CSV
 */
function export_to_csv($data, $columns, $filename)
{
    header('Content-Type: text/csv; charset=UTF-8');
    header('Content-Disposition: attachment;filename="' . $filename . '.csv"');
    header('Cache-Control: max-age=0');
    
    // إضافة BOM للدعم العربي
    echo "\xEF\xBB\xBF";
    
    $output = fopen('php://output', 'w');
    
    // رؤوس الأعمدة
    $headers = array_column($columns, 'title');
    fputcsv($output, $headers);
    
    // البيانات
    foreach ($data as $item) {
        $row = [];
        foreach ($columns as $column) {
            $row[] = $item[$column['field']] ?? '';
        }
        fputcsv($output, $row);
    }
    
    fclose($output);
    exit;
}

/**
 * قراءة ملف Excel/CSV للاستيراد
 */
function read_import_file($file_path, $file_type = 'auto')
{
    // التحقق من وجود الملف
    if (!file_exists($file_path)) {
        return [
            'success' => false,
            'error' => 'الملف غير موجود: ' . $file_path
        ];
    }

    // التحقق من إمكانية القراءة
    if (!is_readable($file_path)) {
        return [
            'success' => false,
            'error' => 'لا يمكن قراءة الملف: ' . $file_path
        ];
    }

    // تحديد نوع الملف بناءً على الامتداد
    $extension = strtolower(pathinfo($file_path, PATHINFO_EXTENSION));

    try {
        if ($extension === 'csv') {
            // قراءة ملف CSV
            return read_csv_file($file_path);
        } else {
            // محاولة قراءة ملف Excel باستخدام PhpSpreadsheet
            return read_excel_file($file_path);
        }

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ عام: ' . $e->getMessage()
        ];
    }
}

/**
 * قراءة ملف CSV
 */
function read_csv_file($file_path)
{
    try {
        $data = [];
        $headers = [];
        $rowIndex = 0;

        // فتح الملف للقراءة
        if (($handle = fopen($file_path, 'r')) !== FALSE) {
            // تعيين الترميز للعربية
            $content = file_get_contents($file_path);

            // إزالة BOM إذا كان موجوداً
            $content = str_replace("\xEF\xBB\xBF", '', $content);

            // تقسيم المحتوى إلى أسطر
            $lines = explode("\n", $content);

            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) continue;

                // تحليل السطر
                $rowData = str_getcsv($line);

                // تنظيف البيانات
                $rowData = array_map(function($value) {
                    return trim($value, " \t\n\r\0\x0B\"'");
                }, $rowData);

                if ($rowIndex === 0) {
                    // رؤوس الأعمدة
                    $headers = array_filter($rowData, function($val) { return !empty($val); });
                    if (empty($headers)) {
                        return [
                            'success' => false,
                            'error' => 'لم يتم العثور على رؤوس أعمدة صحيحة في الصف الأول'
                        ];
                    }
                } else {
                    // بيانات الصفوف
                    if (count($rowData) >= count($headers)) {
                        $rowData = array_slice($rowData, 0, count($headers));
                        $data[] = array_combine($headers, $rowData);
                    }
                }
                $rowIndex++;
            }

            fclose($handle);
        } else {
            return [
                'success' => false,
                'error' => 'لا يمكن فتح ملف CSV للقراءة'
            ];
        }

        if (empty($data)) {
            return [
                'success' => false,
                'error' => 'الملف فارغ أو لا يحتوي على بيانات صحيحة'
            ];
        }

        return [
            'success' => true,
            'headers' => array_values($headers),
            'data' => $data,
            'total_rows' => count($data)
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ في قراءة ملف CSV: ' . $e->getMessage()
        ];
    }
}

/**
 * قراءة ملف Excel باستخدام PhpSpreadsheet
 */
function read_excel_file($file_path)
{
    // تحميل مكتبة PhpSpreadsheet
    $autoload_path = BASE_PATH . '/vendor/autoload.php';
    if (!file_exists($autoload_path)) {
        return [
            'success' => false,
            'error' => 'مكتبة PhpSpreadsheet غير مثبتة. يرجى استخدام ملفات CSV أو تثبيت المكتبة: composer install'
        ];
    }

    require_once $autoload_path;

    try {
        // التحقق من توفر الفئات المطلوبة
        if (!class_exists('\PhpOffice\PhpSpreadsheet\IOFactory')) {
            return [
                'success' => false,
                'error' => 'مكتبة PhpSpreadsheet غير محملة بشكل صحيح. يرجى استخدام ملفات CSV'
            ];
        }

        // تحديد نوع الملف
        $file_type = \PhpOffice\PhpSpreadsheet\IOFactory::identify($file_path);

        // إنشاء القارئ
        $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader($file_type);
        $reader->setReadDataOnly(true);

        // تحميل الملف
        $spreadsheet = $reader->load($file_path);
        $worksheet = $spreadsheet->getActiveSheet();

        $data = [];
        $headers = [];
        $rowIndex = 0;

        // قراءة البيانات
        foreach ($worksheet->getRowIterator() as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);

            $rowData = [];
            foreach ($cellIterator as $cell) {
                $value = $cell->getCalculatedValue();
                // تنظيف القيم الفارغة
                $rowData[] = $value === null ? '' : trim((string)$value);
            }

            // تجاهل الصفوف الفارغة تماماً
            if (array_filter($rowData, function($val) { return !empty($val); })) {
                if ($rowIndex === 0) {
                    $headers = array_filter($rowData, function($val) { return !empty($val); });
                    if (empty($headers)) {
                        return [
                            'success' => false,
                            'error' => 'لم يتم العثور على رؤوس أعمدة صحيحة في الصف الأول'
                        ];
                    }
                } else {
                    // التأكد من تطابق عدد الأعمدة
                    if (count($rowData) >= count($headers)) {
                        $rowData = array_slice($rowData, 0, count($headers));
                        $data[] = array_combine($headers, $rowData);
                    }
                }
            }
            $rowIndex++;
        }

        if (empty($data)) {
            return [
                'success' => false,
                'error' => 'الملف فارغ أو لا يحتوي على بيانات صحيحة'
            ];
        }

        return [
            'success' => true,
            'headers' => array_values($headers),
            'data' => $data,
            'total_rows' => count($data)
        ];

    } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ في قراءة ملف Excel: ' . $e->getMessage()
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => 'خطأ عام: ' . $e->getMessage()
        ];
    }
}

/**
 * معاينة البيانات المستوردة
 */
function preview_import_data($data, $limit = 10)
{
    return [
        'preview' => array_slice($data, 0, $limit),
        'total_rows' => count($data),
        'showing_rows' => min($limit, count($data))
    ];
}

/**
 * تطابق أعمدة الاستيراد مع أعمدة الجدول
 */
function match_import_columns($import_headers, $table_columns)
{
    $matches = [];
    $suggestions = [];
    
    foreach ($import_headers as $import_header) {
        $best_match = null;
        $best_score = 0;
        
        foreach ($table_columns as $table_column) {
            // مقارنة بسيطة بالاسم
            $score = similar_text(
                strtolower($import_header), 
                strtolower($table_column['title']), 
                $percent
            );
            
            if ($percent > $best_score) {
                $best_score = $percent;
                $best_match = $table_column['field'];
            }
        }
        
        $matches[$import_header] = $best_match;
        $suggestions[$import_header] = $best_score;
    }
    
    return [
        'matches' => $matches,
        'suggestions' => $suggestions
    ];
}

/**
 * التحقق من صحة البيانات المستوردة
 */
function validate_import_data($data, $column_mapping, $validation_rules = [])
{
    $errors = [];
    $valid_data = [];
    
    foreach ($data as $index => $row) {
        $row_errors = [];
        $mapped_row = [];
        
        foreach ($column_mapping as $import_field => $table_field) {
            if (!$table_field) continue;
            
            $value = $row[$import_field] ?? '';
            
            // تطبيق قواعد التحقق
            if (isset($validation_rules[$table_field])) {
                $rules = $validation_rules[$table_field];
                
                if (isset($rules['required']) && $rules['required'] && empty($value)) {
                    $row_errors[] = "الحقل {$table_field} مطلوب في الصف " . ($index + 1);
                }
                
                if (isset($rules['type']) && !empty($value)) {
                    switch ($rules['type']) {
                        case 'email':
                            if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                                $row_errors[] = "البريد الإلكتروني غير صحيح في الصف " . ($index + 1);
                            }
                            break;
                        case 'number':
                            if (!is_numeric($value)) {
                                $row_errors[] = "القيمة يجب أن تكون رقم في الصف " . ($index + 1);
                            }
                            break;
                    }
                }
            }
            
            $mapped_row[$table_field] = $value;
        }
        
        if (empty($row_errors)) {
            $valid_data[] = $mapped_row;
        } else {
            $errors = array_merge($errors, $row_errors);
        }
    }
    
    return [
        'valid_data' => $valid_data,
        'errors' => $errors,
        'total_rows' => count($data),
        'valid_rows' => count($valid_data),
        'error_rows' => count($data) - count($valid_data)
    ];
}

/**
 * حفظ البيانات المستوردة
 */
function save_imported_data($model, $data, $company_id, $user_id)
{
    $success_count = 0;
    $error_count = 0;
    $errors = [];
    
    foreach ($data as $index => $row) {
        try {
            $row['company_id'] = $company_id;
            $row['created_by'] = $user_id;
            
            $result = $model->create($row);
            if ($result) {
                $success_count++;
            } else {
                $error_count++;
                $errors[] = "فشل في حفظ الصف " . ($index + 1);
            }
        } catch (Exception $e) {
            $error_count++;
            $errors[] = "خطأ في الصف " . ($index + 1) . ": " . $e->getMessage();
        }
    }
    
    return [
        'success_count' => $success_count,
        'error_count' => $error_count,
        'errors' => $errors,
        'total_processed' => count($data)
    ];
}

/**
 * إنشاء قالب Excel للاستيراد
 */
function create_import_template($columns, $filename, $title = '')
{
    require_once BASE_PATH . '/vendor/autoload.php';
    
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    
    // إعداد العنوان
    if ($title) {
        $sheet->setCellValue('A1', $title . ' - قالب الاستيراد');
        $sheet->mergeCells('A1:' . chr(65 + count($columns) - 1) . '1');
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('A1')->getAlignment()->setHorizontal('center');
        $headerRow = 3;
    } else {
        $headerRow = 1;
    }
    
    // إعداد رؤوس الأعمدة
    $col = 'A';
    foreach ($columns as $column) {
        $sheet->setCellValue($col . $headerRow, $column['title']);
        $sheet->getStyle($col . $headerRow)->getFont()->setBold(true);
        $sheet->getStyle($col . $headerRow)->getFill()
              ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
              ->getStartColor()->setRGB('E8F5E8');
        
        // إضافة تعليق للحقول المطلوبة
        if (isset($column['required']) && $column['required']) {
            $sheet->getComment($col . $headerRow)->getText()->createTextRun('حقل مطلوب');
        }
        
        $col++;
    }
    
    // تنسيق الأعمدة
    foreach (range('A', chr(65 + count($columns) - 1)) as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }
    
    // إعداد الاستجابة
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '_template.xlsx"');
    header('Cache-Control: max-age=0');
    
    $writer = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($spreadsheet);
    $writer->save('php://output');
    exit;
}
