<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص Flatpickr</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            padding: 2rem;
        }
        
        .debug-section {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        .status {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            margin: 0.5rem 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            overflow-x: auto;
        }
        
        .test-input {
            margin: 1rem 0;
        }
    </style>
</head>
<body>

<div class="container">
    <h1>🔍 تشخيص Flatpickr</h1>
    
    <!-- حالة المكتبات -->
    <div class="debug-section">
        <h2>📚 حالة المكتبات</h2>
        <div id="library-status"></div>
    </div>
    
    <!-- اختبارات التواريخ -->
    <div class="debug-section">
        <h2>📅 اختبارات التواريخ</h2>
        
        <div class="test-input">
            <label>تاريخ بسيط:</label>
            <input type="text" id="simple-date" class="form-control" placeholder="اختر تاريخ">
        </div>
        
        <div class="test-input">
            <label>تاريخ ووقت:</label>
            <input type="text" id="datetime" class="form-control" placeholder="اختر تاريخ ووقت">
        </div>
        
        <div class="test-input">
            <label>نطاق تواريخ:</label>
            <input type="text" id="date-range" class="form-control" placeholder="اختر نطاق تواريخ">
        </div>
        
        <button onclick="testDatePickers()" class="btn btn-primary">اختبار منتقيات التواريخ</button>
    </div>
    
    <!-- سجل الأحداث -->
    <div class="debug-section">
        <h2>📝 سجل الأحداث</h2>
        <div id="event-log"></div>
    </div>
    
    <!-- معلومات تقنية -->
    <div class="debug-section">
        <h2>🔧 معلومات تقنية</h2>
        <div id="technical-info"></div>
    </div>
</div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

<script>
// سجل الأحداث
const eventLog = [];

function logEvent(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    eventLog.push({ timestamp, message, type });
    updateEventLog();
    console.log(`[${timestamp}] ${message}`);
}

function updateEventLog() {
    const logDiv = document.getElementById('event-log');
    const html = eventLog.map(event => 
        `<div class="status ${event.type}">[${event.timestamp}] ${event.message}</div>`
    ).join('');
    logDiv.innerHTML = html;
}

// فحص حالة المكتبات
function checkLibraryStatus() {
    const statusDiv = document.getElementById('library-status');
    let html = '';
    
    // فحص jQuery
    if (typeof $ !== 'undefined') {
        html += '<div class="status success">✅ jQuery متاح - الإصدار: ' + $.fn.jquery + '</div>';
        logEvent('jQuery متاح - الإصدار: ' + $.fn.jquery, 'success');
    } else {
        html += '<div class="status error">❌ jQuery غير متاح</div>';
        logEvent('jQuery غير متاح', 'error');
    }
    
    // فحص Flatpickr
    if (typeof flatpickr !== 'undefined') {
        html += '<div class="status success">✅ Flatpickr متاح</div>';
        logEvent('Flatpickr متاح', 'success');
        
        // فحص اللغة العربية
        if (flatpickr.l10ns && flatpickr.l10ns.ar) {
            html += '<div class="status success">✅ اللغة العربية متاحة</div>';
            logEvent('اللغة العربية متاحة', 'success');
        } else {
            html += '<div class="status warning">⚠️ اللغة العربية غير متاحة</div>';
            logEvent('اللغة العربية غير متاحة', 'warning');
        }
    } else {
        html += '<div class="status error">❌ Flatpickr غير متاح</div>';
        logEvent('Flatpickr غير متاح', 'error');
    }
    
    statusDiv.innerHTML = html;
}

// اختبار منتقيات التواريخ
function testDatePickers() {
    logEvent('بدء اختبار منتقيات التواريخ');
    
    if (typeof flatpickr === 'undefined') {
        logEvent('لا يمكن اختبار منتقيات التواريخ - Flatpickr غير متاح', 'error');
        return;
    }
    
    try {
        // تاريخ بسيط
        const simpleDatePicker = flatpickr("#simple-date", {
            locale: flatpickr.l10ns.ar || 'default',
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "d/m/Y",
            allowInput: true,
            clickOpens: true,
            onReady: function() {
                logEvent('تم تهيئة منتقي التاريخ البسيط', 'success');
            },
            onChange: function(selectedDates, dateStr) {
                logEvent('تم تغيير التاريخ البسيط: ' + dateStr, 'success');
            }
        });
        
        // تاريخ ووقت
        const datetimePicker = flatpickr("#datetime", {
            locale: flatpickr.l10ns.ar || 'default',
            enableTime: true,
            dateFormat: "Y-m-d H:i",
            altInput: true,
            altFormat: "d/m/Y H:i",
            time_24hr: true,
            allowInput: true,
            clickOpens: true,
            onReady: function() {
                logEvent('تم تهيئة منتقي التاريخ والوقت', 'success');
            },
            onChange: function(selectedDates, dateStr) {
                logEvent('تم تغيير التاريخ والوقت: ' + dateStr, 'success');
            }
        });
        
        // نطاق تواريخ
        const rangePicker = flatpickr("#date-range", {
            locale: flatpickr.l10ns.ar || 'default',
            mode: "range",
            dateFormat: "Y-m-d",
            altInput: true,
            altFormat: "d/m/Y",
            allowInput: true,
            clickOpens: true,
            onReady: function() {
                logEvent('تم تهيئة منتقي نطاق التواريخ', 'success');
            },
            onChange: function(selectedDates, dateStr) {
                logEvent('تم تغيير نطاق التواريخ: ' + dateStr, 'success');
            }
        });
        
        logEvent('تم إنشاء جميع منتقيات التواريخ بنجاح', 'success');
        
    } catch (error) {
        logEvent('خطأ في إنشاء منتقيات التواريخ: ' + error.message, 'error');
    }
}

// عرض المعلومات التقنية
function showTechnicalInfo() {
    const infoDiv = document.getElementById('technical-info');
    
    const info = {
        'User Agent': navigator.userAgent,
        'Language': navigator.language,
        'Platform': navigator.platform,
        'Screen Resolution': screen.width + 'x' + screen.height,
        'Viewport Size': window.innerWidth + 'x' + window.innerHeight,
        'Document Ready State': document.readyState,
        'Current URL': window.location.href
    };
    
    let html = '<pre>';
    for (const [key, value] of Object.entries(info)) {
        html += `${key}: ${value}\n`;
    }
    html += '</pre>';
    
    infoDiv.innerHTML = html;
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    logEvent('تم تحميل الصفحة');
    
    // انتظار قليل للتأكد من تحميل المكتبات
    setTimeout(function() {
        checkLibraryStatus();
        showTechnicalInfo();
        
        // اختبار تلقائي
        if (typeof flatpickr !== 'undefined') {
            testDatePickers();
        }
    }, 500);
});

// فحص دوري
setInterval(function() {
    if (typeof flatpickr !== 'undefined' && !window.flatpickrChecked) {
        window.flatpickrChecked = true;
        logEvent('تم اكتشاف Flatpickr متأخراً', 'warning');
        checkLibraryStatus();
        testDatePickers();
    }
}, 1000);
</script>

</body>
</html>
