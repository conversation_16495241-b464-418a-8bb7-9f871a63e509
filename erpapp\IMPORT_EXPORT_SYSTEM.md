# 📊 نظام الاستيراد والتصدير الاحترافي

## 🎯 نظرة عامة

تم إنشاء نظام استيراد وتصدير احترافي متكامل للموردين مع دعم كامل لملفات Excel و CSV، ومعاينة البيانات، وتطابق الأعمدة التلقائي.

## 🚀 المميزات

### 📤 التصدير
- **Excel (.xlsx)** - تصدير احترافي مع تنسيق وألوان
- **CSV** - تصدير مع دعم العربية (UTF-8 BOM)
- **PDF** - جاهز للتطوير المستقبلي

### 📥 الاستيراد
- **معاينة البيانات** - عرض أول 10 صفوف قبل الاستيراد
- **تطابق الأعمدة** - تطابق تلقائي ذكي بين أعمدة الملف والجدول
- **التحقق من البيانات** - فحص شامل للبيانات قبل الحفظ
- **تقارير مفصلة** - عدد الصفوف المستوردة والأخطاء

### 🎨 واجهة المستخدم
- **أزرار احترافية** - تصميم متقدم مع تأثيرات بصرية
- **نافذة منبثقة متقدمة** - 3 خطوات واضحة للاستيراد
- **سحب وإفلات** - رفع الملفات بسهولة
- **معاينة تفاعلية** - عرض البيانات في جدول منسق

## 📁 الملفات المنشأة

### 🔧 Backend
```
erpapp/App/Helpers/import_export_helper.php          # مساعد الاستيراد والتصدير
erpapp/App/Controllers/Purchases/SuppliersImportExportController.php  # متحكم الاستيراد/التصدير
```

### 🎨 Frontend
```
erpapp/public/css/components/import-export.css       # تصميم الواجهة
erpapp/public/js/import-export.js                    # وظائف JavaScript
```

### 📂 Storage
```
erpapp/storage/uploads/temp/                         # مجلد الملفات المؤقتة
```

## 🔗 الروابط المضافة

```php
// في App/Modules/purchases/Module.php
add_route('GET', '/purchases/suppliers/export/excel', '...');
add_route('GET', '/purchases/suppliers/export/csv', '...');
add_route('GET', '/purchases/suppliers/download-template', '...');
add_route('POST', '/purchases/suppliers/import-preview', '...');
add_route('GET', '/purchases/suppliers/get-table-columns', '...');
add_route('POST', '/purchases/suppliers/import', '...');
```

## 📦 المكتبات المضافة

```json
{
  "require": {
    "phpoffice/phpspreadsheet": "^1.29"
  }
}
```

## 🛠️ التثبيت

1. **تثبيت المكتبات:**
```bash
composer update
```

2. **إنشاء المجلدات:**
```bash
mkdir -p erpapp/storage/uploads/temp
chmod 755 erpapp/storage/uploads/temp
```

3. **تحديث الملفات:**
- تم تحديث `css_helper.php` لتحميل `import-export.css`
- تم تحديث `js_helper.php` لتحميل `import-export.js`
- تم تحديث `loader.php` لتحميل `import_export_helper.php`

## 🎮 الاستخدام

### 📤 التصدير

1. انتقل إلى `/purchases/suppliers`
2. انقر على زر **"تصدير"**
3. اختر النوع المطلوب:
   - **Excel** - ملف .xlsx منسق
   - **CSV** - ملف .csv مع دعم العربية

### 📥 الاستيراد

1. انقر على زر **"استيراد"**
2. **الخطوة 1:** اختر الملف (Excel أو CSV)
3. **الخطوة 2:** معاينة البيانات
4. **الخطوة 3:** تطابق الأعمدة
5. انقر **"استيراد البيانات"**

### 📋 تحميل القالب

1. انقر على زر **"قالب"**
2. سيتم تحميل ملف Excel فارغ مع الأعمدة المطلوبة

## 🔧 التخصيص

### إضافة أعمدة جديدة

في `SuppliersImportExportController.php`:

```php
$columns = [
    ['field' => 'G_name_ar', 'title' => 'اسم المورد', 'required' => true],
    ['field' => 'new_field', 'title' => 'حقل جديد', 'required' => false],
    // ...
];
```

### تخصيص التحقق من البيانات

```php
$validationRules = [
    'G_name_ar' => ['required' => true],
    'S_email' => ['type' => 'email'],
    'new_field' => ['type' => 'number', 'min' => 0],
];
```

## 🎨 التصميم

### الألوان المستخدمة
- **تصدير:** أخضر (`--success-color`)
- **استيراد:** أزرق (`--primary-color`) 
- **قالب:** سماوي (`--info-color`)

### الأيقونات
- **تصدير:** `fas fa-download`
- **استيراد:** `fas fa-upload`
- **قالب:** `fas fa-file-download`

## 🧪 الاختبار

قم بتشغيل ملف الاختبار:
```
http://your-domain/erpapp/test_import_export.php
```

## 🔒 الأمان

- ✅ فحص نوع الملفات المرفوعة
- ✅ فحص حجم الملفات (5MB كحد أقصى)
- ✅ تنظيف الملفات المؤقتة
- ✅ التحقق من صلاحيات المستخدم
- ✅ فحص البيانات قبل الحفظ

## 📈 الأداء

- **معاينة محدودة:** أول 10 صفوف فقط
- **معالجة تدريجية:** للملفات الكبيرة
- **ذاكرة محسنة:** تنظيف الملفات المؤقتة
- **استجابة سريعة:** واجهة تفاعلية

## 🎉 النتيجة

تم إنشاء نظام استيراد وتصدير احترافي متكامل يوفر:

1. **سهولة الاستخدام** - واجهة بديهية وواضحة
2. **مرونة عالية** - دعم متعدد الصيغ والتخصيص
3. **أمان متقدم** - فحص شامل للبيانات والملفات
4. **أداء محسن** - معالجة ذكية وسريعة
5. **تصميم احترافي** - واجهة جميلة ومتجاوبة

النظام جاهز للاستخدام الفوري! 🚀
