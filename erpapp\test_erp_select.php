<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مكتبة ERP Select المتقدمة</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- ERP Select CSS -->
    <link rel="stylesheet" href="public/css/libs/erp-select.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: '<PERSON><PERSON><PERSON>', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: #1e293b;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .header p {
            font-size: 1.1rem;
            color: #64748b;
            margin-bottom: 2rem;
        }
        
        .theme-toggle {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .demo-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
        }
        
        .demo-item h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #374151;
        }
        
        .demo-item label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #4b5563;
        }
        
        .demo-item select {
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .demo-item .description {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 0.5rem;
            line-height: 1.5;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .feature-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }
        
        .feature-card i {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: #1e293b;
        }
        
        .feature-card p {
            font-size: 0.9rem;
            color: #64748b;
            line-height: 1.5;
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        [data-theme="dark"] {
            color: #f1f5f9;
        }
        
        [data-theme="dark"] .demo-section,
        [data-theme="dark"] .header {
            background: rgba(30, 41, 59, 0.95);
            color: #f1f5f9;
        }
        
        [data-theme="dark"] .demo-item {
            background: #334155;
            border-color: #475569;
        }
        
        [data-theme="dark"] .demo-item h3,
        [data-theme="dark"] .demo-item label {
            color: #f1f5f9;
        }
        
        [data-theme="dark"] .feature-card {
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.3);
        }
        
        [data-theme="dark"] .feature-card h3 {
            color: #f1f5f9;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🚀 مكتبة ERP Select المتقدمة</h1>
        <p>مكتبة Select احترافية مصممة خصيص<|im_start|> لأنظمة ERP مع مميزات متقدمة وتصميم أنيق</p>
        
        <button onclick="toggleTheme()" class="theme-toggle">
            <i class="fas fa-moon" id="theme-icon"></i>
            <span id="theme-text">الوضع الداكن</span>
        </button>
    </div>

    <!-- Controls -->
    <div class="controls">
        <button class="btn btn-primary" onclick="refreshAll()">
            <i class="fas fa-sync"></i>
            تحديث الكل
        </button>
        <button class="btn btn-secondary" onclick="logInstances()">
            <i class="fas fa-list"></i>
            عرض المثيلات
        </button>
        <button class="btn btn-secondary" onclick="testAPI()">
            <i class="fas fa-code"></i>
            اختبار API
        </button>
    </div>

    <!-- Basic Examples -->
    <div class="demo-section">
        <h2><i class="fas fa-play-circle"></i> أمثلة أساسية</h2>
        <div class="demo-grid">
            
            <div class="demo-item">
                <h3>Select بسيط</h3>
                <label>اختر خيار:</label>
                <select data-erp-select data-placeholder="اختر خيار...">
                    <option value="">اختر خيار...</option>
                    <option value="1">الخيار الأول</option>
                    <option value="2">الخيار الثاني</option>
                    <option value="3">الخيار الثالث</option>
                </select>
                <div class="description">Select بسيط مع تصميم أنيق وتأثيرات متقدمة</div>
            </div>
            
            <div class="demo-item">
                <h3>Select مع بحث</h3>
                <label>ابحث واختر:</label>
                <select data-erp-select data-searchable="true" data-placeholder="ابحث واختر...">
                    <option value="">ابحث واختر...</option>
                    <option value="apple">تفاح</option>
                    <option value="banana">موز</option>
                    <option value="orange">برتقال</option>
                    <option value="grape">عنب</option>
                    <option value="mango">مانجو</option>
                    <option value="strawberry">فراولة</option>
                </select>
                <div class="description">Select مع إمكانية البحث السريع في الخيارات</div>
            </div>
            
            <div class="demo-item">
                <h3>Select متعدد الاختيار</h3>
                <label>اختر عدة خيارات:</label>
                <select data-erp-select data-multiple="true" data-placeholder="اختر عدة خيارات..." multiple>
                    <option value="html">HTML</option>
                    <option value="css">CSS</option>
                    <option value="js">JavaScript</option>
                    <option value="php">PHP</option>
                    <option value="python">Python</option>
                    <option value="java">Java</option>
                </select>
                <div class="description">Select متعدد الاختيار مع عداد ذكي</div>
            </div>
            
            <div class="demo-item">
                <h3>Select مع مسح</h3>
                <label>قابل للمسح:</label>
                <select data-erp-select data-clearable="true" data-placeholder="اختر وامسح...">
                    <option value="">اختر وامسح...</option>
                    <option value="red">أحمر</option>
                    <option value="blue">أزرق</option>
                    <option value="green">أخضر</option>
                    <option value="yellow">أصفر</option>
                </select>
                <div class="description">Select مع زر مسح سريع للتحديد</div>
            </div>
            
        </div>
    </div>

    <!-- Advanced Examples -->
    <div class="demo-section">
        <h2><i class="fas fa-cogs"></i> أمثلة متقدمة</h2>
        <div class="demo-grid">
            
            <div class="demo-item">
                <h3>Select مع AJAX</h3>
                <label>بحث مع AJAX:</label>
                <select data-erp-select 
                        data-ajax-url="api/countries" 
                        data-searchable="true"
                        data-placeholder="ابحث عن البلد...">
                    <option value="">ابحث عن البلد...</option>
                </select>
                <div class="description">Select مع تحميل البيانات من الخادم عبر AJAX</div>
            </div>
            
            <div class="demo-item">
                <h3>Select للفلاتر</h3>
                <label>فلتر الحالة:</label>
                <select data-erp-select 
                        data-filter-type="status"
                        data-theme="primary"
                        data-placeholder="جميع الحالات">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="pending">في الانتظار</option>
                </select>
                <div class="description">Select مخصص للفلاتر مع تصميم مميز</div>
            </div>
            
            <div class="demo-item">
                <h3>أحجام مختلفة</h3>
                <label>حجم صغير:</label>
                <select data-erp-select data-size="small" data-placeholder="حجم صغير">
                    <option value="">حجم صغير</option>
                    <option value="1">خيار 1</option>
                    <option value="2">خيار 2</option>
                </select>
                
                <label style="margin-top: 1rem;">حجم كبير:</label>
                <select data-erp-select data-size="large" data-placeholder="حجم كبير">
                    <option value="">حجم كبير</option>
                    <option value="1">خيار 1</option>
                    <option value="2">خيار 2</option>
                </select>
                <div class="description">أحجام مختلفة للSelect حسب الحاجة</div>
            </div>
            
            <div class="demo-item">
                <h3>ثيمات ملونة</h3>
                <label>ثيم النجاح:</label>
                <select data-erp-select data-theme="success" data-placeholder="ثيم أخضر">
                    <option value="">ثيم أخضر</option>
                    <option value="1">خيار 1</option>
                    <option value="2">خيار 2</option>
                </select>
                
                <label style="margin-top: 1rem;">ثيم التحذير:</label>
                <select data-erp-select data-theme="warning" data-placeholder="ثيم أصفر">
                    <option value="">ثيم أصفر</option>
                    <option value="1">خيار 1</option>
                    <option value="2">خيار 2</option>
                </select>
                <div class="description">ثيمات ملونة مختلفة للاستخدامات المتنوعة</div>
            </div>
            
        </div>
    </div>

    <!-- Features -->
    <div class="demo-section">
        <h2><i class="fas fa-star"></i> المميزات الرئيسية</h2>
        <div class="features-grid">
            
            <div class="feature-card">
                <i class="fas fa-search"></i>
                <h3>بحث متقدم</h3>
                <p>بحث سريع وذكي في الخيارات مع دعم AJAX</p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-mobile-alt"></i>
                <h3>تصميم متجاوب</h3>
                <p>يعمل بشكل مثالي على جميع الأجهزة والشاشات</p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-palette"></i>
                <h3>ثيمات متعددة</h3>
                <p>ثيمات ملونة وأحجام مختلفة للاستخدامات المتنوعة</p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-universal-access"></i>
                <h3>إمكانية الوصول</h3>
                <p>دعم كامل لقارئات الشاشة ولوحة المفاتيح</p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-language"></i>
                <h3>دعم RTL</h3>
                <p>دعم كامل للغة العربية والاتجاه من اليمين لليسار</p>
            </div>
            
            <div class="feature-card">
                <i class="fas fa-rocket"></i>
                <h3>أداء عالي</h3>
                <p>محسن للأداء مع تحميل ذكي وذاكرة تخزين مؤقت</p>
            </div>
            
        </div>
    </div>

</div>

<!-- ERP Select JS -->
<script src="public/js/libs/erp-select.js"></script>
<script src="public/js/libs/erp-select-init.js"></script>

<script>
// وظائف التحكم
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    document.documentElement.setAttribute('data-theme', newTheme);
    
    const icon = document.getElementById('theme-icon');
    const text = document.getElementById('theme-text');
    
    if (newTheme === 'dark') {
        icon.className = 'fas fa-sun';
        text.textContent = 'الوضع الفاتح';
    } else {
        icon.className = 'fas fa-moon';
        text.textContent = 'الوضع الداكن';
    }
    
    // إطلاق حدث تغيير الثيم
    document.dispatchEvent(new CustomEvent('theme-changed', {
        detail: { theme: newTheme }
    }));
    
    localStorage.setItem('theme', newTheme);
}

function refreshAll() {
    if (window.ERPSelectManager) {
        window.ERPSelectManager.refreshAll();
        console.log('تم تحديث جميع المثيلات');
    }
}

function logInstances() {
    if (window.ERPSelectManager) {
        console.log('مثيلات ERP Select:', window.ERPSelectManager.instances);
        alert(`عدد المثيلات النشطة: ${window.ERPSelectManager.instances.size}`);
    }
}

function testAPI() {
    // اختبار API
    const select = document.querySelector('select[data-ajax-url]');
    const instance = window.ERPSelectManager.getInstance(select);
    
    if (instance) {
        console.log('قيمة Select:', instance.getValue());
        console.log('خيارات Select:', instance.filteredOptions);
        
        // اختبار تعيين قيمة
        instance.setValue('SA');
        
        alert('تم اختبار API - راجع Console للتفاصيل');
    }
}

// تحميل الثيم المحفوظ
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        if (savedTheme === 'dark') {
            document.getElementById('theme-icon').className = 'fas fa-sun';
            document.getElementById('theme-text').textContent = 'الوضع الفاتح';
        }
    }
    
    console.log('🎉 تم تحميل صفحة اختبار ERP Select');
});

// محاكاة API للبلدان
if (!window.apiMocked) {
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        if (url.includes('api/countries')) {
            const searchParams = new URLSearchParams(url.split('?')[1]);
            const search = searchParams.get('search') || '';
            
            const countries = [
                {id: 'SA', text: 'المملكة العربية السعودية'},
                {id: 'AE', text: 'الإمارات العربية المتحدة'},
                {id: 'KW', text: 'الكويت'},
                {id: 'QA', text: 'قطر'},
                {id: 'BH', text: 'البحرين'},
                {id: 'OM', text: 'عمان'},
                {id: 'JO', text: 'الأردن'},
                {id: 'LB', text: 'لبنان'},
                {id: 'EG', text: 'مصر'},
                {id: 'MA', text: 'المغرب'}
            ];
            
            const filtered = countries.filter(country => 
                country.text.includes(search)
            );
            
            return Promise.resolve({
                ok: true,
                status: 200,
                json: () => Promise.resolve({
                    results: filtered
                })
            });
        }
        
        return originalFetch.apply(this, arguments);
    };
    
    window.apiMocked = true;
}
</script>

</body>
</html>
