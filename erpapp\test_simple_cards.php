<?php
/**
 * اختبار النسخة المبسطة من cards_helper.php
 */

echo "<h1>🧪 اختبار النسخة المبسطة</h1>";

// تحميل الملفات الأساسية
require_once 'App/Config/config.php';
require_once 'App/Helpers/functions.php';

// اختبار تحميل النسخة المبسطة
echo "<h2>1️⃣ اختبار تحميل cards_helper_simple.php:</h2>";

try {
    require_once 'App/Helpers/cards_helper_simple.php';
    echo "<p>✅ تم تحميل cards_helper_simple.php بنجاح</p>";
} catch (ParseError $e) {
    echo "<p>❌ خطأ في بناء الجمل: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p>السطر: " . $e->getLine() . "</p>";
    exit;
} catch (Error $e) {
    echo "<p>❌ خطأ فادح: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
} catch (Exception $e) {
    echo "<p>❌ خطأ: " . htmlspecialchars($e->getMessage()) . "</p>";
    exit;
}

// اختبار وجود الدوال
echo "<h2>2️⃣ اختبار وجود الدوال:</h2>";
$functions = [
    'render_cards_page',
    'render_cards_layout', 
    'render_cards_header',
    'render_stats_cards',
    'render_cards_toolbar',
    'render_cards_scripts',
    'create_stat_card'
];

foreach ($functions as $func) {
    $status = function_exists($func) ? '✅' : '❌';
    echo "<p>$status $func</p>";
}

// اختبار إنشاء بطاقة
echo "<h2>3️⃣ اختبار إنشاء بطاقة:</h2>";

try {
    $card = create_stat_card('اختبار', 100, 'fas fa-test', 'primary', 'وصف');
    echo "<p>✅ تم إنشاء البطاقة: " . json_encode($card) . "</p>";
} catch (Exception $e) {
    echo "<p>❌ خطأ في إنشاء البطاقة: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// اختبار عرض البطاقات
echo "<h2>4️⃣ اختبار عرض البطاقات:</h2>";

try {
    $stats = [
        [
            'title' => 'اختبار 1',
            'value' => 100,
            'icon' => 'fas fa-test',
            'color' => 'primary'
        ],
        [
            'title' => 'اختبار 2', 
            'value' => 200,
            'icon' => 'fas fa-check',
            'color' => 'success'
        ]
    ];
    
    $actions = [
        [
            'type' => 'primary',
            'url' => 'test',
            'icon' => 'fas fa-plus',
            'text' => 'إضافة'
        ]
    ];
    
    $breadcrumb = [
        ['title' => 'الرئيسية', 'url' => 'home'],
        ['title' => 'اختبار البطاقات', 'active' => true]
    ];
    
    echo "<p>✅ تم إعداد البيانات بنجاح</p>";
    
    // عرض البطاقات
    render_cards_page([
        'title' => 'اختبار نظام البطاقات المبسط',
        'module' => 'test',
        'entity' => 'cards',
        'stats' => $stats,
        'actions' => $actions,
        'breadcrumb' => $breadcrumb
    ]);
    
    echo "<p>✅ تم عرض البطاقات بنجاح</p>";
    
} catch (Exception $e) {
    echo "<p>❌ خطأ في عرض البطاقات: " . htmlspecialchars($e->getMessage()) . "</p>";
} catch (Error $e) {
    echo "<p>❌ خطأ فادح في عرض البطاقات: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr><p><strong>✅ انتهى اختبار النسخة المبسطة</strong></p>";
?>
