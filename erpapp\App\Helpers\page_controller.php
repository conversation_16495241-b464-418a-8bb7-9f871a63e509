<?php
/**
 * Page Controller - نسخة مبسطة وآمنة
 * المتحكم الرئيسي في جميع أنواع الصفحات
 */

/**
 * عرض صفحة ذكية - نسخة مبسطة
 */
function render_page($config)
{
    try {
        // تحديد نوع الصفحة
        $page_type = detect_page_type($config);
        
        // توجيه بسيط
        switch ($page_type) {
            case 'cards':
                if (function_exists('render_cards_page')) {
                    render_cards_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام البطاقات غير متاح</div>';
                }
                break;
                
            case 'datatable':
                if (function_exists('render_datatable_page')) {
                    render_datatable_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام الجداول غير متاح</div>';
                }
                break;
                
            case 'form':
                if (function_exists('render_form_page')) {
                    render_form_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام النماذج غير متاح</div>';
                }
                break;
                
            case 'report':
                if (function_exists('render_report_page')) {
                    render_report_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام التقارير غير متاح</div>';
                }
                break;
                
            default:
                render_simple_page($config);
                break;
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">خطأ: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * تحديد نوع الصفحة
 */
function detect_page_type($config)
{
    // فحص بسيط
    if (!empty($config['form'])) {
        return 'form';
    }
    
    if (!empty($config['charts'])) {
        return 'report';
    }
    
    if (!empty($config['columns'])) {
        return 'datatable';
    }
    
    if (!empty($config['stats'])) {
        return 'cards';
    }
    
    return 'simple';
}

/**
 * عرض صفحة بسيطة
 */
function render_simple_page($config)
{
    $title = $config['title'] ?? 'صفحة بسيطة';
    $content = $config['content'] ?? '<p>محتوى الصفحة</p>';
    $breadcrumb = $config['breadcrumb'] ?? [['title' => $title, 'active' => true]];
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <?php foreach ($breadcrumb as $item): ?>
                                <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                    <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </div>
                    <h4 class="page-title"><?= $title ?></h4>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?= $content ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * التحقق من توفر الأنظمة
 */
function get_available_systems()
{
    return [
        'datatable' => function_exists('render_datatable_page'),
        'cards' => function_exists('render_cards_page'),
        'forms' => function_exists('render_form_page'),
        'reports' => function_exists('render_report_page')
    ];
}

/**
 * التحقق من توفر نظام معين
 */
function is_system_available($system)
{
    $systems = get_available_systems();
    return isset($systems[$system]) && $systems[$system];
}
?>
