<?php
/**
 * Page Controller - نسخة مبسطة وآمنة
 * المتحكم الرئيسي في جميع أنواع الصفحات
 */

/**
 * عرض صفحة ذكية - نسخة مبسطة
 */
function render_page($config)
{
    try {
        // تحديد نوع الصفحة
        $page_type = detect_page_type($config);
        
        // توجيه بسيط
        switch ($page_type) {
            case 'cards':
                if (function_exists('render_cards_page')) {
                    render_cards_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام البطاقات غير متاح</div>';
                }
                break;
                
            case 'datatable':
                if (function_exists('render_datatable_page')) {
                    render_datatable_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام الجداول غير متاح</div>';
                }
                break;
                
            case 'form':
                if (function_exists('render_form_page')) {
                    render_form_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام النماذج غير متاح</div>';
                }
                break;
                
            case 'report':
                if (function_exists('render_report_page')) {
                    render_report_page($config);
                } else {
                    echo '<div class="alert alert-danger">نظام التقارير غير متاح</div>';
                }
                break;

            case 'mixed':
                render_mixed_page($config);
                break;

            default:
                render_simple_page($config);
                break;
        }
    } catch (Exception $e) {
        echo '<div class="alert alert-danger">خطأ: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * تحديد نوع الصفحة
 */
function detect_page_type($config)
{
    // فحص الصفحات المختلطة أولاً
    $has_stats = !empty($config['stats']);
    $has_columns = !empty($config['columns']);
    $has_form = !empty($config['form']);
    $has_charts = !empty($config['charts']);

    // صفحة مختلطة (بطاقات + جداول)
    if ($has_stats && $has_columns) {
        return 'mixed';
    }

    // فحص الأنواع المنفردة
    if ($has_form) {
        return 'form';
    }

    if ($has_charts) {
        return 'report';
    }

    if ($has_columns) {
        return 'datatable';
    }

    if ($has_stats) {
        return 'cards';
    }

    return 'simple';
}

/**
 * عرض صفحة بسيطة
 */
function render_simple_page($config)
{
    $title = $config['title'] ?? 'صفحة بسيطة';
    $content = $config['content'] ?? '<p>محتوى الصفحة</p>';
    $breadcrumb = $config['breadcrumb'] ?? [['title' => $title, 'active' => true]];
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <?php foreach ($breadcrumb as $item): ?>
                                <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                    <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </div>
                    <h4 class="page-title"><?= $title ?></h4>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <?= $content ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض صفحة مختلطة (بطاقات + جداول)
 */
function render_mixed_page($config)
{
    // إعداد المتغيرات الأساسية
    $title = $config['title'] ?? 'صفحة مختلطة';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';

    // البيانات
    $stats = $config['stats'] ?? [];
    $columns = $config['columns'] ?? [];
    $data = $config['data'] ?? [];

    // إعدادات اختيارية
    $actions = $config['actions'] ?? [];
    $pagination = $config['pagination'] ?? [];
    $filters = $config['filters'] ?? [];
    $filters_config = $config['filters_config'] ?? [];

    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // Empty states
    $empty_state = $config['empty_state'] ?? [
        'icon' => 'mdi mdi-database-outline',
        'message' => 'لا توجد بيانات'
    ];

    // تحديد ما يجب عرضه
    $has_stats = !empty($stats);
    $has_table = !empty($columns);
    $has_actions = !empty($actions);
    $has_filters = !empty($filters_config);

    ?>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <?php foreach ($breadcrumb as $item): ?>
                                <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                    <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                                <?php else: ?>
                                    <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </ol>
                    </div>
                    <h4 class="page-title"><?= $title ?></h4>
                </div>
            </div>
        </div>

        <!-- Stats Cards Section -->
        <?php if ($has_stats): ?>
            <?php if (function_exists('render_stats_cards')): ?>
                <?php render_stats_cards($stats); ?>
            <?php else: ?>
                <div class="alert alert-warning">نظام البطاقات غير متاح</div>
            <?php endif; ?>
        <?php endif; ?>

        <!-- Table Section -->
        <?php if ($has_table): ?>
        <div class="row">
            <div class="col-12">
                <div class="datatable-container">
                    <div class="datatable-content">

                        <!-- Toolbar -->
                        <?php if ($has_actions || $has_filters): ?>
                            <?php if (function_exists('render_datatable_toolbar')): ?>
                                <?php render_datatable_toolbar($module, $entity, $actions, $filters, $filters_config); ?>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Table -->
                        <div class="table-responsive">
                            <?php if (function_exists('render_datatable_table')): ?>
                                <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
                            <?php else: ?>
                                <div class="alert alert-warning">نظام الجداول غير متاح</div>
                            <?php endif; ?>
                        </div>

                        <!-- Pagination -->
                        <?php if (!empty($pagination) && isset($pagination['total_items']) && $pagination['total_items'] > 0): ?>
                            <?php if (function_exists('render_datatable_pagination')): ?>
                                <?php render_datatable_pagination($pagination, $filters); ?>
                            <?php endif; ?>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Modals -->
    <?php if ($has_filters && function_exists('render_filters_modal')): ?>
        <?php render_filters_modal($module, $entity, $filters_config, $filters, $stats, $pagination); ?>
    <?php endif; ?>

    <?php if ($has_table && function_exists('render_delete_modal')): ?>
        <?php render_delete_modal(); ?>
    <?php endif; ?>

    <!-- JavaScript -->
    <?php if (function_exists('render_datatable_scripts')): ?>
        <?php render_datatable_scripts($module, $entity, $filters); ?>
    <?php endif; ?>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🔄 Mixed page loaded: <?php echo $module; ?>/<?php echo $entity; ?>');

        // إضافة مسافة بين البطاقات والجدول
        const statsRow = document.querySelector('.row:has(.card)');
        const tableContainer = document.querySelector('.datatable-container');

        if (statsRow && tableContainer) {
            statsRow.style.marginBottom = '2rem';
        }
    });
    </script>
    <?php
}

/**
 * التحقق من توفر الأنظمة
 */
function get_available_systems()
{
    return [
        'datatable' => function_exists('render_datatable_page'),
        'cards' => function_exists('render_cards_page'),
        'forms' => function_exists('render_form_page'),
        'reports' => function_exists('render_report_page')
    ];
}

/**
 * التحقق من توفر نظام معين
 */
function is_system_available($system)
{
    $systems = get_available_systems();
    return isset($systems[$system]) && $systems[$system];
}
?>
