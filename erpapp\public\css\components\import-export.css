/* ===== نظام الاستيراد والتصدير ===== */

/* أزرار الاستيراد والتصدير في الـ toolbar */
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-left: auto;
}

.datatable-export-btn,
.datatable-import-btn,
.datatable-template-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border: 2px solid transparent;
    border-radius: 8px;
    text-decoration: none;
    transition: all 0.3s ease;
    cursor: pointer;
    white-space: nowrap;
}

.datatable-export-btn {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    color: white;
    border-color: var(--success-color);
}

.datatable-export-btn:hover {
    background: linear-gradient(135deg, var(--success-dark), var(--success-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--success-rgb), 0.3);
}

.datatable-import-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-color: var(--primary-color);
}

.datatable-import-btn:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.datatable-template-btn {
    background: linear-gradient(135deg, var(--info-color), var(--info-dark));
    color: white;
    border-color: var(--info-color);
}

.datatable-template-btn:hover {
    background: linear-gradient(135deg, var(--info-dark), var(--info-color));
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(var(--info-rgb), 0.3);
}

/* قائمة التصدير المنسدلة */
.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    min-width: 180px;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.dropdown.show .dropdown-menu {
    display: block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: right;
}

.dropdown-item:hover {
    background: var(--hover-bg);
    color: var(--primary-color);
}

.dropdown-item i {
    font-size: 1rem;
    width: 16px;
}

/* نافذة الاستيراد */
.import-modal .modal-dialog {
    max-width: 800px;
}

.import-modal .modal-content {
    border-radius: 12px;
    overflow: hidden;
}

.import-modal .modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-bottom: none;
}

.import-modal .modal-title {
    font-weight: 600;
}

/* خطوات الاستيراد */
.import-step {
    margin-bottom: 2rem;
}

.step-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.step-number {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    font-weight: 700;
    font-size: 0.875rem;
}

/* منطقة رفع الملف */
.file-upload-area {
    margin-bottom: 1rem;
}

.file-upload-box {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: var(--card-bg);
}

.file-upload-box:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.05);
}

.file-upload-box i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.file-upload-box p {
    margin: 0.5rem 0;
    color: var(--text-primary);
    font-weight: 500;
}

.file-upload-box .text-muted {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* معلومات الملف */
.file-info {
    margin-top: 1rem;
}

.file-size {
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-right: 0.5rem;
}

/* معلومات المعاينة */
.preview-info {
    margin-bottom: 1.5rem;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    text-align: center;
}

.info-card i {
    font-size: 1.25rem;
}

.info-card span {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.info-card strong {
    color: var(--text-primary);
}

/* جدول المعاينة */
#previewTable {
    font-size: 0.875rem;
}

#previewTable th {
    background: var(--primary-color);
    color: white;
    font-weight: 600;
    border: none;
}

#previewTable td {
    border-color: var(--border-color);
    padding: 0.5rem;
}

/* تطابق الأعمدة */
#columnMapping {
    display: grid;
    gap: 1rem;
}

.column-mapping-row {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.source-column {
    font-weight: 600;
    color: var(--text-primary);
}

.mapping-arrow {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.target-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-primary);
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .toolbar-right {
        flex-direction: column;
        gap: 0.25rem;
        width: 100%;
    }
    
    .datatable-export-btn,
    .datatable-import-btn,
    .datatable-template-btn {
        width: 100%;
        justify-content: center;
        padding: 0.75rem;
    }
    
    .import-modal .modal-dialog {
        margin: 1rem;
        max-width: none;
    }
    
    .file-upload-box {
        padding: 2rem 1rem;
    }
    
    .file-upload-box i {
        font-size: 2rem;
    }
    
    .column-mapping-row {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .mapping-arrow {
        transform: rotate(90deg);
    }
}

/* الوضع المظلم */
body.dark-theme .file-upload-box {
    border-color: var(--border-color-dark);
    background: var(--card-bg-dark);
}

body.dark-theme .file-upload-box:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
}

body.dark-theme .dropdown-menu {
    background: var(--card-bg-dark);
    border-color: var(--border-color-dark);
}

body.dark-theme .dropdown-item:hover {
    background: var(--hover-bg-dark);
}
