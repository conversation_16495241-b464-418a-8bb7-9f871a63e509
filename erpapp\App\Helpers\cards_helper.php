<?php
/**
 * Cards Helper Functions
 * مساعد البطاقات والإحصائيات - نظام منفصل ومرن
 */

/**
 * عرض صفحة بطاقات موحدة
 *
 * @param array $config إعدادات الصفحة والبطاقات
 * @return void
 */
function render_cards_page($config)
{
    // إعداد المتغيرات الأساسية
    $title = $config['title'] ?? 'صفحة البطاقات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    
    // البيانات الأساسية
    $stats = $config['stats'] ?? [];
    $actions = $config['actions'] ?? [];
    
    // Breadcrumb - افتراضي بسيط
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // عرض الصفحة
    render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions);
}

/**
 * عرض تخطيط صفحة البطاقات
 */
function render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions)
{
    // تحديد ما يجب عرضه
    $has_stats = !empty($stats);
    $has_actions = !empty($actions);
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_cards_header($title, $breadcrumb); ?>

        <!-- Stats Cards -->
        <?php if ($has_stats): ?>
            <?php render_stats_cards($stats); ?>
        <?php endif; ?>

        <!-- Actions Toolbar -->
        <?php if ($has_actions): ?>
            <?php render_cards_toolbar($module, $entity, $actions); ?>
        <?php endif; ?>
    </div>

    <!-- JavaScript -->
    <?php render_cards_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض رأس صفحة البطاقات
 */
function render_cards_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض بطاقات الإحصائيات (الدالة الأساسية في نظام البطاقات)
 */
function render_stats_cards($stats)
{
    render_cards_stats($stats);
}

/**
 * عرض بطاقات الإحصائيات (الدالة الفعلية)
 */
function render_cards_stats($stats)
{
    ?>
    <div class="row">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= number_format($stat['value']) ?></h3>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض شريط أدوات البطاقات
 */
function render_cards_toolbar($module, $entity, $actions)
{
    ?>
    <div class="row mb-3">
        <div class="col-12">
            <div class="cards-toolbar">
                <div class="toolbar-left"></div>
                <div class="toolbar-right">
                    <?php foreach ($actions as $action): ?>
                        <?php if ($action['type'] === 'primary'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-primary">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php elseif ($action['type'] === 'secondary'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-secondary">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php elseif ($action['type'] === 'success'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-success">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php elseif ($action['type'] === 'info'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-info">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php elseif ($action['type'] === 'warning'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-warning">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php elseif ($action['type'] === 'danger'): ?>
                            <a href="<?= base_url($action['url']) ?>" class="btn btn-danger">
                                <i class="<?= $action['icon'] ?>"></i>
                                <span><?= $action['text'] ?></span>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript للبطاقات
 */
function render_cards_scripts($module, $entity)
{
    ?>
    <script>
    // Cards JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📊 Cards system loaded for <?= $module ?>/<?= $entity ?>');
        
        // يمكن إضافة تفاعلات للبطاقات هنا
        // مثل: تحديث البيانات، رسوم بيانية، إلخ
    });
    </script>
    <?php
}

/**
 * إنشاء بطاقة إحصائية واحدة
 */
function create_stat_card($title, $value, $icon, $color = 'primary', $description = '')
{
    return [
        'title' => $title,
        'value' => $value,
        'icon' => $icon,
        'color' => $color,
        'description' => $description
    ];
}

/**
 * إنشاء مجموعة بطاقات إحصائية
 */
function create_stats_group($stats_data)
{
    $stats = [];
    foreach ($stats_data as $stat) {
        $stats[] = create_stat_card(
            $stat['title'],
            $stat['value'],
            $stat['icon'],
            $stat['color'] ?? 'primary',
            $stat['description'] ?? ''
        );
    }
    return $stats;
}

/**
 * إنشاء إجراء للبطاقات
 */
function create_card_action($type, $url, $icon, $text)
{
    return [
        'type' => $type,
        'url' => $url,
        'icon' => $icon,
        'text' => $text
    ];
}
?>
