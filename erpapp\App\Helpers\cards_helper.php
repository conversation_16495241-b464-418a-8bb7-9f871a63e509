<?php
/**
 * Cards Helper Functions
 * مساعد البطاقات والإحصائيات - نظام منفصل ومتخصص
 *
 * هذا الملف مخصص للبطاقات والإحصائيات فقط
 * للتحكم العام في الصفحات استخدم page_controller.php
 */

/**
 * دالة موحدة لمعالجة stats() في جميع Controllers
 */
function handle_cards_stats($config)
{
    $company_id = current_user()['current_company_id'];

    // الحصول على الإحصائيات
    $stats = $config['model']->getStats($company_id);

    // إعداد البيانات للعرض
    $viewData = [
        'title' => $config['title'],
        'stats' => $stats,
        'breadcrumb' => $config['breadcrumb']
    ];

    // إضافة بيانات إضافية إذا وجدت
    if (isset($config['additional_data'])) {
        $viewData = array_merge($viewData, $config['additional_data']);
    }

    return $viewData;
}

/**
 * تحويل الإحصائيات إلى تنسيق البطاقات
 */
function format_stats_for_cards($stats, $config = [])
{
    $cards = [];

    foreach ($stats as $key => $value) {
        $card_config = $config[$key] ?? [];

        $cards[] = [
            'title' => $card_config['title'] ?? ucfirst(str_replace('_', ' ', $key)),
            'value' => is_numeric($value) ? number_format($value) : $value,
            'icon' => $card_config['icon'] ?? 'fas fa-chart-bar',
            'color' => $card_config['color'] ?? 'primary',
            'description' => $card_config['description'] ?? ''
        ];
    }

    return $cards;
}

/**
 * عرض صفحة بطاقات متخصصة
 * هذه الدالة مخصصة للبطاقات فقط
 *
 * @param array $config إعدادات البطاقات
 * @return void
 */
function render_cards_page($config)
{
    // إعداد المتغيرات الأساسية للبطاقات
    $title = $config['title'] ?? 'صفحة البطاقات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';

    // بيانات البطاقات (مطلوبة)
    $stats = $config['stats'] ?? [];

    // إعدادات اختيارية
    $actions = $config['actions'] ?? [];

    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];

    // Empty state
    $empty_state = $config['empty_state'] ?? [
        'icon' => 'mdi mdi-chart-box-outline',
        'message' => 'لا توجد إحصائيات'
    ];

    // عرض البطاقات فقط
    render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions, $empty_state);
}

/**
 * عرض تخطيط البطاقات المتخصص
 * مخصص للبطاقات فقط - بدون جداول
 */
function render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions, $empty_state)
{
    // تحديد ما يجب عرضه
    $has_stats = !empty($stats);
    $has_actions = !empty($actions);

    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_cards_header($title, $breadcrumb); ?>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="cards-container">
                    <div class="cards-content">

                        <!-- Actions Toolbar -->
                        <?php if ($has_actions): ?>
                            <?php render_cards_toolbar($module, $entity, $actions); ?>
                        <?php endif; ?>

                        <!-- Stats Cards -->
                        <?php if ($has_stats): ?>
                            <?php render_cards_stats($stats); ?>
                        <?php else: ?>
                            <?php render_cards_empty_state($empty_state); ?>
                        <?php endif; ?>

                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <?php render_cards_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض رأس صفحة البطاقات
 */
function render_cards_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض بطاقات الإحصائيات (الدالة الأساسية في نظام البطاقات)
 */
function render_stats_cards($stats)
{
    render_cards_stats($stats);
}

/**
 * عرض بطاقات الإحصائيات (الدالة الفعلية)
 */
function render_cards_stats($stats)
{
    ?>
    <div class="row">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= is_numeric($stat['value']) ? number_format($stat['value']) : $stat['value'] ?></h3>
                                <?php if (!empty($stat['description'])): ?>
                                    <p class="text-muted mb-0"><?= $stat['description'] ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض حالة فارغة للبطاقات
 */
function render_cards_empty_state($empty_state)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="<?= $empty_state['icon'] ?? 'mdi mdi-chart-box-outline' ?> mdi-48px"></i>
                    <p class="mt-2"><?= $empty_state['message'] ?? 'لا توجد إحصائيات' ?></p>
                    <?php if (isset($empty_state['action'])): ?>
                        <a href="<?= base_url($empty_state['action']['url']) ?>" class="btn btn-primary btn-sm">
                            <?= $empty_state['action']['text'] ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض شريط أدوات البطاقات
 */
function render_cards_toolbar($module, $entity, $actions)
{
    ?>
    <div class="cards-toolbar mb-3">
        <div class="toolbar-left">
            <!-- يمكن إضافة أدوات إضافية هنا -->
        </div>
        <div class="toolbar-right">
            <?php foreach ($actions as $action): ?>
                <?php if ($action['type'] === 'primary'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-primary">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php elseif ($action['type'] === 'secondary'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-secondary">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php elseif ($action['type'] === 'success'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-success">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php elseif ($action['type'] === 'info'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-info">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php elseif ($action['type'] === 'warning'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-warning">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php elseif ($action['type'] === 'danger'): ?>
                    <a href="<?= base_url($action['url']) ?>" class="btn btn-danger">
                        <i class="<?= $action['icon'] ?>"></i>
                        <span><?= $action['text'] ?></span>
                    </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript المطلوب للبطاقات
 */
function render_cards_scripts($module, $entity)
{
    ?>
    <script>
    // Cards JavaScript Functions
    function refreshCards() {
        // تحديث البطاقات عبر AJAX
        console.log('Refreshing cards for <?php echo $module; ?>/<?php echo $entity; ?>');
        // يمكن إضافة كود AJAX هنا
    }

    function animateCards() {
        // تحريك البطاقات عند التحميل
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';

            setTimeout(() => {
                card.style.transition = 'all 0.3s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('📊 Cards system loaded for <?php echo $module; ?>/<?php echo $entity; ?>');

        // تحريك البطاقات عند التحميل
        animateCards();

        // إضافة تأثيرات تفاعلية للبطاقات
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px)';
                this.style.boxShadow = '0 4px 15px rgba(0,0,0,0.1)';
                this.style.transition = 'all 0.3s ease';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = '';
            });
        });

        // إضافة CSS للتحسينات
        const style = document.createElement('style');
        style.textContent = `
            .cards-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                padding: 1rem;
                background: var(--bs-light, #f8f9fa);
                border-radius: 0.375rem;
            }

            .cards-toolbar .toolbar-left {
                display: flex;
                gap: 0.5rem;
            }

            .cards-toolbar .toolbar-right {
                display: flex;
                gap: 0.5rem;
            }

            .cards-container {
                background: transparent;
            }

            .cards-content {
                padding: 0;
            }

            /* تحسين البطاقات */
            .card {
                transition: all 0.3s ease;
                border: 1px solid var(--bs-border-color, #dee2e6);
            }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }

            /* دعم الثيم الداكن */
            body.dark-theme .cards-toolbar {
                background: var(--bs-dark, #212529);
                border: 1px solid var(--bs-border-color-dark, #495057);
            }

            body.dark-theme .card:hover {
                box-shadow: 0 4px 15px rgba(255,255,255,0.1);
            }
        `;
        document.head.appendChild(style);
    });
    </script>
    <?php
}

/**
 * إنشاء بطاقة إحصائية واحدة
 */
function create_stat_card($title, $value, $icon, $color = 'primary', $description = '')
{
    return [
        'title' => $title,
        'value' => $value,
        'icon' => $icon,
        'color' => $color,
        'description' => $description
    ];
}

/**
 * إنشاء مجموعة بطاقات إحصائية
 */
function create_stats_group($stats_data)
{
    $stats = [];
    foreach ($stats_data as $stat) {
        $stats[] = create_stat_card(
            $stat['title'],
            $stat['value'],
            $stat['icon'],
            $stat['color'] ?? 'primary',
            $stat['description'] ?? ''
        );
    }
    return $stats;
}

/**
 * إنشاء إجراء للبطاقات
 */
function create_card_action($type, $url, $icon, $text)
{
    return [
        'type' => $type,
        'url' => $url,
        'icon' => $icon,
        'text' => $text
    ];
}

/**
 * تحويل البيانات الخام إلى بطاقات منسقة
 */
function convert_raw_data_to_cards($raw_data, $mapping = [])
{
    $cards = [];

    foreach ($raw_data as $key => $value) {
        $config = $mapping[$key] ?? [];

        $cards[] = [
            'title' => $config['title'] ?? ucfirst(str_replace('_', ' ', $key)),
            'value' => is_numeric($value) ? number_format($value) : $value,
            'icon' => $config['icon'] ?? 'fas fa-chart-bar',
            'color' => $config['color'] ?? 'primary',
            'description' => $config['description'] ?? ''
        ];
    }

    return $cards;
}

/**
 * إنشاء بطاقات من نموذج قاعدة البيانات
 */
function create_cards_from_model($model, $company_id, $mapping = [])
{
    if (!method_exists($model, 'getStats')) {
        return [];
    }

    $stats = $model->getStats($company_id);
    return convert_raw_data_to_cards($stats, $mapping);
}

/**
 * دمج مجموعات بطاقات متعددة
 */
function merge_card_groups($groups)
{
    $merged = [];

    foreach ($groups as $group) {
        if (is_array($group)) {
            $merged = array_merge($merged, $group);
        }
    }

    return $merged;
}
?>