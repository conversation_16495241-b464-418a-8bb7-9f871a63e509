<?php
/**
 * Cards Helper Functions - نسخة مبسطة مثل datatable_helper.php
 * مساعد البطاقات والإحصائيات - نظام منفصل ومتخصص
 *
 * هذا الملف مخصص للبطاقات والإحصائيات فقط
 * للتحكم العام في الصفحات استخدم page_controller.php
 */

/**
 * عرض صفحة بطاقات متخصصة
 * هذه الدالة مخصصة للبطاقات فقط
 *
 * @param array $config إعدادات البطاقات
 * @return void
 */
function render_cards_page($config)
{
    // إعداد المتغيرات الأساسية للبطاقات
    $title = $config['title'] ?? 'صفحة البطاقات';
    $module = $config['module'] ?? 'default';
    $entity = $config['entity'] ?? 'items';
    
    // بيانات البطاقات (مطلوبة)
    $stats = $config['stats'] ?? [];
    
    // إعدادات اختيارية
    $actions = $config['actions'] ?? [];
    
    // Breadcrumb
    $breadcrumb = $config['breadcrumb'] ?? [
        ['title' => $title, 'active' => true]
    ];
    
    // Empty state
    $empty_state = $config['empty_state'] ?? [
        'icon' => 'mdi mdi-chart-box-outline',
        'message' => 'لا توجد إحصائيات'
    ];

    // عرض البطاقات فقط
    render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions, $empty_state);
}

/**
 * عرض تخطيط البطاقات المتخصص
 * مخصص للبطاقات فقط - بدون جداول
 */
function render_cards_layout($title, $module, $entity, $stats, $breadcrumb, $actions, $empty_state)
{
    // تحديد ما يجب عرضه
    $has_stats = !empty($stats);
    $has_actions = !empty($actions);
    
    ?>
    <div class="container-fluid">
        <!-- Header -->
        <?php render_cards_header($title, $breadcrumb); ?>

        <!-- Main Content -->
        <div class="row">
            <div class="col-12">
                <div class="cards-container">
                    <div class="cards-content">
                        
                        <!-- Actions Toolbar -->
                        <?php if ($has_actions): ?>
                            <?php render_cards_toolbar($module, $entity, $actions); ?>
                        <?php endif; ?>

                        <!-- Stats Cards -->
                        <?php if ($has_stats): ?>
                            <?php render_stats_cards($stats); ?>
                        <?php else: ?>
                            <?php render_cards_empty_state($empty_state); ?>
                        <?php endif; ?>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <?php render_cards_scripts($module, $entity); ?>
    <?php
}

/**
 * عرض رأس صفحة البطاقات
 */
function render_cards_header($title, $breadcrumb)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <?php foreach ($breadcrumb as $item): ?>
                            <?php if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])): ?>
                                <li class="breadcrumb-item"><a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a></li>
                            <?php else: ?>
                                <li class="breadcrumb-item active"><?= $item['title'] ?></li>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </ol>
                </div>
                <h4 class="page-title"><?= $title ?></h4>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض بطاقات الإحصائيات (للتوافق مع datatable_helper.php)
 */
function render_stats_cards($stats)
{
    ?>
    <div class="row">
        <?php foreach ($stats as $stat): ?>
            <div class="col-md-6 col-xl-3">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-6">
                                <h5 class="text-muted fw-normal mt-0 text-truncate"><?= $stat['title'] ?></h5>
                                <h3 class="my-2 py-1"><?= is_numeric($stat['value']) ? number_format($stat['value']) : $stat['value'] ?></h3>
                                <?php if (!empty($stat['description'])): ?>
                                    <p class="text-muted mb-0"><?= $stat['description'] ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-6">
                                <div class="text-end">
                                    <i class="<?= $stat['icon'] ?> text-<?= $stat['color'] ?? 'muted' ?>" style="font-size: 2rem;"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php
}

/**
 * عرض حالة فارغة للبطاقات
 */
function render_cards_empty_state($empty_state)
{
    ?>
    <div class="row">
        <div class="col-12">
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="<?= $empty_state['icon'] ?? 'mdi mdi-chart-box-outline' ?> mdi-48px"></i>
                    <p class="mt-2"><?= $empty_state['message'] ?? 'لا توجد إحصائيات' ?></p>
                    <?php if (isset($empty_state['action'])): ?>
                        <a href="<?= base_url($empty_state['action']['url']) ?>" class="btn btn-primary btn-sm">
                            <?= $empty_state['action']['text'] ?>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php
}

/**
 * عرض شريط أدوات البطاقات
 */
function render_cards_toolbar($module, $entity, $actions)
{
    ?>
    <div class="cards-toolbar mb-3">
        <div class="toolbar-left">
            <!-- يمكن إضافة أدوات إضافية هنا -->
        </div>
        <div class="toolbar-right">
            <?php foreach ($actions as $action): ?>
                <a href="<?= base_url($action['url']) ?>" class="btn btn-<?= $action['type'] ?? 'secondary' ?>">
                    <i class="<?= $action['icon'] ?>"></i>
                    <span><?= $action['text'] ?></span>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php
}

/**
 * عرض JavaScript المطلوب للبطاقات
 */
function render_cards_scripts($module, $entity)
{
    ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('📊 Cards system loaded for <?php echo $module; ?>/<?php echo $entity; ?>');
        
        // إضافة CSS بسيط للتحسينات
        const style = document.createElement('style');
        style.textContent = `
            .cards-toolbar {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                padding: 1rem;
                background: var(--bs-light, #f8f9fa);
                border-radius: 0.375rem;
            }
            
            .cards-toolbar .toolbar-left {
                display: flex;
                gap: 0.5rem;
            }
            
            .cards-toolbar .toolbar-right {
                display: flex;
                gap: 0.5rem;
            }
            
            .cards-container {
                background: transparent;
            }
            
            .cards-content {
                padding: 0;
            }
        `;
        document.head.appendChild(style);
    });
    </script>
    <?php
}

/**
 * إنشاء بطاقة إحصائية واحدة
 */
function create_stat_card($title, $value, $icon, $color = 'primary', $description = '')
{
    return [
        'title' => $title,
        'value' => $value,
        'icon' => $icon,
        'color' => $color,
        'description' => $description
    ];
}
?>
