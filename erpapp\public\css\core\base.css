        /* ===== SMOOTH THEME TRANSITIONS ===== */
        *,
        *::before,
        *::after {
            transition:
                background-color var(--transition-theme) var(--transition-theme-bezier),
                border-color var(--transition-theme) var(--transition-theme-bezier),
                color var(--transition-theme) var(--transition-theme-bezier),
                box-shadow var(--transition-theme) var(--transition-theme-bezier),
                opacity var(--transition-theme) var(--transition-theme-bezier) !important;
        }

        /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-scrollbar-track);
            border-radius: var(--border-radius-full);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--light-scrollbar-thumb);
            border-radius: var(--border-radius-full);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-500);
        }

        body.dark-theme ::-webkit-scrollbar-track {
            background: var(--dark-scrollbar-track);
        }

        body.dark-theme ::-webkit-scrollbar-thumb {
            background: var(--dark-scrollbar-thumb);
        }

        body.dark-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-600);
        }

        /* Selection Styles */
        ::selection {
            background-color: var(--primary-light);
            color: white;
        }

        body.dark-theme ::selection {
            background-color: var(--primary-dark);
        }

        body {
            font-family: 'Inter', 'Tajawal', sans-serif;
            line-height: 1.6;
            background-color: var(--light-bg-color);
            color: var(--light-text-color);
            min-height: 100vh;
            transition: background-color var(--transition-normal) var(--transition-bezier),
                        color var(--transition-normal) var(--transition-bezier);
            font-size: 0.95rem;
            letter-spacing: 0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }

        /* Dark Theme */
        body.dark-theme {
            background-color: var(--dark-bg-color);
            color: var(--dark-text-color);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: var(--spacing-4);
            color: inherit;
        }

        h1 {
            font-size: 2rem;
            letter-spacing: -0.025em;
        }

        h2 {
            font-size: 1.75rem;
            letter-spacing: -0.025em;
        }

        h3 {
            font-size: 1.5rem;
            letter-spacing: -0.025em;
        }

        h4 {
            font-size: 1.25rem;
        }

        h5 {
            font-size: 1.125rem;
        }

        h6 {
            font-size: 1rem;
        }

        p {
            margin-bottom: var(--spacing-4);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
        }

        a:hover {
            color: var(--primary-dark);
        }

        a.underline-link {
            position: relative;
        }

        a.underline-link::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: currentColor;
            transform: scaleX(0);
            transform-origin: right;
            transition: transform var(--transition-normal) var(--transition-bezier);
        }

        a.underline-link:hover::after {
            transform: scaleX(1);
            transform-origin: left;
        }

        img {
            max-width: 100%;
            height: auto;
        }

        body.dark-theme .card {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

       
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .wrapper {
            display: flex;
            width: 100%;
            min-height: 100vh;
        }

      

        /* Content Styles */
        .content {
            width: calc(100% - var(--sidebar-width));
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease-in-out;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .rtl .content {
            margin-left: 0;
            margin-right: var(--sidebar-width);
        }

        .content.expanded {
            width: calc(100% - var(--sidebar-collapsed-width));
            margin-left: var(--sidebar-collapsed-width);
        }

        .rtl .content.expanded {
            margin-left: 0;
            margin-right: var(--sidebar-collapsed-width);
        }

        /* Content Width */
        .content.small-width .main-content {

            padding-left: 200px;
            padding-right: 200px;
            transition: all 0.3s ease-in-out;
        }

      

        /* Basic Badges - Only for non-table usage */
        .badge:not(.table .badge) {
            font-weight: 500;
            padding: 0.35em 0.65em;
            border-radius: var(--border-radius-sm);
        }

        .badge-primary:not(.table .badge) {
            background-color: var(--primary-color);
            color: white;
        }

        .badge-secondary:not(.table .badge) {
            background-color: var(--secondary-color);
            color: white;
        }

        .badge-success:not(.table .badge) {
            background-color: var(--success-color);
            color: white;
        }

        .badge-info:not(.table .badge) {
            background-color: var(--info-color);
            color: white;
        }

        .badge-warning:not(.table .badge) {
            background-color: var(--warning-color);
            color: white;
        }

        .badge-danger:not(.table .badge) {
            background-color: var(--danger-color);
            color: white;
        }

        /* Alerts */
        .alert {
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            border: none;
            margin-bottom: 1.5rem;
        }

        .alert-primary {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }

        .alert-secondary {
            background-color: rgba(247, 37, 133, 0.1);
            color: var(--secondary-color);
        }

        .alert-success {
            background-color: rgba(76, 201, 240, 0.1);
            color: var(--success-color);
        }

        .alert-info {
            background-color: rgba(72, 149, 239, 0.1);
            color: var(--info-color);
        }

        .alert-warning {
            background-color: rgba(249, 199, 79, 0.1);
            color: var(--warning-color);
        }

        .alert-danger {
            background-color: rgba(249, 65, 68, 0.1);
            color: var(--danger-color);
        }

        /* Auth Pages */
        .auth-container {
            max-width: 450px;
            width: 100%;
            background-color: var(--light-card-bg);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            padding: 2.5rem;
            margin: 3rem auto;
            border: 1px solid var(--light-border-color);
            transition: all var(--transition-speed) ease-in-out;
        }

        body.dark-theme .auth-container {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-logo {
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .auth-logo img {
            height: 3rem;
        }

        .auth-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
        }

        .auth-header p {
            color: var(--gray-600);
            font-size: 0.95rem;
        }

        .auth-form .form-group {
            margin-bottom: 1.25rem;
        }

        .auth-form .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .auth-form .form-control {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--light-input-border);
        }

        .auth-form .btn {
            width: 100%;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            margin-top: 1rem;
        }

        .auth-footer {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--light-border-color);
            font-size: 0.9rem;
            color: var(--gray-600);
        }

        body.dark-theme .auth-footer {
            border-color: var(--dark-border-color);
        }

        .auth-social-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .auth-social-button {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            background-color: var(--light-hover-bg);
            color: var(--gray-700);
            transition: all var(--transition-speed);
            border: 1px solid var(--light-border-color);
        }

        .auth-social-button:hover {
            background-color: var(--light-active-bg);
            color: var(--primary-color);
        }

        body.dark-theme .auth-social-button {
            background-color: var(--dark-hover-bg);
            color: var(--gray-400);
            border-color: var(--dark-border-color);
        }

        body.dark-theme .auth-social-button:hover {
            background-color: var(--dark-active-bg);
            color: var(--primary-color);
        }

       

        @media (max-width: 576px) {
            .auth-container {
                padding: 30px;
            }

            .auth-header h1 {
                font-size: 1.8rem;
            }
        }
        /* Card Styles - Modern & Sleek */
        .card {
            background-color: var(--light-card-bg);
            border: 1px solid var(--light-card-border);
            border-radius: var(--border-radius-md);
            box-shadow: var(--box-shadow-sm);
            margin-bottom: var(--spacing-6);
            transition: all var(--transition-normal) var(--transition-bezier);
            overflow: hidden;
            position: relative;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .card:hover {
            box-shadow: var(--box-shadow-md);
            transform: translateY(-3px);
        }

        .card-header {
            padding: var(--spacing-5) var(--spacing-6);
            background-color: transparent;
            border-bottom: 1px solid var(--light-border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .card-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 50px;
            height: 3px;
            background: var(--primary-gradient);
            border-radius: var(--border-radius-full);
        }

        .rtl .card-header::after {
            left: auto;
            right: 0;
        }

        .card-header h5, .card-header .h5 {
            margin-bottom: 0;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-2);
        }

        .card-header-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border-radius: var(--border-radius-md);
            background: var(--primary-gradient);
            color: white;
            font-size: 1rem;
        }

        .card-body {
            padding: var(--spacing-6);
        }

        .card-footer {
            padding: var(--spacing-5) var(--spacing-6);
            background-color: transparent;
            border-top: 1px solid var(--light-border-color);
        }

        /* Glass Card Effect */
        .card-glass {
            background-color: rgba(255, 255, 255, 0.7);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        body.dark-theme .card-glass {
            background-color: rgba(31, 41, 55, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Button Styles - Modern & Interactive */
        .btn {
            font-weight: 500;
            padding: var(--spacing-3) var(--spacing-5);
            border-radius: var(--border-radius-md);
            transition: all var(--transition-normal) var(--transition-bezier);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-2);
            border: none;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            line-height: 1.5;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateX(-100%);
            transition: transform var(--transition-normal) var(--transition-bezier);
        }

        .btn:hover::before {
            transform: translateX(0);
        }

        .btn-sm {
            padding: var(--spacing-2) var(--spacing-4);
            font-size: 0.875rem;
            border-radius: var(--border-radius-sm);
        }

        .btn-lg {
            padding: var(--spacing-4) var(--spacing-8);
            font-size: 1.1rem;
            border-radius: var(--border-radius);
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }

        .btn-primary:hover {
            box-shadow: 0 6px 10px rgba(99, 102, 241, 0.35);
            transform: translateY(-1px);
        }

        .btn-primary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
        }

        .btn-secondary {
            background: var(--secondary-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(236, 72, 153, 0.25);
        }

        .btn-secondary:hover {
            box-shadow: 0 6px 10px rgba(236, 72, 153, 0.35);
            transform: translateY(-1px);
        }

        .btn-secondary:active {
            transform: translateY(1px);
            box-shadow: 0 2px 4px rgba(236, 72, 153, 0.2);
        }

        .btn-success {
            background: var(--success-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(16, 185, 129, 0.25);
        }

        .btn-success:hover {
            box-shadow: 0 6px 10px rgba(16, 185, 129, 0.35);
            transform: translateY(-1px);
        }

        .btn-info {
            background: var(--info-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(59, 130, 246, 0.25);
        }

        .btn-info:hover {
            box-shadow: 0 6px 10px rgba(59, 130, 246, 0.35);
            transform: translateY(-1px);
        }

        .btn-warning {
            background: var(--warning-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(245, 158, 11, 0.25);
        }

        .btn-warning:hover {
            box-shadow: 0 6px 10px rgba(245, 158, 11, 0.35);
            transform: translateY(-1px);
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
            box-shadow: 0 4px 6px rgba(239, 68, 68, 0.25);
        }

        .btn-danger:hover {
            box-shadow: 0 6px 10px rgba(239, 68, 68, 0.35);
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            background-color: transparent;
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            color: white;
            box-shadow: 0 4px 6px rgba(99, 102, 241, 0.25);
        }

        .btn-outline-secondary {
            background-color: transparent;
            border: 2px solid var(--secondary-color);
            color: var(--secondary-color);
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            color: white;
            box-shadow: 0 4px 6px rgba(236, 72, 153, 0.25);
        }

        .btn-light {
            background-color: var(--light-color);
            color: var(--gray-800);
            border: 1px solid var(--gray-300);
        }

        .btn-light:hover {
            background-color: var(--gray-100);
        }

        .btn-dark {
            background-color: var(--dark-color);
            color: var(--light-color);
        }

        .btn-dark:hover {
            background-color: var(--gray-800);
        }

        .btn-icon {
            width: 40px;
            height: 40px;
            padding: 0;
            border-radius: var(--border-radius-full);
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .btn-icon.btn-sm {
            width: 32px;
            height: 32px;
        }

        .btn-icon.btn-lg {
            width: 48px;
            height: 48px;
        }

        /* Form Controls - Modern & Clean */
        .form-control {
            padding: var(--spacing-3) var(--spacing-4);
            border: 1px solid var(--light-input-border);
            border-radius: var(--border-radius);
            background-color: var(--light-input-bg);
            transition: all var(--transition-normal) var(--transition-bezier);
            font-size: 0.95rem;
            width: 100%;
            color: var(--light-text-color);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
            outline: none;
        }

        .form-control::placeholder {
            color: var(--gray-500);
            opacity: 1;
        }

        .form-label {
            font-weight: 500;
            margin-bottom: var(--spacing-2);
            display: block;
            color: var(--gray-700);
        }

        .form-text {
            margin-top: var(--spacing-2);
            font-size: 0.875rem;
            color: var(--gray-600);
        }

        .form-group {
            margin-bottom: var(--spacing-5);
        }

        .form-check {
            display: flex;
            align-items: center;
            padding-left: 0;
            margin-bottom: var(--spacing-3);
        }

        .form-check-input {
            margin-right: var(--spacing-2);
            width: 18px;
            height: 18px;
            cursor: pointer;
        }

        .rtl .form-check-input {
            margin-right: 0;
            margin-left: var(--spacing-2);
        }

        .form-check-label {
            cursor: pointer;
        }

        .form-switch .form-check-input {
            width: 36px;
            height: 20px;
            border-radius: 20px;
            background-color: var(--gray-300);
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
            background-position: left center;
            background-repeat: no-repeat;
            background-size: contain;
            transition: background-position var(--transition-normal) var(--transition-bezier);
            appearance: none;
            -webkit-appearance: none;
        }

        .form-switch .form-check-input:checked {
            background-color: var(--primary-color);
            background-position: right center;
        }

        /* Additional Dark Theme Styles */
        body.dark-theme .card {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        body.dark-theme .card-header,
        body.dark-theme .card-footer {
            border-color: var(--dark-border-color);
        }

        body.dark-theme .table {
            color: var(--dark-text-color);
            background-color: var(--dark-card-bg);
        }

        body.dark-theme .table th {
            background-color: var(--gray-800);
            color: var(--dark-text-muted);
            border-bottom-color: var(--dark-card-border);
        }

        body.dark-theme .table td {
            border-bottom-color: var(--gray-800);
        }

        body.dark-theme .table-responsive {
            background-color: var(--dark-card-bg);
        }

        body.dark-theme .table-hover tbody tr:hover {
            background-color: var(--dark-hover-bg);
        }

        body.dark-theme .form-control {
            background-color: var(--dark-input-bg);
            border-color: var(--dark-input-border);
            color: var(--dark-text-color);
        }

        body.dark-theme .form-control:focus {
            background-color: var(--dark-input-bg);
            color: var(--dark-text-color);
            border-color: var(--primary-color);
        }

        body.dark-theme .input-group-text {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-input-border);
            color: var(--dark-text-color);
        }

        body.dark-theme .modal-content {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        body.dark-theme .modal-header,
        body.dark-theme .modal-footer {
            border-color: var(--dark-border-color);
        }

        body.dark-theme .alert-info {
            background-color: rgba(72, 149, 239, 0.2);
            border-color: rgba(72, 149, 239, 0.3);
            color: var(--info-color);
        }

        body.dark-theme .text-muted {
            color: var(--gray-500) !important;
        }

        body.dark-theme .page-title {
            color: var(--dark-text-color);
        }

        /* حل جذري لوضع شريط التمرير على اليسار في اللغة العربية وعلى اليمين في اللغة الإنجليزية */

        /* تعيين الاتجاه للغة العربية */
        html[dir="rtl"], body.rtl {
            direction: rtl;
        }

        /* تعيين الاتجاه للغة الإنجليزية */
        html[dir="ltr"], body:not(.rtl) {
            direction: ltr;
        }

        /* إخفاء شريط التمرير الافتراضي */
        html, body {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        html::-webkit-scrollbar, body::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
            width: 0;
            height: 0;
        }

        /* إنشاء شريط تمرير مخصص */
        #custom-scrollbar {
            position: fixed;
            top: 0;
            width: 10px; /* عرض شريط التمرير */
            height: 100%;
            z-index: 9999;
            pointer-events: none; /* لا يتفاعل مع المؤشر */
            display: none; /* إخفاء الشريط افتراضيًا حتى يتم التحقق من الحاجة له */
        }

        /* وضع شريط التمرير على اليسار في اللغة العربية */
        html[dir="rtl"] #custom-scrollbar, body.rtl #custom-scrollbar {
            left: 0;
        }

        /* وضع شريط التمرير على اليمين في اللغة الإنجليزية */
        html[dir="ltr"] #custom-scrollbar, body:not(.rtl) #custom-scrollbar {
            right: 0;
        }

        #scrollbar-thumb {
            position: absolute;
            width: 6px; /* عرض المؤشر */
            border-radius: 3px;
            background-color: var(--primary-color, #4361ee); /* لون المؤشر - استخدام لون أساسي من النظام */
            opacity: 0.7;
            transition: opacity 0.3s, width 0.3s;
            right: 2px; /* المسافة من اليمين */
        }

        /* تغيير لون المؤشر عند التحويم */
        #scrollbar-thumb:hover {
            opacity: 0.9;
            width: 8px; /* زيادة العرض عند التحويم */
        }

        /* تخصيص لون المؤشر في الوضع الداكن */
        body.dark-theme #scrollbar-thumb {
            background-color: var(--primary-light, #6c8fff); /* لون أفتح في الوضع الداكن */
        }

        /* تخصيص شريط التمرير للعناصر الأخرى */
        .sidebar, .main-content, .dropdown-menu {
            scrollbar-width: thin;
            scrollbar-color: var(--primary-color, #4361ee) transparent;
        }

        .sidebar::-webkit-scrollbar, .main-content::-webkit-scrollbar, .dropdown-menu::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track, .main-content::-webkit-scrollbar-track, .dropdown-menu::-webkit-scrollbar-track {
            background: transparent;
        }

        .sidebar::-webkit-scrollbar-thumb, .main-content::-webkit-scrollbar-thumb, .dropdown-menu::-webkit-scrollbar-thumb {
            background-color: var(--primary-color, #4361ee);
            border-radius: 3px;
            opacity: 0.7;
        }

        body.dark-theme .sidebar::-webkit-scrollbar-thumb,
        body.dark-theme .main-content::-webkit-scrollbar-thumb,
        body.dark-theme .dropdown-menu::-webkit-scrollbar-thumb {
            background-color: var(--primary-light, #6c8fff);
        }

        /* تنسيق زر العودة إلى الأعلى */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            width: 45px;
            height: 45px;
            background: var(--primary-color, #4361ee);
            color: white;
            border-radius: 50%;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        /* موضع الزر حسب اللغة */
        html[dir="rtl"] .scroll-to-top, body.rtl .scroll-to-top {
            left: 30px;
            right: auto;
        }

        html[dir="ltr"] .scroll-to-top, body:not(.rtl) .scroll-to-top {
            right: 30px;
            left: auto;
        }

        /* حالة ظهور الزر */
        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        /* تأثير التحويم */
        .scroll-to-top:hover {
            background: var(--primary-dark, #3a56d4);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transform: translateY(-3px);
        }

        /* تأثير النقر */
        .scroll-to-top:active {
            transform: translateY(1px);
        }

        /* تنسيق الأيقونة */
        .scroll-to-top i {
            font-size: 18px;
        }

        /* تنسيق الزر في الوضع الداكن */
        body.dark-theme .scroll-to-top {
            background: var(--primary-light, #6c8fff);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
        }

        body.dark-theme .scroll-to-top:hover {
            background: var(--primary-color, #4361ee);
        }

        /* تنسيق زر اختصارات لوحة المفاتيح */
        #keyboard-shortcuts-btn {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #keyboard-shortcuts-btn:hover {
            color: var(--primary-color, #4361ee);
        }

        /* تنسيق نافذة اختصارات لوحة المفاتيح */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .keyboard-shortcuts-container {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .modal-overlay.show .keyboard-shortcuts-container {
            transform: translateY(0);
        }

        .keyboard-shortcuts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        .keyboard-shortcuts-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--dark-text-color, #333);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #777;
            transition: color 0.2s ease;
        }

        .close-btn:hover {
            color: var(--danger-color, #dc3545);
        }

        .keyboard-shortcuts-body {
            padding: 20px;
        }

        .shortcuts-section {
            margin-bottom: 25px;
        }

        .shortcuts-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--dark-text-color, #333);
            font-size: 1.1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .shortcut-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .shortcut-keys {
            display: flex;
            align-items: center;
            min-width: 150px;
        }

        .shortcut-description {
            color: var(--dark-text-color, #333);
        }

        kbd {
            display: inline-block;
            padding: 4px 8px;
            font-family: monospace;
            font-size: 0.9rem;
            color: #444;
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            margin: 0 5px 0 0;
        }

        /* تنسيق نافذة الاختصارات في الوضع الداكن */
        body.dark-theme .keyboard-shortcuts-container {
            background-color: var(--dark-bg-color, #1e2233);
            border: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme .keyboard-shortcuts-header {
            border-bottom: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme .keyboard-shortcuts-header h3,
        body.dark-theme .shortcuts-section h4,
        body.dark-theme .shortcut-description {
            color: var(--dark-text-color, #e1e1e1);
        }

        body.dark-theme .close-btn {
            color: #aaa;
        }

        body.dark-theme .close-btn:hover {
            color: var(--danger-color, #dc3545);
        }

        body.dark-theme .shortcuts-section h4 {
            border-bottom: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme kbd {
            color: #e1e1e1;
            background-color: var(--dark-secondary-bg, #2d3348);
            border: 1px solid #444;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
        }