  /* Scrollbar Styles */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-scrollbar-track);
            border-radius: var(--border-radius-full);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--light-scrollbar-thumb);
            border-radius: var(--border-radius-full);
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-500);
        }

        body.dark-theme ::-webkit-scrollbar-track {
            background: var(--dark-scrollbar-track);
        }

        body.dark-theme ::-webkit-scrollbar-thumb {
            background: var(--dark-scrollbar-thumb);
        }

        body.dark-theme ::-webkit-scrollbar-thumb:hover {
            background: var(--gray-600);
        }

        /* Selection Styles */
        ::selection {
            background-color: var(--primary-light);
            color: white;
        }

        body.dark-theme ::selection {
            background-color: var(--primary-dark);
        }

        body {
            font-family: 'Inter', 'Tajawal', sans-serif;
            line-height: 1.6;
            background-color: var(--light-bg-color);
            color: var(--light-text-color);
            min-height: 100vh;
            transition: background-color var(--transition-normal) var(--transition-bezier),
                        color var(--transition-normal) var(--transition-bezier);
            font-size: 0.95rem;
            letter-spacing: 0.01em;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow-x: hidden;
        }

        /* Dark Theme */
        body.dark-theme {
            background-color: var(--dark-bg-color);
            color: var(--dark-text-color);
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 600;
            line-height: 1.3;
            margin-bottom: var(--spacing-4);
            color: inherit;
        }

        h1 {
            font-size: 2rem;
            letter-spacing: -0.025em;
        }

        h2 {
            font-size: 1.75rem;
            letter-spacing: -0.025em;
        }

        h3 {
            font-size: 1.5rem;
            letter-spacing: -0.025em;
        }

        h4 {
            font-size: 1.25rem;
        }

        h5 {
            font-size: 1.125rem;
        }

        h6 {
            font-size: 1rem;
        }

        p {
            margin-bottom: var(--spacing-4);
        }

        a {
            color: var(--primary-color);
            text-decoration: none;
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
        }

        a:hover {
            color: var(--primary-dark);
        }

        a.underline-link {
            position: relative;
        }

        a.underline-link::after {
            content: '';
            position: absolute;
            width: 100%;
            height: 2px;
            bottom: -2px;
            left: 0;
            background-color: currentColor;
            transform: scaleX(0);
            transform-origin: right;
            transition: transform var(--transition-normal) var(--transition-bezier);
        }

        a.underline-link:hover::after {
            transform: scaleX(1);
            transform-origin: left;
        }

        img {
            max-width: 100%;
            height: auto;
        }

      

       
        .rtl {
            direction: rtl;
            text-align: right;
        }

        .wrapper {
            display: flex;
            width: 100%;
            min-height: 100vh;
        }

      

        /* Content Styles */
        .content {
            width: calc(100% - var(--sidebar-width));
            margin-left: var(--sidebar-width);
            transition: all 0.3s ease-in-out;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .rtl .content {
            margin-left: 0;
            margin-right: var(--sidebar-width);
        }

        .content.expanded {
            width: calc(100% - var(--sidebar-collapsed-width));
            margin-left: var(--sidebar-collapsed-width);
        }

        .rtl .content.expanded {
            margin-left: 0;
            margin-right: var(--sidebar-collapsed-width);
        }

        /* Content Width */
        .content.small-width .main-content {

            padding-left: 200px;
            padding-right: 200px;
            transition: all 0.3s ease-in-out;
        }

      

      

        /* Alerts */
        .alert {
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            border: none;
            margin-bottom: 1.5rem;
        }

        .alert-primary {
            background-color: rgba(67, 97, 238, 0.1);
            color: var(--primary-color);
        }

        .alert-secondary {
            background-color: rgba(247, 37, 133, 0.1);
            color: var(--secondary-color);
        }

        .alert-success {
            background-color: rgba(76, 201, 240, 0.1);
            color: var(--success-color);
        }

        .alert-info {
            background-color: rgba(72, 149, 239, 0.1);
            color: var(--info-color);
        }

        .alert-warning {
            background-color: rgba(249, 199, 79, 0.1);
            color: var(--warning-color);
        }

        .alert-danger {
            background-color: rgba(249, 65, 68, 0.1);
            color: var(--danger-color);
        }

        /* Auth Pages */
        .auth-container {
            max-width: 450px;
            width: 100%;
            background-color: var(--light-card-bg);
            border-radius: var(--border-radius-lg);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            padding: 2.5rem;
            margin: 3rem auto;
            border: 1px solid var(--light-border-color);
            transition: all var(--transition-speed) ease-in-out;
        }

        body.dark-theme .auth-container {
            background-color: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .auth-logo {
            margin-bottom: 1.5rem;
            display: inline-block;
        }

        .auth-logo img {
            height: 3rem;
        }

        .auth-header h1 {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.75rem;
        }

        .auth-header p {
            color: var(--gray-600);
            font-size: 0.95rem;
        }

        .auth-form .form-group {
            margin-bottom: 1.25rem;
        }

        .auth-form .form-label {
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .auth-form .form-control {
            padding: 0.75rem 1rem;
            border-radius: var(--border-radius);
            border: 1px solid var(--light-input-border);
        }

        .auth-form .btn {
            width: 100%;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            margin-top: 1rem;
        }

        .auth-footer {
            text-align: center;
            margin-top: 1.5rem;
            padding-top: 1.5rem;
            border-top: 1px solid var(--light-border-color);
            font-size: 0.9rem;
            color: var(--gray-600);
        }

        body.dark-theme .auth-footer {
            border-color: var(--dark-border-color);
        }

      


      

        /* حل جذري لوضع شريط التمرير على اليسار في اللغة العربية وعلى اليمين في اللغة الإنجليزية */

        /* تعيين الاتجاه للغة العربية */
        html[dir="rtl"], body.rtl {
            direction: rtl;
        }

        /* تعيين الاتجاه للغة الإنجليزية */
        html[dir="ltr"], body:not(.rtl) {
            direction: ltr;
        }

        /* إخفاء شريط التمرير الافتراضي */
        html, body {
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        html::-webkit-scrollbar, body::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
            width: 0;
            height: 0;
        }

        /* إنشاء شريط تمرير مخصص */
        #custom-scrollbar {
            position: fixed;
            top: 0;
            width: 10px; /* عرض شريط التمرير */
            height: 100%;
            z-index: 9999;
            pointer-events: none; /* لا يتفاعل مع المؤشر */
            display: none; /* إخفاء الشريط افتراضيًا حتى يتم التحقق من الحاجة له */
        }

        /* وضع شريط التمرير على اليسار في اللغة العربية */
        html[dir="rtl"] #custom-scrollbar, body.rtl #custom-scrollbar {
            left: 0;
        }

        /* وضع شريط التمرير على اليمين في اللغة الإنجليزية */
        html[dir="ltr"] #custom-scrollbar, body:not(.rtl) #custom-scrollbar {
            right: 0;
        }

        #scrollbar-thumb {
            position: absolute;
            width: 6px; /* عرض المؤشر */
            border-radius: 3px;
            background-color: var(--primary-color, #4361ee); /* لون المؤشر - استخدام لون أساسي من النظام */
            opacity: 0.7;
            transition: opacity 0.3s, width 0.3s;
            right: 2px; /* المسافة من اليمين */
        }

        /* تغيير لون المؤشر عند التحويم */
        #scrollbar-thumb:hover {
            opacity: 0.9;
            width: 8px; /* زيادة العرض عند التحويم */
        }

        /* تخصيص لون المؤشر في الوضع الداكن */
        body.dark-theme #scrollbar-thumb {
            background-color: var(--primary-light, #6c8fff); /* لون أفتح في الوضع الداكن */
        }

      
        body.dark-theme .sidebar::-webkit-scrollbar-thumb,
        body.dark-theme .main-content::-webkit-scrollbar-thumb,
        body.dark-theme .dropdown-menu::-webkit-scrollbar-thumb {
            background-color: var(--primary-light, #6c8fff);
        }

        /* تنسيق زر العودة إلى الأعلى */
        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            width: 45px;
            height: 45px;
            background: var(--primary-color, #4361ee);
            color: white;
            border-radius: 50%;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            visibility: hidden;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        /* موضع الزر حسب اللغة */
        html[dir="rtl"] .scroll-to-top, body.rtl .scroll-to-top {
            left: 30px;
            right: auto;
        }

        html[dir="ltr"] .scroll-to-top, body:not(.rtl) .scroll-to-top {
            right: 30px;
            left: auto;
        }

        /* حالة ظهور الزر */
        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        /* تأثير التحويم */
        .scroll-to-top:hover {
            background: var(--primary-dark, #3a56d4);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
            transform: translateY(-3px);
        }

        /* تأثير النقر */
        .scroll-to-top:active {
            transform: translateY(1px);
        }

        /* تنسيق الأيقونة */
        .scroll-to-top i {
            font-size: 18px;
        }

        /* تنسيق الزر في الوضع الداكن */
        body.dark-theme .scroll-to-top {
            background: var(--primary-light, #6c8fff);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
        }

        body.dark-theme .scroll-to-top:hover {
            background: var(--primary-color, #4361ee);
        }

        /* تنسيق زر اختصارات لوحة المفاتيح */
        #keyboard-shortcuts-btn {
            cursor: pointer;
            transition: all 0.2s ease;
        }

        #keyboard-shortcuts-btn:hover {
            color: var(--primary-color, #4361ee);
        }

        /* تنسيق نافذة اختصارات لوحة المفاتيح */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .keyboard-shortcuts-container {
            background-color: white;
            border-radius: 8px;
            width: 90%;
            max-width: 700px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            transform: translateY(-20px);
            transition: all 0.3s ease;
        }

        .modal-overlay.show .keyboard-shortcuts-container {
            transform: translateY(0);
        }

        .keyboard-shortcuts-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
        }

        .keyboard-shortcuts-header h3 {
            margin: 0;
            font-size: 1.2rem;
            color: var(--dark-text-color, #333);
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            color: #777;
            transition: color 0.2s ease;
        }

        .close-btn:hover {
            color: var(--danger-color, #dc3545);
        }

        .keyboard-shortcuts-body {
            padding: 20px;
        }

        .shortcuts-section {
            margin-bottom: 25px;
        }

        .shortcuts-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: var(--dark-text-color, #333);
            font-size: 1.1rem;
            border-bottom: 1px solid #eee;
            padding-bottom: 8px;
        }

        .shortcut-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }

        .shortcut-keys {
            display: flex;
            align-items: center;
            min-width: 150px;
        }

        .shortcut-description {
            color: var(--dark-text-color, #333);
        }

        kbd {
            display: inline-block;
            padding: 4px 8px;
            font-family: monospace;
            font-size: 0.9rem;
            color: #444;
            background-color: #f5f5f5;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
            margin: 0 5px 0 0;
        }

        /* تنسيق نافذة الاختصارات في الوضع الداكن */
        body.dark-theme .keyboard-shortcuts-container {
            background-color: var(--dark-bg-color, #1e2233);
            border: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme .keyboard-shortcuts-header {
            border-bottom: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme .keyboard-shortcuts-header h3,
        body.dark-theme .shortcuts-section h4,
        body.dark-theme .shortcut-description {
            color: var(--dark-text-color, #e1e1e1);
        }

        body.dark-theme .close-btn {
            color: #aaa;
        }

        body.dark-theme .close-btn:hover {
            color: var(--danger-color, #dc3545);
        }

        body.dark-theme .shortcuts-section h4 {
            border-bottom: 1px solid var(--dark-border-color, #2d3348);
        }

        body.dark-theme kbd {
            color: #e1e1e1;
            background-color: var(--dark-secondary-bg, #2d3348);
            border: 1px solid #444;
            box-shadow: 0 1px 1px rgba(0, 0, 0, 0.4);
        }

        /* ===== BREADCRUMB STYLES ===== */

        /* Page Title Box - الحاوي الرئيسي */
        .page-title-box {
            background: var(--light-card-bg);
            border: 1px solid var(--light-border-color);
            border-radius: var(--border-radius-lg);
            padding: 1.5rem 2rem;
            margin-bottom: 2rem;
            box-shadow: var(--box-shadow-sm);
            transition: all var(--transition-normal) var(--transition-bezier);
            position: relative;
            overflow: hidden;
        }

        /* تأثير خفيف في الخلفية */
        .page-title-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg,
                var(--primary-color) 0%,
                var(--primary-light) 50%,
                var(--primary-color) 100%);
            opacity: 0.8;
        }

        /* Dark Theme */
        body.dark-theme .page-title-box {
            background: var(--dark-card-bg);
            border-color: var(--dark-border-color);
        }

        /* Page Title Right - منطقة الـ breadcrumb */
        .page-title-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            margin-bottom: 0.75rem;
        }

        /* RTL Support */
        .rtl .page-title-right {
            justify-content: flex-start;
        }

        /* Page Title - العنوان الرئيسي */
        .page-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--light-text-color);
            margin: 0;
            line-height: 1.2;
            letter-spacing: -0.025em;
        }

        body.dark-theme .page-title {
            color: var(--dark-text-color);
        }

        /* Breadcrumb Container */
        .breadcrumb {
            display: flex;
            align-items: center;
            list-style: none;
            margin: 0;
            padding: 0;
            background: transparent;
            font-size: 0.875rem;
            gap: 0.5rem;
        }

        /* Breadcrumb Items */
        .breadcrumb-item {
            display: flex;
            align-items: center;
            color: var(--gray-600);
            font-weight: 500;
            transition: all var(--transition-fast) ease;
        }

        body.dark-theme .breadcrumb-item {
            color: var(--gray-400);
        }

        /* Breadcrumb Links */
        .breadcrumb-item a {
            color: var(--gray-600);
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: var(--border-radius);
            transition: all var(--transition-fast) ease;
            position: relative;
            font-weight: 500;
        }

        .breadcrumb-item a:hover {
            color: var(--primary-color);
            background: var(--primary-color-10);
            transform: translateY(-1px);
        }

        body.dark-theme .breadcrumb-item a {
            color: var(--gray-400);
        }

        body.dark-theme .breadcrumb-item a:hover {
            color: var(--primary-light);
            background: var(--primary-color-20);
        }

        /* Active Breadcrumb Item */
        .breadcrumb-item.active {
            color: var(--primary-color);
            font-weight: 600;
            position: relative;
        }

        body.dark-theme .breadcrumb-item.active {
            color: var(--primary-light);
        }

        /* Breadcrumb Separators */
        .breadcrumb-item + .breadcrumb-item::before {
            content: '';
            display: inline-block;
            width: 0;
            height: 0;
            border-left: 4px solid var(--gray-400);
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            margin: 0 0.5rem;
            opacity: 0.6;
            transition: all var(--transition-fast) ease;
        }

        /* RTL Separators */
        .rtl .breadcrumb-item + .breadcrumb-item::before {
            border-left: none;
            border-right: 4px solid var(--gray-400);
            transform: rotate(180deg);
        }

        body.dark-theme .breadcrumb-item + .breadcrumb-item::before {
            border-left-color: var(--gray-500);
        }

        body.dark-theme .rtl .breadcrumb-item + .breadcrumb-item::before {
            border-right-color: var(--gray-500);
        }

        /* Breadcrumb Icons */
        .breadcrumb-item i {
            margin-left: 0.25rem;
            font-size: 0.75rem;
            opacity: 0.8;
        }

        .rtl .breadcrumb-item i {
            margin-left: 0;
            margin-right: 0.25rem;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .page-title-box {
                padding: 1rem 1.5rem;
                margin-bottom: 1.5rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .breadcrumb {
                font-size: 0.8rem;
                gap: 0.25rem;
            }

            .breadcrumb-item + .breadcrumb-item::before {
                margin: 0 0.25rem;
                border-left-width: 3px;
                border-top-width: 3px;
                border-bottom-width: 3px;
            }

            .rtl .breadcrumb-item + .breadcrumb-item::before {
                border-right-width: 3px;
            }
        }

        @media (max-width: 480px) {
            .page-title-box {
                padding: 0.75rem 1rem;
            }

            .page-title {
                font-size: 1.25rem;
            }

            .breadcrumb {
                font-size: 0.75rem;
                flex-wrap: wrap;
            }

            /* إخفاء بعض العناصر في الشاشات الصغيرة جداً */
            .breadcrumb-item:not(:last-child):not(.active) {
                display: none;
            }

            .breadcrumb-item:nth-last-child(2) {
                display: flex;
            }
        }

        /* تأثيرات إضافية */
        .breadcrumb-item a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--primary-color);
            transition: all var(--transition-fast) ease;
            transform: translateX(-50%);
        }

        .breadcrumb-item a:hover::after {
            width: 80%;
        }

        /* تحسين الوضوح */
        .breadcrumb-item.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
        }

        /* ===== BREADCRUMB AND ACTIONS ROW ===== */

        /* صف الـ breadcrumb والأزرار */
        .breadcrumb-actions-row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
            margin-bottom: 0.75rem;
        }

        /*
         * ترتيب ثابت للعربية:
         * الأزرار دائماً على اليسار، الـ breadcrumb دائماً على اليمين
         */

        /* الأزرار على اليسار */
        .breadcrumb-actions-row .page-title-actions {
            order: 1;
        }

        /* Breadcrumb على اليمين */
        .breadcrumb-actions-row .breadcrumb {
            margin: 0;
            order: 2;
            margin-left: auto; /* يدفع الـ breadcrumb لليمين */
        }

        /* Page Title منفصل */
        .page-title {
            margin: 0 0 1rem 0;
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
        }

        /* حاوي الأزرار في صف الـ breadcrumb - دائماً على اليسار */
        .breadcrumb-actions-row .page-title-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
            flex-shrink: 0;
            order: 1; /* الأزرار دائماً أولاً (على اليسار) */
        }

        /* أزرار منطقة العنوان */
        .page-title-actions .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
            font-weight: 500;
            border-radius: var(--border-radius);
            text-decoration: none;
            transition: all var(--transition-fast) ease;
            border: 1px solid transparent;
            white-space: nowrap;
        }

        .page-title-actions .btn i {
            font-size: 0.875rem;
        }

        /* أزرار ملونة */
        .page-title-actions .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-title-actions .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .page-title-actions .btn-success:hover {
            background: var(--success-dark);
            border-color: var(--success-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-info {
            background: var(--info-color);
            color: white;
            border-color: var(--info-color);
        }

        .page-title-actions .btn-info:hover {
            background: var(--info-dark);
            border-color: var(--info-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-warning {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }

        .page-title-actions .btn-warning:hover {
            background: var(--warning-dark);
            border-color: var(--warning-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-danger {
            background: var(--danger-color);
            color: white;
            border-color: var(--danger-color);
        }

        .page-title-actions .btn-danger:hover {
            background: var(--danger-dark);
            border-color: var(--danger-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-secondary {
            background: var(--gray-500);
            color: white;
            border-color: var(--gray-500);
        }

        .page-title-actions .btn-secondary:hover {
            background: var(--gray-600);
            border-color: var(--gray-600);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* تحسين التخطيط للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-title-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.75rem;
            }

            .rtl .page-title-header {
                align-items: flex-end;
                flex-direction: column; /* Override RTL flex-direction for mobile */
            }

            /* في الشاشات الصغيرة: العنوان أولاً، ثم الأزرار */
            .page-title-header .page-title {
                order: 1;
                width: 100%;
            }

            .page-title-actions {
                order: 2;
                width: 100%;
                justify-content: flex-start;
            }

            .rtl .page-title-actions {
                justify-content: flex-end;
            }
        }

        @media (max-width: 480px) {
            .page-title-actions .btn {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                gap: 0.25rem;
            }

            .page-title-actions .btn span {
                display: none;
            }

            .page-title-actions .btn i {
                font-size: 0.8rem;
            }
        }



        /* أزرار منطقة العنوان */
        .page-title-actions .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            padding: 0.375rem 0.75rem;
            font-size: 0.8rem;
            font-weight: 500;
            border-radius: var(--border-radius);
            text-decoration: none;
            transition: all var(--transition-fast) ease;
            border: 1px solid transparent;
            white-space: nowrap;
        }

        .page-title-actions .btn i {
            font-size: 0.875rem;
        }

        /* أزرار ملونة */
        .page-title-actions .btn-primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .page-title-actions .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-success {
            background: var(--success-color);
            color: white;
            border-color: var(--success-color);
        }

        .page-title-actions .btn-success:hover {
            background: var(--success-dark);
            border-color: var(--success-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-info {
            background: var(--info-color);
            color: white;
            border-color: var(--info-color);
        }

        .page-title-actions .btn-info:hover {
            background: var(--info-dark);
            border-color: var(--info-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-warning {
            background: var(--warning-color);
            color: white;
            border-color: var(--warning-color);
        }

        .page-title-actions .btn-warning:hover {
            background: var(--warning-dark);
            border-color: var(--warning-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-danger {
            background: var(--danger-color);
            color: white;
            border-color: var(--danger-color);
        }

        .page-title-actions .btn-danger:hover {
            background: var(--danger-dark);
            border-color: var(--danger-dark);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-title-actions .btn-secondary {
            background: var(--gray-500);
            color: white;
            border-color: var(--gray-500);
        }

        .page-title-actions .btn-secondary:hover {
            background: var(--gray-600);
            border-color: var(--gray-600);
            color: white;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        /* تحسين التخطيط للشاشات الصغيرة */
        @media (max-width: 768px) {
            .page-title-right .d-flex {
                flex-direction: column;
                align-items: flex-start !important;
                gap: 1rem;
            }

            .page-title-actions {
                order: -1;
                width: 100%;
                justify-content: flex-start;
            }

            .breadcrumb {
                width: 100%;
            }
        }

        @media (max-width: 480px) {
            .page-title-actions .btn {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                gap: 0.25rem;
            }

            .page-title-actions .btn span {
                display: none;
            }

            .page-title-actions .btn i {
                font-size: 0.8rem;
            }
        }

        /* تحسين RTL */
        .rtl .page-title-actions {
            flex-direction: row-reverse;
        }

        .rtl .page-title-right .d-flex {
            flex-direction: row-reverse;
        }

        @media (max-width: 768px) {
            .rtl .page-title-right .d-flex {
                flex-direction: column;
                align-items: flex-end !important;
            }

            .rtl .page-title-actions {
                justify-content: flex-end;
            }
        }