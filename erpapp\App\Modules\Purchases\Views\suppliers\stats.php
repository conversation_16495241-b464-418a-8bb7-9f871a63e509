<?php
/**
 * صفحة إحصائيات الموردين
 * ملف منفصل لعرض إحصائيات مفصلة للموردين
 */

// التحقق من الصلاحيات
if (!is_logged_in()) {
    redirect('login');
}

// التحقق من صلاحية الوصول للوحدة
if (!has_permission('purchases_view')) {
    show_error('ليس لديك صلاحية للوصول إلى هذه الصفحة');
}

// الحصول على الإحصائيات من Controller
$stats = $stats ?? [];
$supplierGroups = $supplierGroups ?? [];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
    ['title' => 'الإحصائيات', 'active' => true]
];

// إعداد الإحصائيات الأساسية
$basic_stats = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats['total_suppliers'] ?? 0,
        'icon' => 'fas fa-truck',
        'color' => 'primary',
        'description' => 'العدد الكلي للموردين المسجلين'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats['active_suppliers'] ?? 0,
        'icon' => 'fas fa-check-circle',
        'color' => 'success',
        'description' => 'الموردين الذين حالتهم نشطة'
    ],
    [
        'title' => 'الموردين غير النشطين',
        'value' => $stats['inactive_suppliers'] ?? 0,
        'icon' => 'fas fa-times-circle',
        'color' => 'secondary',
        'description' => 'الموردين الذين حالتهم غير نشطة'
    ],
    [
        'title' => 'الموردين المعلقين',
        'value' => $stats['suspended_suppliers'] ?? 0,
        'icon' => 'fas fa-pause-circle',
        'color' => 'warning',
        'description' => 'الموردين الذين حالتهم معلقة'
    ]
];

// إعداد إحصائيات المجموعات
$group_stats = [];
if (!empty($supplierGroups)) {
    foreach ($supplierGroups as $group) {
        $group_stats[] = [
            'title' => $group['name_ar'],
            'value' => $stats['groups'][$group['group_number']] ?? 0,
            'icon' => 'fas fa-folder',
            'color' => 'info',
            'description' => 'عدد الموردين في هذه المجموعة'
        ];
    }
}

// إعداد إحصائيات إضافية
$additional_stats = [
    [
        'title' => 'موردين جدد هذا الشهر',
        'value' => $stats['new_this_month'] ?? 0,
        'icon' => 'fas fa-calendar-plus',
        'color' => 'success',
        'description' => 'الموردين المضافين خلال الشهر الحالي'
    ],
    [
        'title' => 'موردين محدثين مؤخراً',
        'value' => $stats['updated_recently'] ?? 0,
        'icon' => 'fas fa-edit',
        'color' => 'info',
        'description' => 'الموردين المحدثين خلال آخر 7 أيام'
    ],
    [
        'title' => 'متوسط الموردين شهرياً',
        'value' => $stats['monthly_average'] ?? 0,
        'icon' => 'fas fa-chart-line',
        'color' => 'primary',
        'description' => 'متوسط عدد الموردين المضافين شهرياً'
    ],
    [
        'title' => 'إجمالي المعاملات',
        'value' => $stats['total_transactions'] ?? 0,
        'icon' => 'fas fa-exchange-alt',
        'color' => 'warning',
        'description' => 'إجمالي المعاملات مع الموردين'
    ]
];

// إعداد بيانات الرسوم البيانية
$chart_data = [
    'status_chart' => [
        'labels' => ['نشط', 'غير نشط', 'معلق'],
        'data' => [
            $stats['active_suppliers'] ?? 0,
            $stats['inactive_suppliers'] ?? 0,
            $stats['suspended_suppliers'] ?? 0
        ],
        'colors' => ['#28a745', '#6c757d', '#ffc107']
    ],
    'monthly_chart' => [
        'labels' => $stats['monthly_labels'] ?? [],
        'data' => $stats['monthly_data'] ?? [],
        'color' => '#007bff'
    ]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات الموردين - <?= APP_NAME ?></title>
    
    <!-- تحميل CSS -->
    <?php include BASE_PATH . '/App/Layouts/head.php'; ?>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>

<?php include BASE_PATH . '/App/Layouts/main.php'; ?>

<div class="container-fluid">
    
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <?php foreach ($breadcrumb as $item): ?>
                <?php if (isset($item['active'])): ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= $item['title'] ?></li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">📊 إحصائيات الموردين</h1>
            <p class="text-muted">تقرير شامل عن حالة الموردين والإحصائيات</p>
        </div>
        <div>
            <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i>
                العودة للموردين
            </a>
            <button onclick="window.print()" class="btn btn-outline-secondary">
                <i class="fas fa-print"></i>
                طباعة
            </button>
        </div>
    </div>

    <!-- الإحصائيات الأساسية -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">📈 الإحصائيات الأساسية</h4>
        </div>
        <?php foreach ($basic_stats as $stat): ?>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-<?= $stat['color'] ?> text-white rounded-circle p-3">
                                    <i class="<?= $stat['icon'] ?> fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="card-title mb-1"><?= $stat['title'] ?></h6>
                                <h3 class="text-<?= $stat['color'] ?> mb-0"><?= number_format($stat['value']) ?></h3>
                                <small class="text-muted"><?= $stat['description'] ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- إحصائيات المجموعات -->
    <?php if (!empty($group_stats)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">📁 إحصائيات المجموعات</h4>
        </div>
        <?php foreach ($group_stats as $stat): ?>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-<?= $stat['color'] ?> text-white rounded-circle p-3">
                                    <i class="<?= $stat['icon'] ?> fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="card-title mb-1"><?= $stat['title'] ?></h6>
                                <h3 class="text-<?= $stat['color'] ?> mb-0"><?= number_format($stat['value']) ?></h3>
                                <small class="text-muted"><?= $stat['description'] ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">📊 توزيع الموردين حسب الحالة</h5>
                </div>
                <div class="card-body">
                    <canvas id="statusChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">📈 الموردين الجدد شهرياً</h5>
                </div>
                <div class="card-body">
                    <canvas id="monthlyChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات الإضافية -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3">📋 إحصائيات إضافية</h4>
        </div>
        <?php foreach ($additional_stats as $stat): ?>
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-<?= $stat['color'] ?> text-white rounded-circle p-3">
                                    <i class="<?= $stat['icon'] ?> fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="card-title mb-1"><?= $stat['title'] ?></h6>
                                <h3 class="text-<?= $stat['color'] ?> mb-0"><?= number_format($stat['value']) ?></h3>
                                <small class="text-muted"><?= $stat['description'] ?></small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

</div>

<script>
// رسم بياني للحالات
const statusCtx = document.getElementById('statusChart').getContext('2d');
new Chart(statusCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode($chart_data['status_chart']['labels']) ?>,
        datasets: [{
            data: <?= json_encode($chart_data['status_chart']['data']) ?>,
            backgroundColor: <?= json_encode($chart_data['status_chart']['colors']) ?>,
            borderWidth: 2,
            borderColor: '#fff'
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// رسم بياني شهري
const monthlyCtx = document.getElementById('monthlyChart').getContext('2d');
new Chart(monthlyCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode($chart_data['monthly_chart']['labels']) ?>,
        datasets: [{
            label: 'الموردين الجدد',
            data: <?= json_encode($chart_data['monthly_chart']['data']) ?>,
            borderColor: '<?= $chart_data['monthly_chart']['color'] ?>',
            backgroundColor: '<?= $chart_data['monthly_chart']['color'] ?>20',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});
</script>

</body>
</html>
