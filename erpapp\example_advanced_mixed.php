<?php
/**
 * مثال متقدم على الصفحة المختلطة
 * لوحة تحكم المبيعات مع إحصائيات وجدول الطلبات
 */

require_once 'loader.php';

echo "<h1>📈 لوحة تحكم المبيعات - صفحة مختلطة متقدمة</h1>";

// إحصائيات المبيعات
$sales_stats = [
    [
        'title' => 'مبيعات اليوم',
        'value' => '15,750 ريال',
        'icon' => 'fas fa-calendar-day',
        'color' => 'success',
        'description' => 'إجمالي مبيعات اليوم الحالي'
    ],
    [
        'title' => 'مبيعات الشهر',
        'value' => '485,250 ريال',
        'icon' => 'fas fa-calendar-alt',
        'color' => 'primary',
        'description' => 'إجمالي مبيعات الشهر الحالي'
    ],
    [
        'title' => 'عدد الطلبات',
        'value' => 127,
        'icon' => 'fas fa-shopping-cart',
        'color' => 'info',
        'description' => 'عدد الطلبات هذا الشهر'
    ],
    [
        'title' => 'العملاء الجدد',
        'value' => 23,
        'icon' => 'fas fa-user-plus',
        'color' => 'warning',
        'description' => 'عملاء جدد هذا الشهر'
    ],
    [
        'title' => 'متوسط قيمة الطلب',
        'value' => '3,820 ريال',
        'icon' => 'fas fa-chart-line',
        'color' => 'success',
        'description' => 'متوسط قيمة الطلب الواحد'
    ],
    [
        'title' => 'الطلبات المعلقة',
        'value' => 8,
        'icon' => 'fas fa-clock',
        'color' => 'danger',
        'description' => 'طلبات تحتاج متابعة'
    ]
];

// أعمدة جدول الطلبات الأخيرة
$orders_columns = [
    [
        'title' => 'رقم الطلب',
        'field' => 'order_number',
        'type' => 'link',
        'url' => 'sales/orders/{entity_number}/view',
        'subtitle_field' => 'order_date'
    ],
    [
        'title' => 'العميل',
        'field' => 'customer_name',
        'type' => 'text'
    ],
    [
        'title' => 'قيمة الطلب',
        'field' => 'total_amount',
        'type' => 'text'
    ],
    [
        'title' => 'الحالة',
        'field' => 'status',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'completed' => 'success',
                'pending' => 'warning',
                'cancelled' => 'danger',
                'processing' => 'info'
            ],
            'texts' => [
                'completed' => 'مكتمل',
                'pending' => 'معلق',
                'cancelled' => 'ملغي',
                'processing' => 'قيد المعالجة'
            ]
        ]
    ],
    [
        'title' => 'طريقة الدفع',
        'field' => 'payment_method',
        'type' => 'text'
    ],
    [
        'title' => 'الإجراءات',
        'field' => 'actions',
        'type' => 'actions',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'sales/orders/{entity_number}/view',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض التفاصيل'
            ],
            [
                'type' => 'link',
                'url' => 'sales/orders/{entity_number}/invoice',
                'class' => 'btn-outline-success',
                'icon' => 'fas fa-file-invoice',
                'title' => 'طباعة الفاتورة'
            ],
            [
                'type' => 'link',
                'url' => 'sales/orders/{entity_number}/edit',
                'class' => 'btn-outline-warning',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ]
        ]
    ]
];

// بيانات الطلبات الأخيرة
$recent_orders = [
    [
        'entity_number' => 1001,
        'order_number' => 'ORD-2024-001',
        'order_date' => '2024-01-15 14:30',
        'customer_name' => 'أحمد محمد علي',
        'total_amount' => '4,250 ريال',
        'status' => 'completed',
        'payment_method' => 'نقداً'
    ],
    [
        'entity_number' => 1002,
        'order_number' => 'ORD-2024-002',
        'order_date' => '2024-01-15 15:45',
        'customer_name' => 'فاطمة أحمد',
        'total_amount' => '2,890 ريال',
        'status' => 'processing',
        'payment_method' => 'بطاقة ائتمان'
    ],
    [
        'entity_number' => 1003,
        'order_number' => 'ORD-2024-003',
        'order_date' => '2024-01-15 16:20',
        'customer_name' => 'محمد عبدالله',
        'total_amount' => '6,750 ريال',
        'status' => 'pending',
        'payment_method' => 'تحويل بنكي'
    ],
    [
        'entity_number' => 1004,
        'order_number' => 'ORD-2024-004',
        'order_date' => '2024-01-15 17:10',
        'customer_name' => 'سارة خالد',
        'total_amount' => '1,950 ريال',
        'status' => 'completed',
        'payment_method' => 'نقداً'
    ],
    [
        'entity_number' => 1005,
        'order_number' => 'ORD-2024-005',
        'order_date' => '2024-01-15 18:00',
        'customer_name' => 'عبدالرحمن سعد',
        'total_amount' => '3,420 ريال',
        'status' => 'cancelled',
        'payment_method' => 'بطاقة ائتمان'
    ]
];

// إجراءات سريعة
$quick_actions = [
    [
        'type' => 'primary',
        'url' => 'sales/orders/create',
        'icon' => 'fas fa-plus',
        'text' => 'طلب جديد'
    ],
    [
        'type' => 'success',
        'url' => 'sales/customers/create',
        'icon' => 'fas fa-user-plus',
        'text' => 'عميل جديد'
    ],
    [
        'type' => 'info',
        'url' => 'sales/reports/daily',
        'icon' => 'fas fa-chart-bar',
        'text' => 'تقرير يومي'
    ],
    [
        'type' => 'warning',
        'url' => 'sales/orders/pending',
        'icon' => 'fas fa-clock',
        'text' => 'الطلبات المعلقة'
    ]
];

// فلاتر البحث
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الطلبات',
        'placeholder' => 'رقم الطلب، اسم العميل...',
        'icon' => 'fas fa-search',
        'col_size' => 4
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة الطلب',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-filter',
        'col_size' => 4,
        'options' => [
            'completed' => 'مكتمل',
            'pending' => 'معلق',
            'processing' => 'قيد المعالجة',
            'cancelled' => 'ملغي'
        ]
    ],
    [
        'name' => 'payment_method',
        'type' => 'select',
        'label' => 'طريقة الدفع',
        'placeholder' => 'جميع الطرق',
        'icon' => 'fas fa-credit-card',
        'col_size' => 4,
        'options' => [
            'cash' => 'نقداً',
            'credit_card' => 'بطاقة ائتمان',
            'bank_transfer' => 'تحويل بنكي'
        ]
    ]
];

// pagination للطلبات
$pagination = [
    'current_page' => 1,
    'total_pages' => 8,
    'total_items' => 127,
    'per_page' => 20,
    'start_item' => 1,
    'end_item' => 20,
    'has_previous' => false,
    'has_next' => true,
    'previous_page' => 0,
    'next_page' => 2
];

// breadcrumb
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => 'dashboard'],
    ['title' => 'المبيعات', 'url' => 'sales'],
    ['title' => 'لوحة التحكم', 'active' => true]
];

// عرض الصفحة المختلطة
render_page([
    'title' => 'لوحة تحكم المبيعات',
    'module' => 'sales',
    'entity' => 'dashboard',
    
    // الإحصائيات (البطاقات)
    'stats' => $sales_stats,
    
    // جدول الطلبات الأخيرة
    'columns' => $orders_columns,
    'data' => $recent_orders,
    
    // الإعدادات
    'actions' => $quick_actions,
    'filters_config' => $filters_config,
    'pagination' => $pagination,
    'filters' => [],
    'breadcrumb' => $breadcrumb,
    
    'empty_state' => [
        'icon' => 'mdi mdi-cart-outline',
        'message' => 'لا توجد طلبات مسجلة',
        'action' => [
            'url' => 'sales/orders/create',
            'text' => 'إنشاء أول طلب'
        ]
    ]
]);

echo "<hr>";
echo "<div class='alert alert-info'>";
echo "<h4>💡 مميزات الصفحة المختلطة:</h4>";
echo "<ul>";
echo "<li><strong>إحصائيات شاملة:</strong> 6 بطاقات تعرض أهم المؤشرات</li>";
echo "<li><strong>جدول تفاعلي:</strong> عرض الطلبات الأخيرة مع إمكانية البحث والفلترة</li>";
echo "<li><strong>إجراءات سريعة:</strong> أزرار للوصول السريع للمهام الشائعة</li>";
echo "<li><strong>تصميم متجاوب:</strong> يعمل على جميع الأجهزة</li>";
echo "<li><strong>نظام ذكي:</strong> يحدد نوع الصفحة تلقائياً</li>";
echo "</ul>";
echo "</div>";
?>
