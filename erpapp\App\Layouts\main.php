<!DOCTYPE html>
<html lang="<?= current_lang() ?>" dir="<?= current_lang() == 'ar' ? 'rtl' : 'ltr' ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $title ?? APP_NAME ?> - <?= APP_NAME ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Dynamic CSS Loading (Unified System) -->
    <?php load_css_files(); ?>

    <!-- RTL Support -->
    <?php load_rtl_css(); ?>

    <!-- External Libraries CSS Only (CDN) -->
    <?php load_external_css_libraries(); ?>

    <!-- Page-specific CSS -->
    <?php if (isset($page_css)): ?>
        <?php foreach ($page_css as $css): ?>
            <link rel="stylesheet" href="<?= rtrim(APP_URL, '/') ?>/public/css/<?= $css ?>">
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Inline CSS -->
    <?php if (isset($inline_css)): ?>
        <style><?= $inline_css ?></style>
    <?php endif; ?>

    <!-- Custom CSS -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
    </style>
</head>
<body class="<?= current_lang() == 'ar' ? 'rtl' : '' ?> <?= is_logged_in() && current_user()['theme'] == 'dark' ? 'dark-theme' : '' ?>">
    <!-- شريط التمرير المخصص -->
    <div id="custom-scrollbar">
        <div id="scrollbar-thumb"></div>
    </div>

    <!-- زر العودة إلى الأعلى -->
    <button id="scroll-to-top" class="scroll-to-top" aria-label="<?= __('العودة إلى الأعلى') ?>">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- نافذة اختصارات لوحة المفاتيح -->
    <div id="keyboard-shortcuts-modal" class="modal-overlay">
        <div class="keyboard-shortcuts-container">
            <div class="keyboard-shortcuts-header">
                <h3><?= __('اختصارات لوحة المفاتيح') ?></h3>
                <button id="close-keyboard-shortcuts" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="keyboard-shortcuts-body">
                <div class="shortcuts-section">
                    <h4><?= __('عام') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>?</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('عرض/إخفاء دليل الاختصارات') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>H</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الصفحة الرئيسية') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('الواجهة') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>T</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تبديل الثيم (فاتح/داكن)') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>L</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تبديل اللغة (العربية/الإنجليزية)') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>S</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تصغير/توسيع القائمة الجانبية') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>C</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('تغيير عرض المحتوى') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('التنقل') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>P</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الملف الشخصي') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>N</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الإشعارات') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>M</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الرسائل') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>I</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('الذهاب إلى الإعدادات') ?></div>
                    </div>
                </div>

                <div class="shortcuts-section">
                    <h4><?= __('أخرى') ?></h4>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Esc</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('إغلاق النوافذ المنبثقة') ?></div>
                    </div>
                    <div class="shortcut-item">
                        <div class="shortcut-keys">
                            <kbd>Alt</kbd> + <kbd>↑</kbd>
                        </div>
                        <div class="shortcut-description"><?= __('العودة إلى أعلى الصفحة') ?></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if (is_logged_in()): ?>
        <!-- Logged in layout -->
        <div class="wrapper">
            <?php
            // تحديد نوع الصفحة
            $pageType = getCurrentPageType();

            // عرض السايدبار المناسب
            if ($pageType === 'module') {
                include BASE_PATH . '/App/Layouts/sidebar_modules.php';
            } else {
                include BASE_PATH . '/App/Layouts/sidebar_system.php';
            }
            ?>

            <!-- Content -->
            <div class="content <?= is_logged_in() && current_user()['Content_Mode'] == 'small' ? 'small-width' : '' ?> <?= is_logged_in() && current_user()['sidebar_mode'] == 'hide' ? 'expanded' : '' ?>" id="content">
                <!-- Sidebar Backdrop for Mobile -->
                <div class="sidebar-backdrop" id="sidebarBackdrop"></div>

                <!-- ===== TOPBAR - MODERN & PROFESSIONAL ===== -->
                <header class="topbar" role="banner">
                    <!-- Mobile Sidebar Toggle -->
                    <button class="mobile-sidebar-toggle"
                            id="mobileSidebarToggle"
                            type="button"
                            aria-label="<?= __('فتح القائمة الجانبية') ?>"
                            title="<?= __('فتح القائمة الجانبية') ?>">
                        <i class="fas fa-bars" aria-hidden="true"></i>
                    </button>

                    <!-- Topbar Actions Container -->
                    <div class="topbar-actions" role="toolbar" aria-label="<?= __('أدوات التطبيق') ?>">

                        <!-- Notifications Action -->
                        <div class="topbar-action"
                             id="notifications-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الإشعارات') ?> (3 <?= __('جديد') ?>)"
                             title="<?= __('الإشعارات') ?>"
                             data-action="notifications">
                            <i class="fas fa-bell" aria-hidden="true"></i>
                            <span class="topbar-action-badge" aria-label="3 <?= __('إشعارات جديدة') ?>">3</span>
                        </div>

                        <!-- Messages Action -->
                        <div class="topbar-action"
                             id="messages-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الرسائل') ?> (5 <?= __('جديد') ?>)"
                             title="<?= __('الرسائل') ?>"
                             data-action="messages">
                            <i class="fas fa-envelope" aria-hidden="true"></i>
                            <span class="topbar-action-badge" aria-label="5 <?= __('رسائل جديدة') ?>">5</span>
                        </div>

                        <!-- Quick Settings Action -->
                        <div class="topbar-action"
                             id="quick-settings-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('الإعدادات السريعة') ?>"
                             title="<?= __('الإعدادات السريعة') ?>"
                             data-action="quick-settings">
                            <i class="fas fa-cog" aria-hidden="true"></i>
                        </div>

                        <!-- Keyboard Shortcuts Action -->
                        <div class="topbar-action"
                             id="keyboard-shortcuts-btn"
                             role="button"
                             tabindex="0"
                             aria-label="<?= __('اختصارات لوحة المفاتيح') ?>"
                             title="<?= __('اختصارات لوحة المفاتيح') ?> (Alt + /)"
                             data-action="keyboard-shortcuts">
                            <i class="fas fa-keyboard" aria-hidden="true"></i>
                        </div>

                        <!-- User Profile Dropdown -->
                        <div class="topbar-dropdown" id="user-dropdown">
                            <div class="topbar-user"
                                 role="button"
                                 tabindex="0"
                                 aria-expanded="false"
                                 aria-haspopup="true"
                                 aria-label="<?= __('قائمة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                 data-toggle="topbar-dropdown">

                                <!-- User Avatar -->
                                <div class="topbar-user-avatar">
                                    <?php if (!empty(current_user()['ProfilePicture']) && file_exists(BASE_PATH . '/' . current_user()['ProfilePicture'])): ?>
                                        <img src="<?= base_url(current_user()['ProfilePicture']) ?>"
                                             alt="<?= __('صورة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                             loading="lazy">
                                    <?php else: ?>
                                        <img src="https://ui-avatars.com/api/?name=<?= urlencode(current_user()['FirstName']) ?>+<?= urlencode(current_user()['LastName']) ?>&background=2196F3&color=fff&size=128&font-size=0.6"
                                             alt="<?= __('صورة المستخدم') ?>: <?= current_user()['FirstName'] ?> <?= current_user()['LastName'] ?>"
                                             loading="lazy">
                                    <?php endif; ?>
                                </div>

                                <!-- User Info -->
                                <div class="topbar-user-info">
                                    <span class="topbar-user-name"><?= htmlspecialchars(current_user()['FirstName'] . ' ' . current_user()['LastName']) ?></span>
                                </div>

                                <!-- Dropdown Arrow -->
                                <i class="fas fa-chevron-down topbar-user-dropdown" aria-hidden="true"></i>
                            </div>

                            <!-- User Dropdown Menu -->
                            <div class="topbar-dropdown-menu topbar-dropdown-menu-end"
                                 role="menu"
                                 aria-labelledby="user-dropdown"
                                 data-popper="static">

                                <!-- User Info Header -->
                                <div class="topbar-dropdown-header" role="presentation">
                                    <i class="fas fa-user-circle me-2" aria-hidden="true"></i>
                                    <?= __('إعدادات المستخدم') ?>
                                </div>

                                <!-- Profile Link -->
                                <a class="topbar-dropdown-item"
                                   href="<?= base_url('profile') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('عرض الملف الشخصي') ?>">
                                    <i class="fas fa-user" aria-hidden="true"></i>
                                    <span><?= __('الملف الشخصي') ?></span>
                                </a>

                                <!-- Account Settings -->
                                <a class="topbar-dropdown-item"
                                   href="<?= base_url('settings/account') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('إعدادات الحساب') ?>">
                                    <i class="fas fa-user-cog" aria-hidden="true"></i>
                                    <span><?= __('إعدادات الحساب') ?></span>
                                </a>

                                <!-- Theme Toggle -->
                                <?php if (current_user()['theme'] == 'light'): ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/theme/dark') ?>"
                                       data-toggle="theme"
                                       data-theme="dark"
                                       role="menuitem"
                                       aria-label="<?= __('تفعيل الوضع الداكن') ?>">
                                        <i class="fas fa-moon" aria-hidden="true"></i>
                                        <span><?= __('الوضع الداكن') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/theme/light') ?>"
                                       data-toggle="theme"
                                       data-theme="light"
                                       role="menuitem"
                                       aria-label="<?= __('تفعيل الوضع الفاتح') ?>">
                                        <i class="fas fa-sun" aria-hidden="true"></i>
                                        <span><?= __('الوضع الفاتح') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Language Toggle -->
                                <?php if (current_user()['language'] == 'العربية'): ?>
                                    <a class="dropdown-item language-switch"
                                       href="<?= base_url('language/en') ?>"
                                       data-lang="en"
                                       role="menuitem"
                                       aria-label="<?= __('تغيير اللغة إلى الإنجليزية') ?>">
                                        <i class="fas fa-language" aria-hidden="true"></i>
                                        <span>English</span>
                                    </a>
                                <?php else: ?>
                                    <a class="dropdown-item language-switch"
                                       href="<?= base_url('language/ar') ?>"
                                       data-lang="ar"
                                       role="menuitem"
                                       aria-label="<?= __('تغيير اللغة إلى العربية') ?>">
                                        <i class="fas fa-language" aria-hidden="true"></i>
                                        <span>العربية</span>
                                    </a>
                                <?php endif; ?>

                                <!-- Divider -->
                                <div class="topbar-dropdown-divider" role="separator"></div>

                                <!-- Interface Settings Header -->
                                <div class="topbar-dropdown-header" role="presentation">
                                    <i class="fas fa-cogs me-2" aria-hidden="true"></i>
                                    <?= __('إعدادات الواجهة') ?>
                                </div>

                                <!-- Sidebar Mode Toggle -->
                                <?php if (current_user()['sidebar_mode'] == 'show'): ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/sidebar/hide') ?>"
                                       data-toggle="sidebar-mode"
                                       data-mode="hide"
                                       role="menuitem"
                                       aria-label="<?= __('تصغير القائمة الجانبية') ?>">
                                        <i class="fas fa-angle-double-left" aria-hidden="true"></i>
                                        <span><?= __('تصغير القائمة الجانبية') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/sidebar/show') ?>"
                                       data-toggle="sidebar-mode"
                                       data-mode="show"
                                       role="menuitem"
                                       aria-label="<?= __('توسيع القائمة الجانبية') ?>">
                                        <i class="fas fa-angle-double-right" aria-hidden="true"></i>
                                        <span><?= __('توسيع القائمة الجانبية') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Content Mode Toggle -->
                                <?php if (current_user()['Content_Mode'] == 'large'): ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/content/small') ?>"
                                       data-toggle="content-mode"
                                       data-mode="small"
                                       role="menuitem"
                                       aria-label="<?= __('تقليص المحتوى') ?>">
                                        <i class="fas fa-compress" aria-hidden="true"></i>
                                        <span><?= __('تقليص المحتوى') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('settings/content/large') ?>"
                                       data-toggle="content-mode"
                                       data-mode="large"
                                       role="menuitem"
                                       aria-label="<?= __('توسيع المحتوى') ?>">
                                        <i class="fas fa-expand" aria-hidden="true"></i>
                                        <span><?= __('توسيع المحتوى') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Dashboard Toggle -->
                                <?php
                                // تحديد نوع الصفحة الحالية
                                $currentPageType = getCurrentPageType();
                                ?>
                                <?php if ($currentPageType === 'module'): ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('dashboard') ?>"
                                       role="menuitem"
                                       aria-label="<?= __('التبديل إلى لوحة تحكم النظام') ?>">
                                        <i class="fas fa-cogs" aria-hidden="true"></i>
                                        <span><?= __('لوحة تحكم النظام') ?></span>
                                    </a>
                                <?php else: ?>
                                    <a class="topbar-dropdown-item"
                                       href="<?= base_url('home') ?>"
                                       role="menuitem"
                                       aria-label="<?= __('التبديل إلى لوحة تحكم الوحدات') ?>">
                                        <i class="fas fa-cubes" aria-hidden="true"></i>
                                        <span><?= __('لوحة تحكم الوحدات') ?></span>
                                    </a>
                                <?php endif; ?>

                                <!-- Notifications Settings -->
                                <a class="topbar-dropdown-item"
                                   href="<?= base_url('settings/notifications') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('إعدادات الإشعارات') ?>">
                                    <i class="fas fa-bell" aria-hidden="true"></i>
                                    <span><?= __('إعدادات الإشعارات') ?></span>
                                </a>

                                <!-- Divider -->
                                <div class="topbar-dropdown-divider" role="separator"></div>

                                <!-- Help & Support -->
                                <a class="topbar-dropdown-item"
                                   href="<?= base_url('help') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('المساعدة والدعم') ?>">
                                    <i class="fas fa-question-circle" aria-hidden="true"></i>
                                    <span><?= __('المساعدة والدعم') ?></span>
                                </a>

                                <!-- Logout -->
                                <a class="topbar-dropdown-item topbar-dropdown-item-with-icon-right"
                                   href="<?= base_url('logout') ?>"
                                   role="menuitem"
                                   aria-label="<?= __('تسجيل الخروج من النظام') ?>">
                                    <i class="fas fa-sign-out-alt" aria-hidden="true"></i>
                                    <span><?= __('تسجيل الخروج') ?></span>
                                    <i class="fas fa-arrow-right topbar-dropdown-item-icon-right" aria-hidden="true"></i>
                                </a>

                                <!-- Footer -->
                                <div class="topbar-dropdown-footer" role="presentation">
                                    <small>
                                        <i class="fas fa-info-circle me-1" aria-hidden="true"></i>
                                        <?= APP_NAME ?> v<?= APP_VERSION ?>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </header>

                <!-- Main Content -->
                <main class="main-content">
                    <?php
    // استخدام Toastr بدلاً من display_flash
    $success = flash('success');
    if ($success) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.success("' . addslashes($success['message']) . '", "' . __('نجاح') . '");
                }
            });
        </script>';
    }

    $error = flash('error');
    if ($error) {
        echo '<script>
            document.addEventListener("DOMContentLoaded", function() {
                if (typeof toastr !== "undefined") {
                    toastr.error("' . addslashes($error['message']) . '", "' . __('خطأ') . '");
                }
            });
        </script>';
    }
    ?>

                    <?= $content ?>
                </main>
            </div>
        </div>
    <?php else: ?>
        <!-- Guest layout -->
        <?= $content ?>
    <?php endif; ?>

    <!-- External Libraries JS (CDN) -->
    <?php load_external_js_libraries(); ?>

    <!-- Dynamic JavaScript Loading (Unified System) -->
    <?php
    // تحميل متغيرات مخصصة للصفحة
    $custom_variables = [
        'PAGE_TITLE' => $title ?? '',
        'USER_PERMISSIONS' => $_SESSION['user_permissions'] ?? [],
        'NOTIFICATIONS_COUNT' => 3, // مثال
        'MESSAGES_COUNT' => 5 // مثال
    ];

    // تحميل متغيرات JavaScript
    load_js_variables($custom_variables);

    // تحميل ملفات JavaScript المخصصة فقط
    load_js_files();
    ?>

    <!-- تم نقل جميع الكود إلى ملفات JavaScript منفصلة -->

</body>
</html>