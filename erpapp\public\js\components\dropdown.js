/**
 * Custom Dropdown Component (بديل Bootstrap)
 * يوفر وظائف dropdown بدون الحاجة لـ Bootstrap
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // ===== DROPDOWN FUNCTIONALITY ===== //
    
    // العثور على جميع عناصر dropdown (عدا التوب بار)
    const dropdowns = document.querySelectorAll('[data-toggle="dropdown"]');

    dropdowns.forEach(function(trigger) {
        const dropdown = trigger.closest('.dropdown');
        const menu = dropdown.querySelector('.dropdown-menu');

        // تجاهل عناصر التوب بار - سيتم التعامل معها في topbar.js
        if (trigger.classList.contains('topbar-user')) {
            return;
        }

        if (!menu) return;
        
        // إضافة event listener للنقر
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // إغلاق جميع dropdown الأخرى
            closeAllDropdowns();
            
            // تبديل حالة dropdown الحالي
            toggleDropdown(dropdown, menu, trigger);
        });
        
        // إضافة event listener للوحة المفاتيح
        trigger.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                trigger.click();
            }
        });
    });
    
    // إغلاق dropdown عند النقر خارجه (عدا التوب بار)
    document.addEventListener('click', function(e) {
        const clickedDropdown = e.target.closest('.dropdown');
        if (!clickedDropdown) {
            closeAllDropdowns();
        }
    });
    
    // إغلاق dropdown عند الضغط على Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllDropdowns();
        }
    });
    
    // ===== DROPDOWN FUNCTIONS ===== //
    
    function toggleDropdown(dropdown, menu, trigger) {
        const isOpen = dropdown.classList.contains('show');
        
        if (isOpen) {
            closeDropdown(dropdown, menu, trigger);
        } else {
            openDropdown(dropdown, menu, trigger);
        }
    }
    
    function openDropdown(dropdown, menu, trigger) {
        // إضافة class للعرض
        dropdown.classList.add('show');
        menu.classList.add('show');

        // تحديث aria attributes
        trigger.setAttribute('aria-expanded', 'true');

        // تحديد موضع dropdown
        positionDropdown(dropdown, menu, trigger);

        // إضافة animation
        menu.style.opacity = '0';
        menu.style.transform = 'translateY(-10px)';

        requestAnimationFrame(function() {
            menu.style.transition = 'opacity 0.15s ease, transform 0.15s ease';
            menu.style.opacity = '1';
            menu.style.transform = 'translateY(0)';
        });

        // focus على أول عنصر في القائمة
        const firstItem = menu.querySelector('.dropdown-item');
        if (firstItem) {
            setTimeout(() => firstItem.focus(), 100);
        }
    }
    
    function closeDropdown(dropdown, menu, trigger) {
        // إزالة classes
        dropdown.classList.remove('show');
        menu.classList.remove('show');

        // تحديث aria attributes
        trigger.setAttribute('aria-expanded', 'false');

        // إزالة animation styles
        menu.style.transition = '';
        menu.style.opacity = '';
        menu.style.transform = '';
    }
    
    function closeAllDropdowns() {
        const openDropdowns = document.querySelectorAll('.dropdown.show');

        openDropdowns.forEach(function(dropdown) {
            const menu = dropdown.querySelector('.dropdown-menu');
            const trigger = dropdown.querySelector('[data-toggle="dropdown"]');

            // تجاهل عناصر التوب بار - سيتم التعامل معها في topbar.js
            if (trigger && trigger.classList.contains('topbar-user')) {
                return;
            }

            if (menu && trigger) {
                closeDropdown(dropdown, menu, trigger);
            }
        });
    }
    
    function positionDropdown(dropdown, menu, trigger) {
        // إعادة تعيين الموضع
        menu.style.position = 'absolute';
        menu.style.top = '';
        menu.style.left = '';
        menu.style.right = '';
        menu.style.bottom = '';
        
        const triggerRect = trigger.getBoundingClientRect();
        const menuRect = menu.getBoundingClientRect();
        const viewportHeight = window.innerHeight;
        const viewportWidth = window.innerWidth;
        
        // تحديد الاتجاه (RTL/LTR)
        const isRTL = document.documentElement.dir === 'rtl';
        
        // حساب المساحة المتاحة
        const spaceBelow = viewportHeight - triggerRect.bottom;
        const spaceAbove = triggerRect.top;
        const spaceRight = viewportWidth - triggerRect.right;
        const spaceLeft = triggerRect.left;
        
        // تحديد الموضع العمودي
        if (spaceBelow >= menuRect.height || spaceBelow >= spaceAbove) {
            // عرض تحت العنصر
            menu.style.top = '100%';
        } else {
            // عرض فوق العنصر
            menu.style.bottom = '100%';
        }
        
        // تحديد الموضع الأفقي
        if (isRTL) {
            if (spaceLeft >= menuRect.width || spaceLeft >= spaceRight) {
                menu.style.right = '0';
            } else {
                menu.style.left = '0';
            }
        } else {
            if (spaceRight >= menuRect.width || spaceRight >= spaceLeft) {
                menu.style.left = '0';
            } else {
                menu.style.right = '0';
            }
        }
    }
    
    // ===== DROPDOWN NAVIGATION ===== //
    
    // التنقل بالأسهم داخل dropdown
    document.addEventListener('keydown', function(e) {
        const activeDropdown = document.querySelector('.dropdown.show');
        if (!activeDropdown) return;
        
        const menu = activeDropdown.querySelector('.dropdown-menu');
        const items = menu.querySelectorAll('.dropdown-item:not(.disabled)');
        const currentFocus = document.activeElement;
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            const currentIndex = Array.from(items).indexOf(currentFocus);
            const nextIndex = currentIndex < items.length - 1 ? currentIndex + 1 : 0;
            items[nextIndex].focus();
        }
        
        if (e.key === 'ArrowUp') {
            e.preventDefault();
            const currentIndex = Array.from(items).indexOf(currentFocus);
            const prevIndex = currentIndex > 0 ? currentIndex - 1 : items.length - 1;
            items[prevIndex].focus();
        }
        
        if (e.key === 'Home') {
            e.preventDefault();
            items[0].focus();
        }
        
        if (e.key === 'End') {
            e.preventDefault();
            items[items.length - 1].focus();
        }
    });
    
    // ===== RESPONSIVE DROPDOWN ===== //
    
    // تحديث موضع dropdown عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        const openDropdowns = document.querySelectorAll('.dropdown.show');
        
        openDropdowns.forEach(function(dropdown) {
            const menu = dropdown.querySelector('.dropdown-menu');
            const trigger = dropdown.querySelector('[data-toggle="dropdown"]');
            
            if (menu && trigger) {
                positionDropdown(dropdown, menu, trigger);
            }
        });
    });
    
    // ===== DROPDOWN ITEM ACTIONS ===== //
    
    // معالجة النقر على عناصر dropdown
    document.addEventListener('click', function(e) {
        const dropdownItem = e.target.closest('.dropdown-item');
        if (!dropdownItem) return;
        
        // إغلاق dropdown بعد النقر (إلا إذا كان العنصر يحتوي على data-no-close)
        if (!dropdownItem.hasAttribute('data-no-close')) {
            const dropdown = dropdownItem.closest('.dropdown');
            if (dropdown) {
                const menu = dropdown.querySelector('.dropdown-menu');
                const trigger = dropdown.querySelector('[data-toggle="dropdown"]');
                
                if (menu && trigger) {
                    setTimeout(() => {
                        closeDropdown(dropdown, menu, trigger);
                    }, 100);
                }
            }
        }
    });
    
    // ===== ACCESSIBILITY IMPROVEMENTS ===== //
    
    // إضافة ARIA labels تلقائياً
    dropdowns.forEach(function(trigger) {
        if (!trigger.hasAttribute('aria-haspopup')) {
            trigger.setAttribute('aria-haspopup', 'true');
        }
        
        if (!trigger.hasAttribute('aria-expanded')) {
            trigger.setAttribute('aria-expanded', 'false');
        }
    });
    
    // تحسين التنقل بـ Tab
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Tab') {
            const activeDropdown = document.querySelector('.dropdown.show');
            if (activeDropdown) {
                const menu = activeDropdown.querySelector('.dropdown-menu');
                const items = menu.querySelectorAll('.dropdown-item:not(.disabled)');
                const currentFocus = document.activeElement;
                
                // إذا كان التركيز على آخر عنصر وتم الضغط على Tab
                if (currentFocus === items[items.length - 1] && !e.shiftKey) {
                    const trigger = activeDropdown.querySelector('[data-toggle="dropdown"]');
                    closeDropdown(activeDropdown, menu, trigger);
                }
                
                // إذا كان التركيز على أول عنصر وتم الضغط على Shift+Tab
                if (currentFocus === items[0] && e.shiftKey) {
                    const trigger = activeDropdown.querySelector('[data-toggle="dropdown"]');
                    closeDropdown(activeDropdown, menu, trigger);
                    trigger.focus();
                }
            }
        }
    });
    
    console.log('✅ Custom Dropdown Component loaded successfully');
});
