/**
 * ERP Select - ملف التهيئة والاستخدام
 * تهيئة تلقائية ووظائف مساعدة
 */

(function() {
    'use strict';

    // إعدادات افتراضية للنظام
    const SYSTEM_DEFAULTS = {
        rtl: document.dir === 'rtl' || document.documentElement.lang === 'ar',
        theme: document.documentElement.getAttribute('data-theme') || 'default',
        searchDelay: 300,
        maxResults: 50,
        cacheResults: true
    };

    // كلاس مدير ERP Select
    class ERPSelectManager {
        constructor() {
            this.instances = new Map();
            this.globalConfig = { ...SYSTEM_DEFAULTS };
            this.init();
        }

        init() {
            // تهيئة تلقائية عند تحميل DOM
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', () => this.autoInit());
            } else {
                this.autoInit();
            }

            // مراقبة التغييرات في DOM
            this.observeDOM();

            console.log('🚀 تم تهيئة مدير ERP Select');
        }

        autoInit() {
            // تهيئة جميع العناصر مع data-erp-select
            this.initElements('[data-erp-select]');
            
            // تهيئة عناصر الفلاتر
            this.initFilterSelects();
            
            // تهيئة عناصر النماذج
            this.initFormSelects();
        }

        initElements(selector) {
            const elements = document.querySelectorAll(selector);
            
            elements.forEach(element => {
                if (element.tagName === 'SELECT' && !this.instances.has(element)) {
                    this.createInstance(element);
                }
            });
        }

        createInstance(element) {
            try {
                // جمع الإعدادات من data attributes
                const config = this.parseElementConfig(element);
                
                // دمج مع الإعدادات العامة
                const finalConfig = { ...this.globalConfig, ...config };
                
                // إنشاء المثيل
                const instance = new ERPSelect(element, finalConfig);
                this.instances.set(element, instance);
                
                // إضافة معالجات خاصة
                this.attachSpecialHandlers(instance, element);
                
                return instance;
            } catch (error) {
                console.error('خطأ في إنشاء مثيل ERP Select:', error);
                return null;
            }
        }

        parseElementConfig(element) {
            const config = {};
            
            // قراءة الإعدادات من data attributes
            const dataset = element.dataset;
            
            // الإعدادات الأساسية
            if (dataset.erpSelectConfig) {
                try {
                    Object.assign(config, JSON.parse(dataset.erpSelectConfig));
                } catch (e) {
                    console.warn('تعذر تحليل إعدادات ERP Select:', dataset.erpSelectConfig);
                }
            }
            
            // إعدادات مباشرة
            if (dataset.placeholder) config.placeholder = dataset.placeholder;
            if (dataset.searchable !== undefined) config.searchable = dataset.searchable !== 'false';
            if (dataset.clearable !== undefined) config.clearable = dataset.clearable !== 'false';
            if (dataset.multiple !== undefined) config.multiple = dataset.multiple !== 'false';
            if (dataset.size) config.size = dataset.size;
            if (dataset.theme) config.theme = dataset.theme;
            
            // إعدادات AJAX
            if (dataset.ajaxUrl) {
                config.ajax = {
                    url: dataset.ajaxUrl,
                    data: dataset.ajaxData ? JSON.parse(dataset.ajaxData) : {}
                };
            }
            
            // إعدادات خاصة بالفلاتر
            if (dataset.filterType) {
                config.filterType = dataset.filterType;
                this.applyFilterConfig(config, dataset.filterType);
            }
            
            return config;
        }

        applyFilterConfig(config, filterType) {
            switch (filterType) {
                case 'status':
                    config.searchable = false;
                    config.placeholder = 'جميع الحالات';
                    break;
                    
                case 'user':
                    config.searchable = true;
                    config.placeholder = 'اختر المستخدم';
                    config.searchPlaceholder = 'ابحث عن المستخدم...';
                    break;
                    
                case 'category':
                    config.searchable = true;
                    config.allowCreate = true;
                    config.placeholder = 'اختر أو أضف فئة';
                    break;
                    
                case 'country':
                    config.searchable = true;
                    config.placeholder = 'اختر البلد';
                    config.searchPlaceholder = 'ابحث عن البلد...';
                    break;
                    
                case 'group':
                    config.searchable = true;
                    config.placeholder = 'اختر المجموعة';
                    break;
            }
        }

        attachSpecialHandlers(instance, element) {
            // معالج خاص للفلاتر
            if (element.closest('.filter-modal, .datatable-filters')) {
                this.attachFilterHandlers(instance, element);
            }
            
            // معالج خاص للنماذج
            if (element.closest('form')) {
                this.attachFormHandlers(instance, element);
            }
        }

        attachFilterHandlers(instance, element) {
            // تحديث عداد الفلاتر النشطة
            instance.originalSelect.addEventListener('erp-select:change', () => {
                this.updateFilterCount();
            });
            
            // إضافة تأثيرات بصرية للفلاتر النشطة
            instance.originalSelect.addEventListener('erp-select:change', (e) => {
                const hasValue = e.detail.value && e.detail.value !== '';
                instance.container.classList.toggle('erp-select--has-value', hasValue);
            });
        }

        attachFormHandlers(instance, element) {
            // التحقق من صحة البيانات
            instance.originalSelect.addEventListener('erp-select:change', () => {
                this.validateField(element);
            });
            
            // حفظ تلقائي (إذا كان مفعل)
            if (element.dataset.autoSave === 'true') {
                instance.originalSelect.addEventListener('erp-select:change', () => {
                    this.autoSave(element);
                });
            }
        }

        initFilterSelects() {
            // تهيئة خاصة لعناصر الفلاتر
            const filterSelects = document.querySelectorAll('.filter-modal select, .datatable-filters select');
            
            filterSelects.forEach(select => {
                if (!this.instances.has(select)) {
                    const config = {
                        theme: 'primary',
                        size: 'medium',
                        searchDelay: 200
                    };
                    
                    this.createInstance(select, config);
                }
            });
        }

        initFormSelects() {
            // تهيئة خاصة لعناصر النماذج
            const formSelects = document.querySelectorAll('form select:not([data-erp-select])');
            
            formSelects.forEach(select => {
                if (!this.instances.has(select) && !select.hasAttribute('data-no-erp-select')) {
                    const config = {
                        theme: 'default',
                        size: 'medium'
                    };
                    
                    this.createInstance(select, config);
                }
            });
        }

        observeDOM() {
            // مراقبة إضافة عناصر جديدة
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // البحث عن عناصر select جديدة
                            const selects = node.querySelectorAll ? 
                                node.querySelectorAll('select[data-erp-select]') : [];
                            
                            selects.forEach(select => {
                                if (!this.instances.has(select)) {
                                    this.createInstance(select);
                                }
                            });
                            
                            // إذا كان العنصر نفسه select
                            if (node.tagName === 'SELECT' && node.hasAttribute('data-erp-select')) {
                                if (!this.instances.has(node)) {
                                    this.createInstance(node);
                                }
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }

        updateFilterCount() {
            // تحديث عداد الفلاتر النشطة
            const filterForm = document.querySelector('#filtersForm, .datatable-filters');
            if (!filterForm) return;
            
            const activeFilters = Array.from(filterForm.querySelectorAll('select'))
                .filter(select => {
                    const instance = this.instances.get(select);
                    if (instance) {
                        const value = instance.getValue();
                        return value && value !== '' && value.length > 0;
                    }
                    return select.value && select.value !== '';
                }).length;
            
            // تحديث شارة العداد
            const badge = document.querySelector('.datatable-filter-badge');
            if (badge) {
                if (activeFilters > 0) {
                    badge.textContent = activeFilters;
                    badge.style.display = 'inline-block';
                } else {
                    badge.style.display = 'none';
                }
            }
            
            // تحديث أيقونة الفلتر
            const filterIcon = document.querySelector('.datatable-filter-icon');
            if (filterIcon) {
                filterIcon.classList.toggle('has-active-filters', activeFilters > 0);
            }
        }

        validateField(element) {
            // التحقق من صحة الحقل
            const isRequired = element.hasAttribute('required');
            const value = element.value;
            
            if (isRequired && (!value || value === '')) {
                element.classList.add('is-invalid');
                return false;
            } else {
                element.classList.remove('is-invalid');
                element.classList.add('is-valid');
                return true;
            }
        }

        autoSave(element) {
            // حفظ تلقائي للحقل
            const form = element.closest('form');
            if (!form) return;
            
            const formData = new FormData(form);
            const url = form.getAttribute('data-auto-save-url') || form.action;
            
            if (!url) return;
            
            fetch(url, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    element.classList.add('auto-saved');
                    setTimeout(() => {
                        element.classList.remove('auto-saved');
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('خطأ في الحفظ التلقائي:', error);
            });
        }

        // API عام
        getInstance(element) {
            return this.instances.get(element);
        }

        destroyInstance(element) {
            const instance = this.instances.get(element);
            if (instance) {
                instance.destroy();
                this.instances.delete(element);
            }
        }

        updateGlobalConfig(config) {
            Object.assign(this.globalConfig, config);
        }

        refreshAll() {
            this.instances.forEach((instance, element) => {
                instance.updateOptions();
            });
        }

        destroyAll() {
            this.instances.forEach((instance, element) => {
                instance.destroy();
            });
            this.instances.clear();
        }
    }

    // إنشاء مدير عام
    const manager = new ERPSelectManager();

    // تصدير للاستخدام العام
    window.ERPSelectManager = manager;

    // دوال مساعدة سريعة
    window.initERPSelect = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const instances = [];
        
        elements.forEach(element => {
            if (element.tagName === 'SELECT') {
                const instance = manager.createInstance(element, options);
                if (instance) instances.push(instance);
            }
        });
        
        return instances.length === 1 ? instances[0] : instances;
    };

    window.destroyERPSelect = function(selector) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            manager.destroyInstance(element);
        });
    };

    // تحديث الثيم عند تغييره
    document.addEventListener('theme-changed', (e) => {
        manager.updateGlobalConfig({ theme: e.detail.theme });
        manager.refreshAll();
    });

    console.log('✅ تم تحميل ERP Select بنجاح');

})();
