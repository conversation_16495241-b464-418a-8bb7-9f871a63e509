<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مكتبة التواريخ - Flatpickr</title>
    
    <!-- Bootstrap RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Flatpickr CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
            color: #1e293b;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 2rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h2 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
            color: #1e293b;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
        }
        
        .demo-item {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid #e2e8f0;
        }
        
        .demo-item h3 {
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #374151;
        }
        
        .demo-item label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #4b5563;
        }
        
        .demo-item input {
            width: 100%;
            margin-bottom: 1rem;
        }
        
        .demo-item .description {
            font-size: 0.9rem;
            color: #6b7280;
            margin-top: 0.5rem;
            line-height: 1.5;
        }
        
        .controls {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #f1f5f9;
            color: #475569;
            border: 1px solid #e2e8f0;
        }
        
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .output {
            background: #1e293b;
            color: #f1f5f9;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin-top: 1rem;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .flatpickr-input {
            background: white !important;
            border: 2px solid #e2e8f0 !important;
            border-radius: 8px !important;
            padding: 0.75rem !important;
            font-size: 1rem !important;
            transition: all 0.2s ease !important;
        }
        
        .flatpickr-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            outline: none !important;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🗓️ اختبار مكتبة التواريخ Flatpickr</h1>
        <p>مكتبة تواريخ متقدمة مع دعم كامل للغة العربية و RTL</p>
    </div>

    <!-- Controls -->
    <div class="controls">
        <button class="btn btn-primary" onclick="testAllDates()">
            <i class="fas fa-play"></i>
            اختبار جميع التواريخ
        </button>
        <button class="btn btn-secondary" onclick="clearAllDates()">
            <i class="fas fa-eraser"></i>
            مسح جميع التواريخ
        </button>
        <button class="btn btn-secondary" onclick="showValues()">
            <i class="fas fa-eye"></i>
            عرض القيم
        </button>
    </div>

    <!-- Basic Date Types -->
    <div class="demo-section">
        <h2><i class="fas fa-calendar-day"></i> أنواع التواريخ الأساسية</h2>
        <div class="demo-grid">
            
            <div class="demo-item">
                <h3>تاريخ بسيط</h3>
                <label>اختر التاريخ:</label>
                <input type="text" 
                       id="simple-date"
                       data-date 
                       data-date-type="date"
                       placeholder="اختر التاريخ"
                       class="form-control">
                <div class="description">منتقي تاريخ بسيط مع دعم اللغة العربية</div>
            </div>
            
            <div class="demo-item">
                <h3>تاريخ ووقت</h3>
                <label>اختر التاريخ والوقت:</label>
                <input type="text" 
                       id="datetime"
                       data-date 
                       data-date-type="datetime"
                       placeholder="اختر التاريخ والوقت"
                       class="form-control">
                <div class="description">منتقي تاريخ ووقت مع نظام 24 ساعة</div>
            </div>
            
            <div class="demo-item">
                <h3>وقت فقط</h3>
                <label>اختر الوقت:</label>
                <input type="text" 
                       id="time-only"
                       data-date 
                       data-date-type="time"
                       placeholder="اختر الوقت"
                       class="form-control">
                <div class="description">منتقي وقت فقط بدون تاريخ</div>
            </div>
            
            <div class="demo-item">
                <h3>نطاق تواريخ</h3>
                <label>اختر نطاق التواريخ:</label>
                <input type="text" 
                       id="date-range"
                       data-date 
                       data-date-type="range"
                       placeholder="من تاريخ - إلى تاريخ"
                       class="form-control">
                <div class="description">اختيار نطاق من تاريخ البداية إلى النهاية</div>
            </div>
            
        </div>
    </div>

    <!-- Advanced Date Types -->
    <div class="demo-section">
        <h2><i class="fas fa-calendar-alt"></i> أنواع متقدمة</h2>
        <div class="demo-grid">
            
            <div class="demo-item">
                <h3>تواريخ متعددة</h3>
                <label>اختر عدة تواريخ:</label>
                <input type="text" 
                       id="multiple-dates"
                       data-date 
                       data-date-type="multiple"
                       placeholder="اختر عدة تواريخ"
                       class="form-control">
                <div class="description">اختيار تواريخ متعددة غير متتالية</div>
            </div>
            
            <div class="demo-item">
                <h3>تاريخ مع حدود</h3>
                <label>تاريخ محدود:</label>
                <input type="text" 
                       id="limited-date"
                       data-date 
                       data-date-type="date"
                       data-min-date="2024-01-01"
                       data-max-date="2024-12-31"
                       data-default-date="2024-06-15"
                       placeholder="تاريخ محدود (2024 فقط)"
                       class="form-control">
                <div class="description">تاريخ محدود بين 1 يناير و 31 ديسمبر 2024</div>
            </div>
            
            <div class="demo-item">
                <h3>تاريخ مخصص</h3>
                <label>تنسيق مخصص:</label>
                <input type="text" 
                       id="custom-format"
                       data-date 
                       data-date-type="date"
                       data-date-format="d/m/Y"
                       placeholder="تنسيق مخصص"
                       class="form-control">
                <div class="description">تنسيق عرض مخصص: يوم/شهر/سنة</div>
            </div>
            
            <div class="demo-item">
                <h3>تاريخ مضمن</h3>
                <label>تقويم مضمن:</label>
                <input type="text" 
                       id="inline-date"
                       data-date 
                       data-date-type="date"
                       data-date-inline="true"
                       class="form-control">
                <div class="description">تقويم مضمن يظهر دائماً</div>
            </div>
            
        </div>
    </div>

    <!-- Output Console -->
    <div class="demo-section">
        <h2><i class="fas fa-terminal"></i> وحدة التحكم</h2>
        <div id="console-output" class="output">
            جاهز لعرض النتائج...
        </div>
    </div>

</div>

<!-- JavaScript Libraries -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/ar.js"></script>

<!-- Date Component -->
<script src="public/js/components/date.js"></script>

<script>
// وظائف التحكم
function testAllDates() {
    log('🧪 بدء اختبار جميع التواريخ...');
    
    // تعيين قيم تجريبية
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // تاريخ بسيط
    const simpleDatePicker = window.dateInstances?.['simple-date'];
    if (simpleDatePicker) {
        simpleDatePicker.setValue(today);
        log('✅ تم تعيين التاريخ البسيط: ' + simpleDatePicker.getFormattedValue());
    }
    
    // تاريخ ووقت
    const datetimePicker = window.dateInstances?.['datetime'];
    if (datetimePicker) {
        datetimePicker.setValue(today);
        log('✅ تم تعيين التاريخ والوقت: ' + datetimePicker.getFormattedValue());
    }
    
    // وقت فقط
    const timePicker = window.dateInstances?.['time-only'];
    if (timePicker) {
        timePicker.setValue('14:30');
        log('✅ تم تعيين الوقت: ' + timePicker.getFormattedValue());
    }
    
    // نطاق تواريخ
    const rangePicker = window.dateInstances?.['date-range'];
    if (rangePicker) {
        rangePicker.setValue([today, tomorrow]);
        log('✅ تم تعيين نطاق التواريخ: ' + rangePicker.getFormattedValue());
    }
    
    log('🎉 تم الانتهاء من الاختبار!');
}

function clearAllDates() {
    log('🧹 مسح جميع التواريخ...');
    
    if (window.dateInstances) {
        Object.keys(window.dateInstances).forEach(id => {
            const instance = window.dateInstances[id];
            if (instance && instance.clear) {
                instance.clear();
                log('✅ تم مسح: ' + id);
            }
        });
    }
    
    log('🎉 تم مسح جميع التواريخ!');
}

function showValues() {
    log('👁️ عرض جميع القيم الحالية...');
    
    if (window.dateInstances) {
        Object.keys(window.dateInstances).forEach(id => {
            const instance = window.dateInstances[id];
            if (instance) {
                const value = instance.getFormattedValue();
                log(`📅 ${id}: ${value || 'فارغ'}`);
            }
        });
    } else {
        log('⚠️ لا توجد مثيلات تواريخ متاحة');
    }
}

function log(message) {
    const output = document.getElementById('console-output');
    const timestamp = new Date().toLocaleTimeString('ar-SA');
    output.innerHTML += `<div>[${timestamp}] ${message}</div>`;
    output.scrollTop = output.scrollHeight;
}

// تهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    log('🚀 تم تحميل صفحة اختبار التواريخ');
    
    // حفظ مراجع المثيلات للاختبار
    window.dateInstances = {};
    
    // إضافة مستمعي الأحداث
    document.querySelectorAll('input[data-date]').forEach(input => {
        input.addEventListener('date:change', function(e) {
            log(`🔄 تغيير في ${this.id}: ${e.detail.dateStr}`);
        });
        
        input.addEventListener('date:open', function(e) {
            log(`📂 فتح منتقي التاريخ: ${this.id}`);
        });
        
        input.addEventListener('date:close', function(e) {
            log(`📁 إغلاق منتقي التاريخ: ${this.id}`);
        });
    });
    
    // حفظ المثيلات (سيتم إنشاؤها بواسطة date.js)
    setTimeout(() => {
        log('📋 تم تهيئة جميع منتقيات التواريخ');
        log('ℹ️ استخدم الأزرار أعلاه لاختبار الوظائف');
    }, 1000);
});
</script>

</body>
</html>
