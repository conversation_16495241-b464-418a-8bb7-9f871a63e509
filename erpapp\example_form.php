<?php
/**
 * مثال على استخدام نظام النماذج المنفصل
 */

require_once 'loader.php';

// إعداد حقول النموذج
$form_config = [
    'action' => 'purchases/suppliers/store',
    'method' => 'POST',
    'fields' => [
        [
            'name' => 'name_ar',
            'label' => 'الاسم بالعربية',
            'type' => 'text',
            'required' => true,
            'placeholder' => 'أدخل الاسم بالعربية'
        ],
        [
            'name' => 'name_en',
            'label' => 'الاسم بالإنجليزية',
            'type' => 'text',
            'required' => true,
            'placeholder' => 'Enter name in English'
        ],
        [
            'name' => 'email',
            'label' => 'البريد الإلكتروني',
            'type' => 'email',
            'required' => true,
            'placeholder' => '<EMAIL>'
        ],
        [
            'name' => 'phone',
            'label' => 'رقم الهاتف',
            'type' => 'text',
            'placeholder' => '+966 50 123 4567'
        ],
        [
            'name' => 'status',
            'label' => 'الحالة',
            'type' => 'select',
            'required' => true,
            'placeholder' => 'اختر الحالة',
            'options' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ],
        [
            'name' => 'description',
            'label' => 'الوصف',
            'type' => 'textarea',
            'rows' => 4,
            'placeholder' => 'أدخل وصف المورد'
        ],
        [
            'name' => 'is_featured',
            'label' => 'مورد مميز',
            'type' => 'checkbox',
            'checkbox_label' => 'تحديد كمورد مميز'
        ]
    ]
];

// بيانات النموذج (للتعديل)
$form_data = [
    'name_ar' => 'مورد تجريبي',
    'name_en' => 'Test Supplier',
    'email' => '<EMAIL>',
    'status' => 'active'
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
    ['title' => 'إضافة مورد', 'active' => true]
];

// إعداد إجراءات إضافية
$actions = [
    [
        'type' => 'info',
        'url' => 'purchases/suppliers',
        'icon' => 'fas fa-list',
        'text' => 'عرض جميع الموردين'
    ]
];

// استخدام نظام النماذج
render_form_page([
    'title' => 'إضافة مورد جديد',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'form' => $form_config,
    'data' => $form_data,
    'breadcrumb' => $breadcrumb,
    'actions' => $actions
]);
?>
