/**
 * ERP Select - أنماط مخصصة متقدمة
 * تصميم احترافي مصمم خصيص<|im_start|> لأنظمة ERP
 */

/* ===== المتغيرات الأساسية ===== */
:root {
    --erp-select-primary: #3b82f6;
    --erp-select-primary-dark: #2563eb;
    --erp-select-success: #10b981;
    --erp-select-danger: #ef4444;
    --erp-select-warning: #f59e0b;
    
    --erp-select-bg: #ffffff;
    --erp-select-bg-hover: #f8fafc;
    --erp-select-bg-focus: #ffffff;
    --erp-select-bg-disabled: #f1f5f9;
    
    --erp-select-border: #e2e8f0;
    --erp-select-border-hover: #cbd5e1;
    --erp-select-border-focus: var(--erp-select-primary);
    --erp-select-border-disabled: #e2e8f0;
    
    --erp-select-text: #1e293b;
    --erp-select-text-muted: #64748b;
    --erp-select-text-placeholder: #94a3b8;
    --erp-select-text-disabled: #94a3b8;
    
    --erp-select-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --erp-select-shadow-focus: 0 0 0 3px rgba(59, 130, 246, 0.1);
    --erp-select-shadow-dropdown: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    --erp-select-radius: 8px;
    --erp-select-radius-sm: 6px;
    --erp-select-radius-lg: 12px;
    
    --erp-select-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --erp-select-transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
}

/* الوضع الداكن */
[data-theme="dark"] {
    --erp-select-bg: #1e293b;
    --erp-select-bg-hover: #334155;
    --erp-select-bg-focus: #1e293b;
    --erp-select-bg-disabled: #0f172a;
    
    --erp-select-border: #334155;
    --erp-select-border-hover: #475569;
    --erp-select-border-focus: var(--erp-select-primary);
    --erp-select-border-disabled: #334155;
    
    --erp-select-text: #f1f5f9;
    --erp-select-text-muted: #94a3b8;
    --erp-select-text-placeholder: #64748b;
    --erp-select-text-disabled: #64748b;
    
    --erp-select-shadow-dropdown: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* ===== الحاوي الرئيسي ===== */
.erp-select {
    position: relative;
    display: inline-block;
    width: 100%;
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
}

.erp-select * {
    box-sizing: border-box;
}

/* ===== الزر المحفز ===== */
.erp-select__trigger {
    position: relative;
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 42px;
    padding: 8px 40px 8px 12px;
    background: var(--erp-select-bg);
    border: 2px solid var(--erp-select-border);
    border-radius: var(--erp-select-radius);
    color: var(--erp-select-text);
    cursor: pointer;
    transition: var(--erp-select-transition);
    outline: none;
    box-shadow: var(--erp-select-shadow);
}

.erp-select__trigger:hover {
    background: var(--erp-select-bg-hover);
    border-color: var(--erp-select-border-hover);
}

.erp-select__trigger:focus,
.erp-select--open .erp-select__trigger {
    background: var(--erp-select-bg-focus);
    border-color: var(--erp-select-border-focus);
    box-shadow: var(--erp-select-shadow-focus);
}

/* النص المعروض */
.erp-select__display {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: var(--erp-select-text);
    font-weight: 500;
}

.erp-select__display--placeholder {
    color: var(--erp-select-text-placeholder);
    font-weight: 400;
}

/* أيقونة السهم */
.erp-select__arrow {
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    color: var(--erp-select-text-muted);
    transition: var(--erp-select-transition);
    pointer-events: none;
}

.erp-select--open .erp-select__arrow {
    transform: translateY(-50%) rotate(180deg);
    color: var(--erp-select-primary);
}

/* زر المسح */
.erp-select__clear {
    position: absolute;
    top: 50%;
    right: 36px;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background: none;
    border: none;
    border-radius: 50%;
    color: var(--erp-select-text-muted);
    cursor: pointer;
    transition: var(--erp-select-transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.7;
}

.erp-select__clear:hover {
    background: var(--erp-select-bg-hover);
    color: var(--erp-select-danger);
    opacity: 1;
}

/* ===== القائمة المنسدلة ===== */
.erp-select__dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    margin-top: 4px;
    background: var(--erp-select-bg);
    border: 1px solid var(--erp-select-border);
    border-radius: var(--erp-select-radius);
    box-shadow: var(--erp-select-shadow-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: var(--erp-select-transition);
    max-height: 300px;
    overflow: hidden;
    backdrop-filter: blur(8px);
}

.erp-select--open .erp-select__dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.erp-select__dropdown--up {
    top: auto;
    bottom: 100%;
    margin-top: 0;
    margin-bottom: 4px;
    transform: translateY(10px);
}

.erp-select--open .erp-select__dropdown--up {
    transform: translateY(0);
}

/* ===== البحث ===== */
.erp-select__search {
    position: relative;
    padding: 12px;
    border-bottom: 1px solid var(--erp-select-border);
}

.erp-select__search-input {
    width: 100%;
    padding: 8px 12px 8px 36px;
    background: var(--erp-select-bg-hover);
    border: 1px solid var(--erp-select-border);
    border-radius: var(--erp-select-radius-sm);
    color: var(--erp-select-text);
    font-size: 14px;
    outline: none;
    transition: var(--erp-select-transition);
}

.erp-select__search-input:focus {
    background: var(--erp-select-bg-focus);
    border-color: var(--erp-select-border-focus);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.erp-select__search-input::placeholder {
    color: var(--erp-select-text-placeholder);
}

.erp-select__search-icon {
    position: absolute;
    top: 50%;
    left: 24px;
    transform: translateY(-50%);
    color: var(--erp-select-text-muted);
    pointer-events: none;
}

/* ===== قائمة الخيارات ===== */
.erp-select__options {
    max-height: 240px;
    overflow-y: auto;
    padding: 4px 0;
}

.erp-select__options::-webkit-scrollbar {
    width: 6px;
}

.erp-select__options::-webkit-scrollbar-track {
    background: transparent;
}

.erp-select__options::-webkit-scrollbar-thumb {
    background: var(--erp-select-border);
    border-radius: 3px;
}

.erp-select__options::-webkit-scrollbar-thumb:hover {
    background: var(--erp-select-border-hover);
}

/* ===== الخيارات ===== */
.erp-select__option {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: var(--erp-select-text);
    cursor: pointer;
    transition: var(--erp-select-transition-fast);
    border-radius: 0;
    margin: 0 4px;
    border-radius: var(--erp-select-radius-sm);
}

.erp-select__option:hover {
    background: var(--erp-select-bg-hover);
    color: var(--erp-select-primary);
}

.erp-select__option--selected {
    background: rgba(59, 130, 246, 0.1);
    color: var(--erp-select-primary);
    font-weight: 600;
}

.erp-select__option--selected:hover {
    background: rgba(59, 130, 246, 0.15);
}

.erp-select__option--disabled {
    color: var(--erp-select-text-disabled);
    cursor: not-allowed;
    opacity: 0.6;
}

.erp-select__option--disabled:hover {
    background: transparent;
    color: var(--erp-select-text-disabled);
}

.erp-select__option--focused {
    background: var(--erp-select-bg-hover);
    outline: 2px solid var(--erp-select-primary);
    outline-offset: -2px;
}

/* نص الخيار */
.erp-select__option-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* checkbox للخيارات المتعددة */
.erp-select__option-checkbox {
    margin-right: 8px;
    color: var(--erp-select-text-muted);
    transition: var(--erp-select-transition-fast);
}

.erp-select__option--selected .erp-select__option-checkbox {
    color: var(--erp-select-primary);
}

/* ===== حالات خاصة ===== */
.erp-select__no-results,
.erp-select__loading,
.erp-select__error {
    padding: 16px;
    text-align: center;
    color: var(--erp-select-text-muted);
    font-style: italic;
}

.erp-select__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.erp-select__loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--erp-select-border);
    border-top-color: var(--erp-select-primary);
    border-radius: 50%;
    animation: erp-select-spin 1s linear infinite;
}

.erp-select__error {
    color: var(--erp-select-danger);
}

@keyframes erp-select-spin {
    to {
        transform: rotate(360deg);
    }
}

/* ===== الأحجام ===== */
.erp-select--small .erp-select__trigger {
    min-height: 36px;
    padding: 6px 32px 6px 10px;
    font-size: 13px;
}

.erp-select--small .erp-select__arrow {
    right: 10px;
}

.erp-select--small .erp-select__clear {
    right: 28px;
    width: 16px;
    height: 16px;
}

.erp-select--large .erp-select__trigger {
    min-height: 48px;
    padding: 12px 48px 12px 16px;
    font-size: 16px;
}

.erp-select--large .erp-select__arrow {
    right: 16px;
}

.erp-select--large .erp-select__clear {
    right: 40px;
    width: 24px;
    height: 24px;
}

/* ===== الحالة المعطلة ===== */
.erp-select--disabled .erp-select__trigger {
    background: var(--erp-select-bg-disabled);
    border-color: var(--erp-select-border-disabled);
    color: var(--erp-select-text-disabled);
    cursor: not-allowed;
    box-shadow: none;
}

.erp-select--disabled .erp-select__trigger:hover {
    background: var(--erp-select-bg-disabled);
    border-color: var(--erp-select-border-disabled);
}

.erp-select--disabled .erp-select__arrow {
    color: var(--erp-select-text-disabled);
}

/* ===== RTL Support ===== */
.erp-select--rtl .erp-select__trigger {
    padding: 8px 12px 8px 40px;
}

.erp-select--rtl .erp-select__arrow {
    right: auto;
    left: 12px;
}

.erp-select--rtl .erp-select__clear {
    right: auto;
    left: 36px;
}

.erp-select--rtl .erp-select__search-input {
    padding: 8px 36px 8px 12px;
}

.erp-select--rtl .erp-select__search-icon {
    left: auto;
    right: 24px;
}

.erp-select--rtl .erp-select__option-checkbox {
    margin-right: 0;
    margin-left: 8px;
}

/* ===== الخيارات المتعددة ===== */
.erp-select--multiple .erp-select__display {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    min-height: 24px;
}

.erp-select--multiple .erp-select__trigger {
    min-height: 42px;
    padding-top: 6px;
    padding-bottom: 6px;
}

/* ===== الثيمات ===== */
.erp-select--primary {
    --erp-select-primary: #3b82f6;
    --erp-select-primary-dark: #2563eb;
}

.erp-select--success {
    --erp-select-primary: #10b981;
    --erp-select-primary-dark: #059669;
}

.erp-select--warning {
    --erp-select-primary: #f59e0b;
    --erp-select-primary-dark: #d97706;
}

.erp-select--danger {
    --erp-select-primary: #ef4444;
    --erp-select-primary-dark: #dc2626;
}

/* ===== انيميشن الفتح والإغلاق ===== */
@keyframes erp-select-fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes erp-select-fadeOut {
    from {
        opacity: 1;
        transform: translateY(0);
    }
    to {
        opacity: 0;
        transform: translateY(-10px);
    }
}

/* ===== تحسينات الاستجابة ===== */
@media (max-width: 768px) {
    .erp-select__dropdown {
        position: fixed;
        top: auto !important;
        bottom: 0;
        left: 0;
        right: 0;
        margin: 0;
        border-radius: var(--erp-select-radius) var(--erp-select-radius) 0 0;
        max-height: 50vh;
    }
    
    .erp-select__options {
        max-height: calc(50vh - 80px);
    }
    
    .erp-select__search {
        padding: 16px;
    }
}

/* ===== تحسينات إمكانية الوصول ===== */
@media (prefers-reduced-motion: reduce) {
    .erp-select,
    .erp-select *,
    .erp-select *::before,
    .erp-select *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    .erp-select__trigger {
        border-width: 3px;
    }
    
    .erp-select__option--focused {
        outline-width: 3px;
    }
}
