<?php
/**
 * مثال لاستخدام Tailwind CSS مع datatable_helper.php
 * Example of using Tailwind CSS with datatable_helper.php
 */

// تضمين الملفات المطلوبة
require_once '../App/Helpers/datatable_helper.php';

// بيانات تجريبية للجدول
$columns = [
    [
        'field' => 'id',
        'title' => 'الرقم',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'number',
        'width' => '80px'
    ],
    [
        'field' => 'name',
        'title' => 'الاسم',
        'type' => 'link',
        'url' => 'users/{entity_number}/view',
        'subtitle_field' => 'email',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'danger',
                'pending' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'pending' => 'في الانتظار'
            ]
        ]
    ],
    [
        'field' => 'balance',
        'title' => 'الرصيد',
        'type' => 'currency',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'created_at',
        'title' => 'تاريخ الإنشاء',
        'type' => 'datetime',
        'sortable' => true,
        'data_type' => 'date'
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'users/{entity_number}/edit',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'onclick' => 'confirmDelete({entity_number})',
                'class' => 'btn-outline-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف'
            ]
        ]
    ]
];

$data = [
    [
        'id' => 1,
        'entity_number' => 'USR001',
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'status' => 'active',
        'balance' => '1500.00',
        'created_at' => '2024-01-15 10:30:00'
    ],
    [
        'id' => 2,
        'entity_number' => 'USR002',
        'name' => 'فاطمة علي',
        'email' => '<EMAIL>',
        'status' => 'pending',
        'balance' => '750.50',
        'created_at' => '2024-01-14 14:20:00'
    ],
    [
        'id' => 3,
        'entity_number' => 'USR003',
        'name' => 'محمد حسن',
        'email' => '<EMAIL>',
        'status' => 'inactive',
        'balance' => '0.00',
        'created_at' => '2024-01-13 09:15:00'
    ]
];

$empty_state = [
    'icon' => 'fas fa-users',
    'message' => 'لا يوجد مستخدمين',
    'action' => [
        'url' => 'users/create',
        'text' => 'إضافة مستخدم جديد'
    ]
];

$module = 'users';
$entity = 'user';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مثال Tailwind CSS DataTable</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { font-family: 'Tajawal', sans-serif; }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900">

<div class="<?= get_table_tailwind_classes('container') ?> py-8">
    
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            مثال Tailwind CSS DataTable
        </h1>
        <p class="text-gray-600 dark:text-gray-400">
            مثال شامل لاستخدام مكتبة Tailwind CSS مع نظام الجداول
        </p>
    </div>

    <!-- Toolbar -->
    <div class="<?= get_table_tailwind_classes('toolbar') ?>">
        <div class="<?= get_table_tailwind_classes('toolbar_left') ?>">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white">قائمة المستخدمين</h2>
            <span class="text-sm text-gray-500 dark:text-gray-400">
                إجمالي: <?= count($data) ?> مستخدم
            </span>
        </div>
        <div class="<?= get_table_tailwind_classes('toolbar_right') ?>">
            <button class="<?= get_tailwind_classes('btn btn-outline-secondary') ?>">
                <i class="fas fa-filter mr-2"></i>
                فلاتر
            </button>
            <button class="<?= get_tailwind_classes('btn btn-primary') ?>">
                <i class="fas fa-plus mr-2"></i>
                إضافة مستخدم
            </button>
        </div>
    </div>

    <!-- DataTable with Tailwind -->
    <?php render_datatable_table_tailwind($columns, $data, $empty_state, $module, $entity); ?>

    <!-- Comparison Section -->
    <div class="mt-12 grid grid-cols-1 lg:grid-cols-2 gap-8">
        
        <!-- Bootstrap Version -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    النسخة الأصلية (Bootstrap)
                </h3>
            </div>
            <div class="p-6">
                <div class="table-responsive">
                    <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
                </div>
            </div>
        </div>

        <!-- Features Comparison -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                    مقارنة المميزات
                </h3>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700 dark:text-gray-300">تصميم عصري</span>
                        <span class="text-green-600 dark:text-green-400">✓ Tailwind</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700 dark:text-gray-300">دعم الثيم الداكن</span>
                        <span class="text-green-600 dark:text-green-400">✓ كلاهما</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700 dark:text-gray-300">سهولة التخصيص</span>
                        <span class="text-green-600 dark:text-green-400">✓ Tailwind</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-700 dark:text-gray-300">حجم أصغر</span>
                        <span class="text-blue-600 dark:text-blue-400">✓ Bootstrap</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Usage Examples -->
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                أمثلة الاستخدام
            </h3>
        </div>
        <div class="p-6">
            <div class="space-y-6">
                
                <!-- Bootstrap Example -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Bootstrap (النسخة الأصلية):</h4>
                    <pre class="bg-gray-100 dark:bg-gray-900 p-4 rounded-md text-sm overflow-x-auto"><code>&lt;?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?&gt;</code></pre>
                </div>

                <!-- Tailwind Example -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">Tailwind CSS (النسخة الجديدة):</h4>
                    <pre class="bg-gray-100 dark:bg-gray-900 p-4 rounded-md text-sm overflow-x-auto"><code>&lt;?php render_datatable_table_tailwind($columns, $data, $empty_state, $module, $entity); ?&gt;</code></pre>
                </div>

                <!-- Class Conversion Example -->
                <div>
                    <h4 class="font-medium text-gray-900 dark:text-white mb-2">تحويل الكلاسات:</h4>
                    <pre class="bg-gray-100 dark:bg-gray-900 p-4 rounded-md text-sm overflow-x-auto"><code>$tailwind_classes = get_tailwind_classes('btn btn-primary', 'custom-class');</code></pre>
                </div>

            </div>
        </div>
    </div>

</div>

<!-- Dark Mode Toggle -->
<button onclick="toggleDarkMode()" 
        class="fixed bottom-4 right-4 bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200">
    <i class="fas fa-moon dark:hidden"></i>
    <i class="fas fa-sun hidden dark:inline"></i>
</button>

<script>
function toggleDarkMode() {
    document.documentElement.classList.toggle('dark');
    localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
}

// Load saved theme
if (localStorage.getItem('darkMode') === 'true') {
    document.documentElement.classList.add('dark');
}
</script>

</body>
</html>
