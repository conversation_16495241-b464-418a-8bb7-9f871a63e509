<?php
/**
 * اختبار التصميم الأصلي النظيف للجداول
 */

// تضمين الملفات المطلوبة
require_once 'App/Helpers/datatable_helper.php';

// بيانات تجريبية
$columns = [
    [
        'field' => 'id',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '80px'
    ],
    [
        'field' => 'name',
        'title' => 'الاسم',
        'type' => 'link',
        'url' => 'users/{entity_number}/view',
        'subtitle_field' => 'email'
    ],
    [
        'field' => 'status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'danger',
                'pending' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'pending' => 'في الانتظار'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'users/{entity_number}/edit',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'onclick' => 'confirmDelete({entity_number})',
                'class' => 'btn-outline-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف'
            ]
        ]
    ]
];

$data = [
    [
        'id' => 1,
        'entity_number' => 'USR001',
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'status' => 'active'
    ],
    [
        'id' => 2,
        'entity_number' => 'USR002',
        'name' => 'فاطمة علي',
        'email' => '<EMAIL>',
        'status' => 'pending'
    ],
    [
        'id' => 3,
        'entity_number' => 'USR003',
        'name' => 'محمد حسن',
        'email' => '<EMAIL>',
        'status' => 'inactive'
    ]
];

$empty_state = [
    'icon' => 'mdi mdi-account-group-outline',
    'message' => 'لا يوجد مستخدمين',
    'action' => [
        'url' => 'users/create',
        'text' => 'إضافة مستخدم جديد'
    ]
];

$module = 'users';
$entity = 'user';

// دوال مساعدة مؤقتة
function base_url($path = '') {
    return 'http://localhost/erpapp/' . $path;
}

function htmlspecialchars_decode($string) {
    return htmlspecialchars($string);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التصميم الأصلي النظيف للجداول</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Material Design Icons -->
    <link rel="stylesheet" href="https://cdn.materialdesignicons.com/7.2.96/css/materialdesignicons.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="public/css/core/variables.css">
    <link rel="stylesheet" href="public/css/core/base.css">
    <link rel="stylesheet" href="public/css/components/tables.css">
    
    <style>
        body { 
            font-family: 'Tajawal', sans-serif; 
            background: var(--light-bg-color);
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
        }
        
        body.dark-theme {
            background: var(--dark-bg-color);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--light-text-color);
            margin-bottom: 1rem;
        }
        
        body.dark-theme .header h1 {
            color: var(--dark-text-color);
        }
        
        .header p {
            font-size: 1.125rem;
            color: var(--light-text-muted);
            margin-bottom: 2rem;
        }
        
        body.dark-theme .header p {
            color: var(--dark-text-muted);
        }
        
        .theme-toggle {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
            position: relative;
            overflow: hidden;
        }

        .theme-toggle::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .theme-toggle:hover::before {
            left: 100%;
        }

        .theme-toggle:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: var(--box-shadow-md);
        }
        
        .table-container {
            background: var(--light-card-bg);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--box-shadow);
            border: 1px solid var(--light-card-border);
        }
        
        body.dark-theme .table-container {
            background: var(--dark-card-bg);
            border-color: var(--dark-card-border);
        }
        
        .table-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--light-card-border);
        }
        
        body.dark-theme .table-header {
            border-bottom-color: var(--dark-card-border);
        }
        
        .table-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--light-text-color);
            margin: 0;
        }
        
        body.dark-theme .table-title {
            color: var(--dark-text-color);
        }
        
        .table-subtitle {
            font-size: 0.875rem;
            color: var(--light-text-muted);
            margin: 0.5rem 0 0 0;
        }
        
        body.dark-theme .table-subtitle {
            color: var(--dark-text-muted);
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>✨ تصميمك الأصلي النظيف</h1>
        <p>تصميم بسيط ونظيف بدون تعقيدات - كما تريده تماماً!</p>
        
        <button onclick="toggleTheme()" class="theme-toggle">
            <i class="fas fa-moon" id="theme-icon"></i>
            <span id="theme-text">الوضع الداكن</span>
        </button>
    </div>

    <!-- Table Container -->
    <div class="table-container">
        
        <!-- Table Header -->
        <div class="table-header">
            <h2 class="table-title">📊 قائمة المستخدمين</h2>
            <p class="table-subtitle">إجمالي: <?= count($data) ?> مستخدم - تصميم نظيف ومنظم</p>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
        </div>
        
    </div>

</div>

<script>
function toggleTheme() {
    // إضافة كلاس انتقال مؤقت لضمان السلاسة
    document.body.style.transition = 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)';

    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');

    const icon = document.getElementById('theme-icon');
    const text = document.getElementById('theme-text');

    // تأخير بسيط لضمان سلاسة التبديل
    setTimeout(() => {
        if (isDark) {
            icon.className = 'fas fa-sun';
            text.textContent = 'الوضع الفاتح';
        } else {
            icon.className = 'fas fa-moon';
            text.textContent = 'الوضع الداكن';
        }
    }, 50);

    localStorage.setItem('darkMode', isDark);

    // إزالة الانتقال المؤقت بعد انتهاء التبديل
    setTimeout(() => {
        document.body.style.transition = '';
    }, 400);
}

// Load saved theme
if (localStorage.getItem('darkMode') === 'true') {
    document.body.classList.add('dark-theme');
    document.getElementById('theme-icon').className = 'fas fa-sun';
    document.getElementById('theme-text').textContent = 'الوضع الفاتح';
}

// Simulate functions
function confirmDelete(id) {
    if (confirm('هل أنت متأكد من حذف المستخدم رقم: ' + id + '؟')) {
        alert('تم حذف المستخدم بنجاح!');
    }
}
</script>

</body>
</html>
