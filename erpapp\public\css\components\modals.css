/* ===== ELEGANT MODALS (تصميم أنيق وبسيط) ===== */

/* Modal Container */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1050;
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-4);
    overflow-x: hidden;
    overflow-y: auto;
}

.modal.show {
    display: flex;
}

/* Modal Dialog */
.modal-dialog {
    position: relative;
    width: 100%;
    max-width: 480px;
    margin: var(--spacing-6) auto;
    pointer-events: auto;
    transform: scale(0.9) translateY(-20px);
    transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    opacity: 0;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
    opacity: 1;
}

/* Modal Content */
.modal-content {
    position: relative;
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    overflow: hidden;
    max-height: 85vh;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

body.dark-theme .modal-content {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 8px 16px rgba(0, 0, 0, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Modal Header */
.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 28px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: transparent;
    position: relative;
}

body.dark-theme .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.08);
}

.modal-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--light-text-color);
    display: flex;
    align-items: center;
    gap: 10px;
    letter-spacing: -0.01em;
}

body.dark-theme .modal-title {
    color: var(--dark-text-color);
}

.modal-title i {
    font-size: 16px;
    opacity: 0.7;
    color: var(--primary-color);
}

/* Modal Close Button */
.modal-close {
    background: rgba(0, 0, 0, 0.04);
    border: none;
    font-size: 14px;
    color: var(--light-text-muted);
    cursor: pointer;
    padding: 0;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    position: relative;
}

body.dark-theme .modal-close {
    background: rgba(255, 255, 255, 0.06);
    color: var(--dark-text-muted);
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.08);
    color: var(--light-text-color);
    transform: scale(1.05);
}

body.dark-theme .modal-close:hover {
    background: rgba(255, 255, 255, 0.12);
    color: var(--dark-text-color);
}

.modal-close:active {
    transform: scale(0.95);
}

.modal-close:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Modal Body */
.modal-body {
    padding: 24px 28px;
    flex: 1;
    overflow-y: auto;
    color: var(--light-text-color);
    line-height: 1.6;
    font-size: 14px;
}

body.dark-theme .modal-body {
    color: var(--dark-text-color);
}

/* Modal Footer */
.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 28px 24px;
    border-top: 1px solid rgba(0, 0, 0, 0.06);
    background: transparent;
}

body.dark-theme .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.08);
}

/* Modal Sizes */
.modal-sm .modal-dialog {
    max-width: 300px;
}

.modal-lg .modal-dialog {
    max-width: 800px;
}

.modal-xl .modal-dialog {
    max-width: 1140px;
}

.modal-fullscreen .modal-dialog {
    width: 100%;
    max-width: none;
    height: 100%;
    margin: 0;
}

.modal-fullscreen .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
}

/* Filter Modal Specific */
.filter-modal .modal-dialog {
    max-width: 520px;
}

.filter-modal .modal-body {
    padding: 24px 28px;
}

.filter-modal .form-group {
    margin-bottom: var(--spacing-4);
}

.filter-modal .form-row {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
}

.filter-modal .form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Modal Buttons */
.modal .btn {
    padding: 10px 20px;
    font-size: 13px;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
    min-width: 80px;
    text-align: center;
}

.modal .btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.modal .btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.modal .btn-secondary {
    background: rgba(0, 0, 0, 0.04);
    color: var(--light-text-color);
    border-color: rgba(0, 0, 0, 0.08);
}

body.dark-theme .modal .btn-secondary {
    background: rgba(255, 255, 255, 0.06);
    color: var(--dark-text-color);
    border-color: rgba(255, 255, 255, 0.12);
}

.modal .btn-secondary:hover {
    background: rgba(0, 0, 0, 0.08);
    border-color: rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

body.dark-theme .modal .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.18);
}

.modal .btn-outline-secondary {
    background: transparent;
    color: var(--light-text-muted);
    border-color: rgba(0, 0, 0, 0.12);
}

body.dark-theme .modal .btn-outline-secondary {
    color: var(--dark-text-muted);
    border-color: rgba(255, 255, 255, 0.18);
}

.modal .btn-outline-secondary:hover {
    background: rgba(0, 0, 0, 0.04);
    color: var(--light-text-color);
    border-color: rgba(0, 0, 0, 0.18);
}

body.dark-theme .modal .btn-outline-secondary:hover {
    background: rgba(255, 255, 255, 0.06);
    color: var(--dark-text-color);
    border-color: rgba(255, 255, 255, 0.24);
}

/* Confirmation Modal */
.confirmation-modal .modal-dialog {
    max-width: 400px;
}

.confirmation-modal .modal-body {
    text-align: center;
    padding: var(--spacing-6);
}

.confirmation-modal .modal-icon {
    font-size: 48px;
    margin-bottom: var(--spacing-4);
}

.confirmation-modal .modal-icon.warning {
    color: var(--warning-color);
}

.confirmation-modal .modal-icon.danger {
    color: var(--danger-color);
}

.confirmation-modal .modal-icon.success {
    color: var(--success-color);
}

.confirmation-modal .modal-icon.info {
    color: var(--info-color);
}

/* Loading Modal */
.loading-modal .modal-dialog {
    max-width: 300px;
}

.loading-modal .modal-body {
    text-align: center;
    padding: var(--spacing-6);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-300);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Backdrop */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1040;
    opacity: 0;
    transition: opacity 0.25s ease;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    cursor: pointer;
}

.modal-backdrop.show {
    opacity: 1;
}

/* Responsive Modals */
@media (max-width: 768px) {
    .modal {
        padding: var(--spacing-2);
    }
    
    .modal-dialog {
        margin: var(--spacing-2) auto;
        max-width: calc(100% - var(--spacing-4));
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-4);
    }
    
    .modal-lg .modal-dialog,
    .modal-xl .modal-dialog {
        max-width: calc(100% - var(--spacing-4));
    }
    
    .filter-modal .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .filter-modal .form-row .form-group {
        margin-bottom: var(--spacing-4);
    }
}

/* Animation Classes */
.modal-fade-in {
    animation: modalFadeIn var(--transition-normal) ease;
}

.modal-fade-out {
    animation: modalFadeOut var(--transition-normal) ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
}

/* Prevent Body Scroll */
body.modal-open {
    overflow: hidden;
    padding-right: 15px; /* Compensate for scrollbar */
}

/* RTL Support */
[dir="rtl"] .modal-footer {
    justify-content: flex-start;
}

[dir="rtl"] body.modal-open {
    padding-right: 0;
    padding-left: 15px;
}
