<?php
/**
 * اختبار مكتبات Select المتقدمة
 */

// تضمين الملفات المطلوبة
require_once 'App/Helpers/datatable_helper.php';

// بيانات تجريبية للفلاتر مع Select المتقدم
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'fas fa-search',
        'col_size' => 6,
        'help' => 'البحث في الاسم العربي، الإنجليزي، أو اسم الشركة'
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 3,
        'filter_type' => 'status',
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ],
    [
        'name' => 'group_id',
        'type' => 'select',
        'label' => 'مجموعة الموردين',
        'placeholder' => 'جميع المجموعات',
        'icon' => 'fas fa-folder',
        'col_size' => 3,
        'filter_type' => 'group',
        'ajax_url' => 'api/supplier-groups',
        'options' => [
            '1' => 'موردين محليين',
            '2' => 'موردين دوليين',
            '3' => 'موردين مميزين',
            '4' => 'موردين استراتيجيين',
            '5' => 'موردين مؤقتين'
        ]
    ],
    [
        'name' => 'country',
        'type' => 'select',
        'label' => 'البلد',
        'placeholder' => 'جميع البلدان',
        'icon' => 'fas fa-globe',
        'col_size' => 6,
        'filter_type' => 'country',
        'options' => [
            'SA' => 'المملكة العربية السعودية',
            'AE' => 'الإمارات العربية المتحدة',
            'KW' => 'الكويت',
            'QA' => 'قطر',
            'BH' => 'البحرين',
            'OM' => 'عمان',
            'JO' => 'الأردن',
            'LB' => 'لبنان',
            'EG' => 'مصر',
            'MA' => 'المغرب'
        ]
    ],
    [
        'name' => 'created_from',
        'type' => 'date',
        'label' => 'تاريخ الإنشاء من',
        'placeholder' => 'اختر التاريخ',
        'icon' => 'fas fa-calendar',
        'col_size' => 6
    ]
];

$filters = [
    'per_page' => 20,
    'search' => '',
    'status' => '',
    'group_id' => '',
    'country' => '',
    'created_from' => ''
];

$stats = [];
$pagination = ['total_items' => 150];

$module = 'purchases';
$entity = 'suppliers';

// دوال مساعدة مؤقتة
function base_url($path = '') {
    return 'http://localhost/erpapp/' . $path;
}

function has_active_filters($filters) {
    return !empty($filters['search']) || !empty($filters['status']) || !empty($filters['group_id']);
}

if (!function_exists('htmlspecialchars_decode')) {
    function htmlspecialchars_decode($string) {
        return htmlspecialchars($string);
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مكتبات Select المتقدمة</title>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="public/css/core/variables.css">
    <link rel="stylesheet" href="public/css/core/base.css">
    <link rel="stylesheet" href="public/css/components/buttons.css">
    <link rel="stylesheet" href="public/css/components/forms.css">
    <link rel="stylesheet" href="public/css/components/modals.css">
    <link rel="stylesheet" href="public/css/components/filters.css">
    
    <style>
        body { 
            font-family: 'Tajawal', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 2rem;
        }
        
        body.dark-theme {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 3rem;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        body.dark-theme .header {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--light-text-color);
            margin-bottom: 1rem;
        }
        
        body.dark-theme .header h1 {
            color: var(--dark-text-color);
        }
        
        .test-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 2rem;
        }
        
        .test-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            color: var(--primary-color);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            backdrop-filter: blur(10px);
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        body.dark-theme .test-btn {
            background: rgba(31, 41, 55, 0.9);
            color: var(--primary-color);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .test-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            color: var(--primary-color);
            text-decoration: none;
        }
        
        body.dark-theme .test-btn:hover {
            background: rgba(31, 41, 55, 1);
            color: var(--primary-color);
        }
        
        .theme-toggle {
            padding: 0.75rem 1.5rem;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-normal);
        }
        
        .theme-toggle:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }
        
        .info-box {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
        }
        
        body.dark-theme .info-box {
            background: rgba(31, 41, 55, 0.9);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .info-box h3 {
            color: var(--primary-color);
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .info-box p {
            color: var(--light-text-color);
            margin-bottom: 0;
            line-height: 1.6;
        }
        
        body.dark-theme .info-box p {
            color: var(--dark-text-color);
        }
        
        .library-selector {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            backdrop-filter: blur(20px);
        }
        
        body.dark-theme .library-selector {
            background: rgba(31, 41, 55, 0.9);
            border-color: rgba(255, 255, 255, 0.1);
        }
        
        .library-options {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .library-option {
            padding: 8px 16px;
            background: rgba(var(--primary-rgb), 0.1);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-weight: 600;
        }
        
        .library-option.active {
            background: var(--primary-color);
            color: white;
        }
        
        .library-option:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>

<div class="container">
    
    <!-- Header -->
    <div class="header">
        <h1>🚀 اختبار مكتبات Select المتقدمة</h1>
        <p>نظام موحد لدعم Select2, Choices.js, Tom Select مع تصميم أنيق</p>
        
        <button onclick="toggleTheme()" class="theme-toggle">
            <i class="fas fa-moon" id="theme-icon"></i>
            <span id="theme-text">الوضع الداكن</span>
        </button>
    </div>

    <!-- Library Selector -->
    <div class="library-selector">
        <h3 style="text-align: center; margin-bottom: 1rem; color: var(--primary-color);">اختر مكتبة Select:</h3>
        <div class="library-options">
            <div class="library-option active" data-library="select2">
                <i class="fas fa-list"></i> Select2
            </div>
            <div class="library-option" data-library="choices">
                <i class="fas fa-check-square"></i> Choices.js
            </div>
            <div class="library-option" data-library="tomselect">
                <i class="fas fa-layer-group"></i> Tom Select
            </div>
        </div>
    </div>

    <!-- Info Box -->
    <div class="info-box">
        <h3>✨ مميزات النظام الموحد:</h3>
        <p>• دعم 3 مكتبات Select متقدمة<br>
        • API موحد لجميع المكتبات<br>
        • تحميل تلقائي للمكتبات<br>
        • تصميم مخصص لكل مكتبة<br>
        • دعم AJAX والبحث المتقدم<br>
        • دعم RTL كامل<br>
        • تكامل مع نظام الفلاتر</p>
    </div>

    <!-- Test Buttons -->
    <div class="test-buttons">
        <button class="test-btn" data-toggle="modal" data-target="#filtersModal">
            <i class="fas fa-filter"></i>
            فتح نافذة الفلاتر المتقدمة
        </button>
    </div>

</div>

<!-- Filter Modal -->
<?php render_filters_modal($module, $entity, $filters_config, $filters, $stats, $pagination); ?>

<!-- Bootstrap JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<!-- Custom JS -->
<script src="public/js/core/app.js"></script>
<script src="public/js/components/modals.js"></script>
<script src="public/js/components/select.js"></script>
<script src="public/js/components/filters.js"></script>

<script>
// متغيرات التطبيق
window.APP_CONFIG = {
    APP_URL: 'http://localhost/erpapp/',
    LANG: 'ar',
    IS_RTL: true
};

// تبديل الثيم
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    const isDark = document.body.classList.contains('dark-theme');
    
    const icon = document.getElementById('theme-icon');
    const text = document.getElementById('theme-text');
    
    if (isDark) {
        icon.className = 'fas fa-sun';
        text.textContent = 'الوضع الفاتح';
    } else {
        icon.className = 'fas fa-moon';
        text.textContent = 'الوضع الداكن';
    }
    
    localStorage.setItem('darkMode', isDark);
}

// تبديل مكتبة Select
function switchSelectLibrary(library) {
    // تحديث الأزرار
    document.querySelectorAll('.library-option').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-library="${library}"]`).classList.add('active');
    
    // تحديث إعدادات Select
    if (window.filtersManager) {
        window.filtersManager.config.selectLibrary = library;
        
        // إعادة تهيئة Select
        window.filtersManager.destroy();
        setTimeout(() => {
            window.filtersManager = new FilterManager('#filtersForm', {
                selectLibrary: library,
                autoSubmit: false
            });
        }, 100);
    }
    
    console.log(`🔄 تم تبديل مكتبة Select إلى: ${library}`);
}

// تهيئة الأحداث
document.addEventListener('DOMContentLoaded', function() {
    // تحميل الثيم المحفوظ
    if (localStorage.getItem('darkMode') === 'true') {
        document.body.classList.add('dark-theme');
        document.getElementById('theme-icon').className = 'fas fa-sun';
        document.getElementById('theme-text').textContent = 'الوضع الفاتح';
    }
    
    // أحداث تبديل المكتبة
    document.querySelectorAll('.library-option').forEach(btn => {
        btn.addEventListener('click', function() {
            const library = this.dataset.library;
            switchSelectLibrary(library);
        });
    });
    
    console.log('🚀 تم تهيئة صفحة اختبار Select المتقدم');
});
</script>

</body>
</html>
