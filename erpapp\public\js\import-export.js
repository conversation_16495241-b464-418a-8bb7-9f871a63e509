/**
 * نظام الاستيراد والتصدير
 * Import/Export System JavaScript
 */

// متغيرات عامة
let currentStep = 1;
let importData = null;
let importHeaders = null;
let tableColumns = [];

// تهيئة النظام
document.addEventListener('DOMContentLoaded', function() {
    initializeImportExport();
});

/**
 * تهيئة نظام الاستيراد والتصدير
 */
function initializeImportExport() {
    // تهيئة قائمة التصدير المنسدلة
    initializeExportDropdown();
    
    // تهيئة رفع الملفات
    initializeFileUpload();
    
    // تهيئة نافذة الاستيراد
    initializeImportModal();
}

/**
 * تهيئة قائمة التصدير المنسدلة
 */
function initializeExportDropdown() {
    const exportBtn = document.querySelector('.datatable-export-btn');
    const dropdown = exportBtn?.closest('.dropdown');
    
    if (exportBtn && dropdown) {
        exportBtn.addEventListener('click', function(e) {
            e.preventDefault();
            dropdown.classList.toggle('show');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('show');
            }
        });
    }
}

/**
 * تهيئة رفع الملفات
 */
function initializeFileUpload() {
    const fileInput = document.getElementById('importFile');
    const uploadBox = document.querySelector('.file-upload-box');
    
    if (!fileInput || !uploadBox) return;
    
    // النقر على منطقة الرفع
    uploadBox.addEventListener('click', function() {
        fileInput.click();
    });
    
    // سحب وإفلات الملفات
    uploadBox.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadBox.classList.add('dragover');
    });
    
    uploadBox.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadBox.classList.remove('dragover');
    });
    
    uploadBox.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadBox.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });
    
    // تغيير الملف
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });
}

/**
 * تهيئة نافذة الاستيراد
 */
function initializeImportModal() {
    const modal = document.getElementById('importModal');
    if (!modal) return;
    
    // إعادة تعيين النافذة عند الإغلاق
    modal.addEventListener('hidden.bs.modal', function() {
        resetImportModal();
    });
}

/**
 * معالجة اختيار الملف
 */
function handleFileSelection(file) {
    // التحقق من نوع الملف
    const allowedTypes = [
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'text/csv'
    ];
    
    if (!allowedTypes.includes(file.type)) {
        alert('نوع الملف غير مدعوم. يرجى اختيار ملف Excel أو CSV.');
        return;
    }
    
    // التحقق من حجم الملف (5MB كحد أقصى)
    if (file.size > 5 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 5 ميجابايت.');
        return;
    }
    
    // عرض معلومات الملف
    displayFileInfo(file);
    
    // قراءة الملف
    readImportFile(file);
}

/**
 * عرض معلومات الملف
 */
function displayFileInfo(file) {
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    
    if (fileInfo && fileName && fileSize) {
        fileName.textContent = file.name;
        fileSize.textContent = `(${formatFileSize(file.size)})`;
        fileInfo.style.display = 'block';
    }
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * قراءة ملف الاستيراد
 */
function readImportFile(file) {
    const formData = new FormData();
    formData.append('import_file', file);
    formData.append('action', 'preview');
    
    // عرض مؤشر التحميل
    showLoadingIndicator();
    
    // إرسال الملف للخادم للمعاينة
    fetch(window.location.href.replace(/\/[^\/]*$/, '/import-preview'), {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingIndicator();
        
        if (data.success) {
            importData = data.data;
            importHeaders = data.headers;
            displayPreview(data);
            enableNextStep();
        } else {
            alert('خطأ في قراءة الملف: ' + data.error);
        }
    })
    .catch(error => {
        hideLoadingIndicator();
        console.error('Error:', error);
        alert('حدث خطأ في قراءة الملف');
    });
}

/**
 * عرض معاينة البيانات
 */
function displayPreview(data) {
    // تحديث معلومات المعاينة
    document.getElementById('totalRows').textContent = data.total_rows;
    document.getElementById('totalColumns').textContent = data.headers.length;
    document.getElementById('previewRows').textContent = Math.min(10, data.total_rows);
    
    // إنشاء جدول المعاينة
    const table = document.getElementById('previewTable');
    const thead = table.querySelector('thead');
    const tbody = table.querySelector('tbody');
    
    // مسح المحتوى السابق
    thead.innerHTML = '';
    tbody.innerHTML = '';
    
    // إضافة رؤوس الأعمدة
    const headerRow = document.createElement('tr');
    data.headers.forEach(header => {
        const th = document.createElement('th');
        th.textContent = header;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    
    // إضافة صفوف البيانات (أول 10 صفوف)
    const previewData = data.data.slice(0, 10);
    previewData.forEach(row => {
        const tr = document.createElement('tr');
        data.headers.forEach(header => {
            const td = document.createElement('td');
            td.textContent = row[header] || '';
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
}

/**
 * الانتقال للخطوة التالية
 */
function nextImportStep() {
    if (currentStep === 1) {
        // من الخطوة 1 إلى 2 (معاينة البيانات)
        if (!importData) {
            alert('يرجى اختيار ملف أولاً');
            return;
        }
        
        showStep(2);
        currentStep = 2;
        
    } else if (currentStep === 2) {
        // من الخطوة 2 إلى 3 (تطابق الأعمدة)
        loadColumnMapping();
        showStep(3);
        currentStep = 3;
        
        // تغيير زر التالي إلى زر الاستيراد
        document.getElementById('nextStep').style.display = 'none';
        document.getElementById('importSubmit').style.display = 'inline-block';
    }
}

/**
 * عرض خطوة معينة
 */
function showStep(stepNumber) {
    // إخفاء جميع الخطوات
    document.querySelectorAll('.import-step').forEach(step => {
        step.style.display = 'none';
    });
    
    // عرض الخطوة المطلوبة
    document.getElementById('step' + stepNumber).style.display = 'block';
}

/**
 * تحميل تطابق الأعمدة
 */
function loadColumnMapping() {
    // الحصول على أعمدة الجدول من الخادم
    fetch(window.location.href.replace(/\/[^\/]*$/, '/get-table-columns'))
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            tableColumns = data.columns;
            displayColumnMapping();
        } else {
            alert('خطأ في تحميل أعمدة الجدول');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('حدث خطأ في تحميل أعمدة الجدول');
    });
}

/**
 * عرض تطابق الأعمدة
 */
function displayColumnMapping() {
    const container = document.getElementById('columnMapping');
    container.innerHTML = '';
    
    importHeaders.forEach(header => {
        const row = document.createElement('div');
        row.className = 'column-mapping-row';
        
        // عمود المصدر
        const sourceCol = document.createElement('div');
        sourceCol.className = 'source-column';
        sourceCol.textContent = header;
        
        // سهم التطابق
        const arrow = document.createElement('div');
        arrow.className = 'mapping-arrow';
        arrow.innerHTML = '<i class="fas fa-arrow-left"></i>';
        
        // قائمة الأعمدة المستهدفة
        const targetSelect = document.createElement('select');
        targetSelect.className = 'target-select';
        targetSelect.name = 'column_mapping[' + header + ']';
        
        // خيار فارغ
        const emptyOption = document.createElement('option');
        emptyOption.value = '';
        emptyOption.textContent = 'تجاهل هذا العمود';
        targetSelect.appendChild(emptyOption);
        
        // أعمدة الجدول
        tableColumns.forEach(column => {
            const option = document.createElement('option');
            option.value = column.field;
            option.textContent = column.title;
            
            // اقتراح تلقائي بناءً على التشابه
            if (header.toLowerCase().includes(column.title.toLowerCase()) || 
                column.title.toLowerCase().includes(header.toLowerCase())) {
                option.selected = true;
            }
            
            targetSelect.appendChild(option);
        });
        
        row.appendChild(sourceCol);
        row.appendChild(arrow);
        row.appendChild(targetSelect);
        container.appendChild(row);
    });
}

/**
 * تمكين زر الخطوة التالية
 */
function enableNextStep() {
    const nextBtn = document.getElementById('nextStep');
    if (nextBtn) {
        nextBtn.disabled = false;
    }
}

/**
 * إعادة تعيين نافذة الاستيراد
 */
function resetImportModal() {
    currentStep = 1;
    importData = null;
    importHeaders = null;
    
    // إعادة تعيين النموذج
    document.getElementById('importForm').reset();
    
    // إخفاء معلومات الملف
    document.getElementById('fileInfo').style.display = 'none';
    
    // عرض الخطوة الأولى فقط
    showStep(1);
    
    // إعادة تعيين الأزرار
    document.getElementById('nextStep').style.display = 'inline-block';
    document.getElementById('nextStep').disabled = true;
    document.getElementById('importSubmit').style.display = 'none';
}

/**
 * عرض مؤشر التحميل
 */
function showLoadingIndicator() {
    // يمكن تحسين هذا لاحقاً
    console.log('Loading...');
}

/**
 * إخفاء مؤشر التحميل
 */
function hideLoadingIndicator() {
    // يمكن تحسين هذا لاحقاً
    console.log('Loading complete');
}
