/* ===== TABLES (بديل Bootstrap) ===== */

.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    background: var(--light-card-bg);
    box-shadow: var(--box-shadow-sm);
}

body.dark-theme .table-responsive {
    background: var(--dark-card-bg);
}

.table {
    width: 100%;
    margin: 0;
    background: var(--light-card-bg);
    border-collapse: collapse;
    font-size: 14px;
}

body.dark-theme .table {
    background: var(--dark-card-bg);
    color: var(--dark-text-color);
}

.table th {
    background: var(--gray-100);
    padding: var(--spacing-4) var(--spacing-5);
    text-align: start;
    font-weight: 600;
    color: var(--light-text-muted);
    border-bottom: 1px solid var(--light-card-border);
    white-space: nowrap;
    position: relative;
}

body.dark-theme .table th {
    background: var(--gray-800);
    color: var(--dark-text-muted);
    border-bottom-color: var(--dark-card-border);
}

.table td {
    padding: var(--spacing-4) var(--spacing-5);
    border-bottom: 1px solid var(--gray-200);
    color: var(--light-text-color);
    vertical-align: middle;
}

body.dark-theme .table td {
    border-bottom-color: var(--gray-800);
    color: var(--dark-text-color);
}

/* ===== TABLE HOVER ===== */
.table-hover tbody tr {
    transition: background-color var(--transition-fast) ease;
}

.table-hover tbody tr:hover {
    background: var(--light-hover-bg);
}

body.dark-theme .table-hover tbody tr:hover {
    background: var(--dark-hover-bg);
}

/* ===== TABLE STRIPED ===== */
.table-striped tbody tr:nth-child(odd) {
    background: rgba(0, 0, 0, 0.02);
}

body.dark-theme .table-striped tbody tr:nth-child(odd) {
    background: rgba(255, 255, 255, 0.02);
}

/* ===== TABLE BORDERED ===== */
.table-bordered {
    border: 1px solid var(--light-card-border);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--light-card-border);
}

body.dark-theme .table-bordered {
    border-color: var(--dark-card-border);
}

body.dark-theme .table-bordered th,
body.dark-theme .table-bordered td {
    border-color: var(--dark-card-border);
}

/* ===== TABLE SIZES ===== */
.table-sm th,
.table-sm td {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 12px;
}

.table-lg th,
.table-lg td {
    padding: var(--spacing-5) var(--spacing-6);
    font-size: 16px;
}

/* ===== SORTABLE COLUMNS ===== */
.table th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    padding-right: calc(var(--spacing-5) + 24px);
    transition: background-color var(--transition-fast) ease;
}

.table th[data-sortable="true"]:hover {
    background: var(--gray-200);
    color: var(--light-text-color);
}

body.dark-theme .table th[data-sortable="true"]:hover {
    background: var(--gray-700);
    color: var(--dark-text-color);
}

/* Sort Icons */
.sort-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: opacity var(--transition-normal) ease, visibility var(--transition-normal) ease;
}

.sort-icon i {
    font-size: 12px;
    color: var(--light-text-muted);
    transition: color var(--transition-fast) ease;
}

th[data-sortable="true"]:hover .sort-icon {
    opacity: 1;
    visibility: visible;
}

th[data-sortable="true"]:hover .sort-icon i {
    color: var(--light-text-color);
}

th.sorted-asc .sort-icon,
th.sorted-desc .sort-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

th.sorted-asc .sort-icon i,
th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* Dark Theme Sort Icons */
body.dark-theme .sort-icon i {
    color: var(--dark-text-muted);
}

body.dark-theme th[data-sortable="true"]:hover .sort-icon i {
    color: var(--dark-text-color);
}

body.dark-theme th.sorted-asc .sort-icon i,
body.dark-theme th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* ===== TABLE ACTIONS ===== */
.table-actions {
    display: flex;
    gap: var(--spacing-2);
    align-items: center;
}

.table-actions .btn {
    padding: var(--spacing-1) var(--spacing-2);
    font-size: 12px;
}

/* ===== TABLE STATUS BADGES ===== */
.table .badge {
    font-size: 11px;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
    font-weight: 500;
}

.badge {
    display: inline-block;
    padding: var(--spacing-1) var(--spacing-2);
    font-size: 12px;
    font-weight: 500;
    border-radius: var(--border-radius-sm);
    text-transform: none;
}

.badge-success {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-dark);
}

.badge-warning {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-dark);
}

.badge-danger {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-dark);
}

.badge-info {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-dark);
}

.badge-secondary {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* Dark Theme Badges */
body.dark-theme .badge-success {
    background: var(--success-color);
    color: var(--light-color);
}

body.dark-theme .badge-warning {
    background: var(--warning-color);
    color: var(--dark-color);
}

body.dark-theme .badge-danger {
    background: var(--danger-color);
    color: var(--light-color);
}

body.dark-theme .badge-info {
    background: var(--info-color);
    color: var(--light-color);
}

body.dark-theme .badge-secondary {
    background: var(--gray-600);
    color: var(--light-color);
}

/* ===== TABLE LINKS ===== */
.table a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color var(--transition-fast) ease;
}

.table a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

body.dark-theme .table a {
    color: var(--primary-light);
}

body.dark-theme .table a:hover {
    color: var(--primary-color);
}

/* ===== TABLE PAGINATION ===== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-1);
    margin: var(--spacing-4) 0 0 0;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 14px;
    color: var(--light-text-muted);
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    min-width: 40px;
    height: 40px;
}

.page-link:hover {
    color: var(--light-text-color);
    background: var(--light-hover-bg);
    text-decoration: none;
}

.page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
    font-weight: 500;
}

.page-item.disabled .page-link {
    color: var(--gray-400);
    background: var(--light-card-bg);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Dark Theme Pagination */
body.dark-theme .page-link {
    color: var(--dark-text-muted);
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

body.dark-theme .page-link:hover {
    color: var(--dark-text-color);
    background: var(--dark-hover-bg);
}

body.dark-theme .page-item.active .page-link {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

body.dark-theme .page-item.disabled .page-link {
    color: var(--gray-600);
    background: var(--dark-card-bg);
}

/* ===== TABLE INFO ===== */
.datatable-info {
    color: var(--light-text-muted);
    font-size: 13px;
    text-align: center;
}

.datatable-info strong {
    color: var(--light-text-color);
    font-weight: 500;
}

body.dark-theme .datatable-info {
    color: var(--dark-text-muted);
}

body.dark-theme .datatable-info strong {
    color: var(--dark-text-color);
}

/* ===== CLEAN TABLE DESIGN (من purchases.css) ===== */
.table-responsive {
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    background: #ffffff;
}

.table {
    margin: 0;
    background: #ffffff;
    border: none;
    font-size: 14px;
}

.table th {
    background: #f8f9fa;
    border: none;
    border-bottom: 1px solid #e9ecef;
    font-weight: 500;
    color: #6c757d;
    font-size: 13px;
    text-transform: none;
    letter-spacing: 0;
    padding: 16px 20px;
    position: relative;
    white-space: nowrap;
}

.table th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
}

.table th[data-sortable="true"]:hover {
    background: #f1f3f4;
    color: #495057;
}

.table td {
    vertical-align: middle;
    padding: 16px 20px;
    border: none;
    border-bottom: 1px solid #f1f3f4;
    color: #495057;
    font-size: 14px;
    white-space: nowrap;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background: #f8f9fa;
}

/* ===== ENHANCED SORT ICONS (من purchases.css) ===== */
.sort-icon {
    margin-left: 8px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: inline-block;
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
}

.sort-icon i {
    font-size: 12px;
    color: #6c757d;
    transition: color 0.2s ease;
}

/* إظهار الأيقونة عند التمرير على العمود */
th[data-sortable="true"]:hover .sort-icon {
    opacity: 1;
    visibility: visible;
}

/* تغيير لون الأيقونة عند التمرير */
th[data-sortable="true"]:hover .sort-icon i {
    color: #495057;
}

/* الأيقونة النشطة (عند الفرز) - تبقى ظاهرة دائماً */
th.sorted-asc .sort-icon,
th.sorted-desc .sort-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

/* لون الأيقونة النشطة */
th.sorted-asc .sort-icon i,
th.sorted-desc .sort-icon i {
    color: #007bff !important;
}

/* تحسين موضع العمود القابل للفرز */
th[data-sortable="true"] {
    position: relative;
    padding-right: 40px;
}

/* إخفاء النصوص الافتراضية */
.sort-icon .text-muted {
    color: transparent !important;
}

.sort-icon .text-primary {
    color: #007bff !important;
}

/* تأثير خفيف عند التمرير على العمود */
th[data-sortable="true"]:hover {
    background: #f1f3f4;
    transition: background-color 0.2s ease;
}

/* ===== ENHANCED BADGES (من purchases.css) ===== */
.badge {
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: none;
    letter-spacing: 0;
    border: none;
}

.badge.bg-success {
    background: #d4edda !important;
    color: #155724 !important;
}

.badge.bg-warning {
    background: #fff3cd !important;
    color: #856404 !important;
}

.badge.bg-danger {
    background: #f8d7da !important;
    color: #721c24 !important;
}

.badge.bg-info {
    background: #d1ecf1 !important;
    color: #0c5460 !important;
}

.badge.bg-secondary {
    background: #e2e3e5 !important;
    color: #383d41 !important;
}

/* حالات خاصة للجدول */
.table .badge {
    font-size: 11px;
    padding: 3px 6px;
}

/* ===== ENHANCED TABLE LINKS (من purchases.css) ===== */
.table a {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.table a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* ===== TABLE TEXT ENHANCEMENTS (من purchases.css) ===== */
/* تحسين الأرقام */
.table .text-end {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

/* تحسين التواريخ */
.table .text-muted {
    color: #6c757d !important;
    font-size: 13px;
}

.text-sm-end {
    float: left !important;
}

/* ===== RESPONSIVE TABLE ===== */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: var(--spacing-2) var(--spacing-3);
    }

    .table th[data-sortable="true"] {
        padding-right: calc(var(--spacing-3) + 20px);
    }

    .sort-icon {
        right: 8px;
    }
}

/* ===== ENHANCED PAGINATION (من purchases.css) ===== */
.pagination {
    margin: 0;
    gap: 4px;
    justify-content: center;
}

.page-link {
    color: #6c757d;
    border: 1px solid #dee2e6;
    padding: 8px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    background: #ffffff;
}

.page-link:hover {
    color: #495057;
    background: #f8f9fa;
    border-color: #dee2e6;
}

.page-item.active .page-link {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
    font-weight: 500;
}

.page-item.disabled .page-link {
    color: #adb5bd;
    background: #ffffff;
    border-color: #dee2e6;
    cursor: not-allowed;
}

/* تحسين أزرار التنقل */
.page-link[aria-label="Previous"],
.page-link[aria-label="Next"] {
    padding: 8px 10px;
}

/* تحسين النص السفلي */
.datatable-info {
    color: #6c757d;
    font-size: 13px;
    margin-top: 16px;
    text-align: center;
}

.datatable-info strong {
    color: #495057;
    font-weight: 500;
}

/* ===== TABLE LOADING STATES (من purchases.css) ===== */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== RTL SUPPORT FOR TABLES (من purchases.css) ===== */
[dir="rtl"] .sort-icon {
    margin-left: 0;
    margin-right: var(--spacing-2);
}
