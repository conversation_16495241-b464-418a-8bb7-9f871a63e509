/* ===== ENHANCED DATATABLE STYLES ===== */

/* ===== DATATABLE CONTAINER ===== */
.datatable-container {
    background-color: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-6);
    overflow: hidden;
}

body.dark-theme .datatable-container {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

.datatable-content {
    padding: var(--spacing-6);
}

/* ===== TABLE RESPONSIVE CONTAINER ===== */
.table-responsive {
    overflow-x: auto;
    border-radius: var(--border-radius);
    background: transparent;
    box-shadow: none;
    margin-top: var(--spacing-4);
}

.datatable-content .table-responsive {
    margin-top: 0;
}

/* ===== BASE TABLE ===== */
.table {
    width: 100%;
    margin: 0;
    background: var(--light-card-bg);
    border-collapse: collapse;
    border: none;
    font-size: 14px;
}

body.dark-theme .table {
    background: var(--dark-card-bg);
    color: var(--dark-text-color);
}

/* ===== TABLE HEADER ===== */
.table th {
    background: var(--gray-100);
    padding: 16px 20px;
    text-align: start;
    font-weight: 500;
    color: var(--light-text-muted);
    font-size: 13px;
    text-transform: none;
    letter-spacing: 0;
    border: none;
    border-bottom: 1px solid var(--light-card-border);
    white-space: nowrap;
    position: relative;
}

body.dark-theme .table th {
    background: var(--gray-800);
    color: var(--dark-text-muted);
    border-bottom-color: var(--dark-card-border);
}

/* ===== TABLE BODY ===== */
.table td {
    padding: 16px 20px;
    border: none;
    border-bottom: 1px solid var(--gray-200);
    color: var(--light-text-color);
    font-size: 14px;
    white-space: nowrap;
    vertical-align: middle;
}

body.dark-theme .table td {
    border-bottom-color: var(--gray-800);
    color: var(--dark-text-color);
}

/* ===== TABLE INTERACTIONS ===== */
.table tbody tr {
    transition: background-color 0.2s ease;
}

.table-hover tbody tr:hover {
    background: var(--light-hover-bg);
}

body.dark-theme .table-hover tbody tr:hover {
    background: var(--dark-hover-bg);
}

/* ===== TABLE VARIANTS ===== */
.table-striped tbody tr:nth-child(odd) {
    background: var(--gray-100);
    opacity: 0.3;
}

body.dark-theme .table-striped tbody tr:nth-child(odd) {
    background: var(--gray-700);
    opacity: 0.3;
}

.table-bordered {
    border: 1px solid var(--light-card-border);
}

.table-bordered th,
.table-bordered td {
    border: 1px solid var(--light-card-border);
}

body.dark-theme .table-bordered {
    border-color: var(--dark-card-border);
}

body.dark-theme .table-bordered th,
body.dark-theme .table-bordered td {
    border-color: var(--dark-card-border);
}

/* ===== TABLE SIZES ===== */
.table-sm th,
.table-sm td {
    padding: 8px 12px;
    font-size: 12px;
}

.table-lg th,
.table-lg td {
    padding: 20px 24px;
    font-size: 16px;
}

/* ===== SORTABLE COLUMNS ===== */
.table th[data-sortable="true"] {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 40px;
    transition: background-color 0.2s ease;
}

.table th[data-sortable="true"]:hover {
    background: var(--gray-200);
    color: var(--light-text-color);
}

body.dark-theme .table th[data-sortable="true"]:hover {
    background: var(--gray-700);
    color: var(--dark-text-color);
}

/* ===== SORT ICONS ===== */
.sort-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    display: inline-block;
}

.sort-icon i {
    font-size: 12px;
    color: var(--light-text-muted);
    transition: color 0.2s ease;
}

/* Show icon on hover */
th[data-sortable="true"]:hover .sort-icon {
    opacity: 1;
    visibility: visible;
}

th[data-sortable="true"]:hover .sort-icon i {
    color: var(--light-text-color);
}

/* Active sort states */
th.sorted-asc .sort-icon,
th.sorted-desc .sort-icon {
    opacity: 1 !important;
    visibility: visible !important;
}

th.sorted-asc .sort-icon i,
th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* Dark theme sort icons */
body.dark-theme .sort-icon i {
    color: var(--dark-text-muted);
}

body.dark-theme th[data-sortable="true"]:hover .sort-icon i {
    color: var(--dark-text-color);
}

body.dark-theme th.sorted-asc .sort-icon i,
body.dark-theme th.sorted-desc .sort-icon i {
    color: var(--primary-color) !important;
}

/* ===== TABLE ACTIONS ===== */
.table-actions {
    display: flex;
    gap: 4px;
    align-items: center;
}

.table-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
}

/* ===== TABLE BADGES ===== */
.badge {
    display: inline-block;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
    text-transform: none;
    letter-spacing: 0;
    border: none;
}

.table .badge {
    font-size: 11px;
    padding: 3px 6px;
}

.badge.bg-success {
    background: var(--success-light) !important;
    color: var(--success-dark) !important;
}

.badge.bg-warning {
    background: var(--warning-light) !important;
    color: var(--warning-dark) !important;
}

.badge.bg-danger {
    background: var(--danger-light) !important;
    color: var(--danger-dark) !important;
}

.badge.bg-info {
    background: var(--info-light) !important;
    color: var(--info-dark) !important;
}

.badge.bg-secondary {
    background: var(--gray-300) !important;
    color: var(--gray-700) !important;
}

/* Dark theme badges */
body.dark-theme .badge.bg-success {
    background: var(--success-color) !important;
    color: var(--light-color) !important;
}

body.dark-theme .badge.bg-warning {
    background: var(--warning-color) !important;
    color: var(--dark-color) !important;
}

body.dark-theme .badge.bg-danger {
    background: var(--danger-color) !important;
    color: var(--light-color) !important;
}

body.dark-theme .badge.bg-info {
    background: var(--info-color) !important;
    color: var(--light-color) !important;
}

body.dark-theme .badge.bg-secondary {
    background: var(--gray-600) !important;
    color: var(--light-color) !important;
}

/* ===== TABLE LINKS ===== */
.table a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

.table a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

body.dark-theme .table a {
    color: var(--primary-light);
}

body.dark-theme .table a:hover {
    color: var(--primary-color);
}

/* ===== TABLE TEXT ENHANCEMENTS ===== */
.table .text-end {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-weight: 500;
}

.table .text-muted {
    color: var(--light-text-muted) !important;
    font-size: 13px;
}

.text-sm-end {
    float: left !important;
}

/* ===== TABLE LOADING STATES ===== */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--light-card-bg);
    opacity: 0.8;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== RESPONSIVE TABLE ===== */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 12px;
    }

    .table th,
    .table td {
        padding: 8px 12px;
    }

    .table th[data-sortable="true"] {
        padding-right: 32px;
    }

    .sort-icon {
        right: 8px;
    }
}

/* ===== ENHANCED DATATABLE TOOLBAR ===== */
.datatable-toolbar {
    padding: var(--spacing-5) var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
 
    border-bottom: 1px solid var(--light-card-border);
    position: relative;
}

body.dark-theme .datatable-toolbar {
    background: linear-gradient(135deg, var(--dark-card-bg) 0%, var(--dark-bg-secondary) 100%);
    border-bottom-color: var(--dark-card-border);
}



.toolbar-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex: 1;
}

.toolbar-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.toolbar-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
}

.toolbar-label {
    font-size: 13px;
    font-weight: 500;
    color: var(--light-text-muted);
    margin: 0;
    white-space: nowrap;
}

body.dark-theme .toolbar-label {
    color: var(--dark-text-muted);
}

.toolbar-form {
    display: inline-flex;
    align-items: center;
}

/* Enhanced Filter Button */
.filter-btn {
    position: relative;
    background: var(--light-card-bg);
    border: 2px solid var(--light-card-border);
    color: var(--light-text-color);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    transition: all var(--transition-normal) var(--transition-bezier);
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    font-weight: 500;
    font-size: 13px;
}

.filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--light-hover-bg);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-sm);
}

.filter-btn.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

body.dark-theme .filter-btn {
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
    color: var(--dark-text-color);
}

body.dark-theme .filter-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: var(--dark-hover-bg);
}

/* Filter Badge */
.filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--danger-color);
    color: var(--light-color);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-sm);
    animation: pulse-badge 2s infinite;
}

[dir="rtl"] .filter-badge {
    right: auto;
    left: -8px;
}

@keyframes pulse-badge {
    0% {
        box-shadow: var(--box-shadow-sm);
    }
    50% {
        box-shadow: var(--box-shadow);
    }
    100% {
        box-shadow: var(--box-shadow-sm);
    }
}

/* Enhanced Form Select */
.datatable-select-sm {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 13px;
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius);
    background: var(--light-card-bg);
    color: var(--light-text-color);
    min-width: 80px;
    transition: all var(--transition-fast) ease;
    cursor: pointer;
}

.datatable-select-sm:focus {
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow-focus);
    outline: none;
}

body.dark-theme .datatable-select-sm {
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
    color: var(--dark-text-color);
}

body.dark-theme .datatable-select-sm:focus {
    border-color: var(--primary-color);
    box-shadow: var(--box-shadow-focus);
}

/* Responsive Toolbar */
@media (max-width: 768px) {
    .datatable-toolbar {
        flex-direction: column;
        align-items: stretch;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
    }

    .toolbar-left,
    .toolbar-right {
        justify-content: center;
        flex: none;
    }

    .toolbar-right {
        flex-wrap: wrap;
        justify-content: space-between;
    }

    .toolbar-item {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }

    .toolbar-form {
        width: 100%;
        justify-content: center;
    }

    .datatable-select-sm {
        width: 100%;
        max-width: 100px;
    }
}

/* ===== ENHANCED DATATABLE PAGINATION ===== */
.datatable-pagination {
    padding: var(--spacing-5) var(--spacing-6);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: var(--spacing-4);
    border-radius: 0 0 var(--border-radius-lg) var(--border-radius-lg);
}



/* Pagination Info */
.pagination-info {
    display: flex;
    align-items: center;
}

.info-text {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: 14px;
    color: var(--light-text-muted);
}

body.dark-theme .info-text {
    color: var(--dark-text-muted);
}

.info-label {
    font-weight: 400;
}

.info-numbers {
    font-weight: 600;
    color: var(--primary-color);
    background: var(--primary-light);
    opacity: 0.2;
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius-sm);
}

.info-total {
    font-weight: 600;
    color: var(--light-text-color);
}

body.dark-theme .info-total {
    color: var(--dark-text-color);
}

/* Pagination Controls */
.pagination-controls {
    display: flex;
    align-items: center;
}

.pagination-nav {
    display: flex;
}

.pagination {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
    margin: 0;
    padding: 0;
    list-style: none;
}

.page-item {
    display: flex;
}

.page-link {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: 14px;
    font-weight: 500;
    color: var(--light-text-muted);
    background: var(--light-card-bg);
    border: 1px solid var(--light-card-border);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all var(--transition-fast) ease;
    min-width: 40px;
    height: 40px;
    gap: var(--spacing-1);
    cursor: pointer;
}

.page-link:hover {
    color: var(--primary-color);
    background: var(--light-hover-bg);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-sm);
}

/* Active Page */
.page-item.active .page-link,
.page-link.current {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
    font-weight: 600;
    box-shadow: var(--box-shadow);
}

.page-item.active .page-link:hover,
.page-link.current:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow-lg);
}

/* Disabled Page */
.page-item.disabled .page-link,
.page-link.disabled {
    color: var(--gray-400);
    background: var(--light-card-bg);
    border-color: var(--light-card-border);
    cursor: not-allowed;
    opacity: 0.6;
    pointer-events: none;
}

/* Previous/Next Buttons */
.page-prev,
.page-next {
    padding: var(--spacing-2) var(--spacing-4);
    font-weight: 500;
    min-width: auto;
}

.page-prev i,
.page-next i {
    font-size: 16px;
}

/* Page Dots */
.page-dots .page-link {
    border: none;
    background: transparent;
    cursor: default;
    pointer-events: none;
    font-weight: 600;
    color: var(--light-text-muted);
}

body.dark-theme .page-dots .page-link {
    color: var(--dark-text-muted);
}

/* Dark Theme Pagination */
body.dark-theme .page-link {
    color: var(--dark-text-muted);
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

body.dark-theme .page-link:hover {
    color: var(--primary-color);
    background: var(--dark-hover-bg);
    border-color: var(--primary-color);
}

body.dark-theme .page-item.active .page-link,
body.dark-theme .page-link.current {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--light-color);
}

body.dark-theme .page-item.disabled .page-link,
body.dark-theme .page-link.disabled {
    color: var(--gray-600);
    background: var(--dark-card-bg);
    border-color: var(--dark-card-border);
}

/* Responsive Pagination */
@media (max-width: 768px) {
    .datatable-pagination {
        flex-direction: column;
        gap: var(--spacing-3);
        padding: var(--spacing-4);
        text-align: center;
    }

    .pagination-info {
        order: 2;
    }

    .pagination-controls {
        order: 1;
    }

    .info-text {
        font-size: 13px;
        justify-content: center;
    }

    .pagination {
        flex-wrap: wrap;
        justify-content: center;
        gap: var(--spacing-1);
    }

    .page-link {
        min-width: 36px;
        height: 36px;
        padding: var(--spacing-1) var(--spacing-2);
        font-size: 13px;
    }

    .page-prev,
    .page-next {
        padding: var(--spacing-1) var(--spacing-3);
    }

    .page-prev span,
    .page-next span {
        display: none;
    }

    .page-prev i,
    .page-next i {
        font-size: 14px;
    }
}

/* ===== RTL SUPPORT FOR TABLES ===== */
[dir="rtl"] .sort-icon {
    left: 12px;
    right: auto;
}

[dir="rtl"] .table th[data-sortable="true"] {
    padding-left: 40px;
    padding-right: 20px;
}

[dir="rtl"] .filter-badge {
    right: auto;
    left: -8px;
}

[dir="rtl"] .page-prev i {
    transform: scaleX(-1);
}

[dir="rtl"] .page-next i {
    transform: scaleX(-1);
}

@media (max-width: 768px) {
    [dir="rtl"] .sort-icon {
        left: 8px;
        right: auto;
    }

    [dir="rtl"] .table th[data-sortable="true"] {
        padding-left: 32px;
        padding-right: 12px;
    }
}

/* ===== DATATABLE BUTTONS ===== */
.datatable-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-5);
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: all var(--transition-normal) var(--transition-bezier);
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    user-select: none;
    background: transparent;
    box-shadow: var(--box-shadow-sm);
}

.datatable-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-md);
    text-decoration: none;
}

.datatable-btn:active {
    transform: translateY(0);
    box-shadow: var(--box-shadow-sm);
}

.datatable-btn i {
    font-size: 16px;
    transition: transform var(--transition-fast) ease;
}

.datatable-btn:hover i {
    transform: scale(1.1);
}

/* ===== PRIMARY BUTTON ===== */
.datatable-btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-color: var(--primary-color);
}

.datatable-btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: white;
    border-color: var(--primary-dark);
}

.datatable-btn-primary:focus {
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.25);
}

/* ===== SECONDARY BUTTON ===== */
.datatable-btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
    color: white;
    border-color: var(--secondary-color);
}

.datatable-btn-secondary:hover {
    background: linear-gradient(135deg, var(--secondary-dark) 0%, var(--secondary-color) 100%);
    color: white;
    border-color: var(--secondary-dark);
}

/* ===== SUCCESS BUTTON ===== */
.datatable-btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
    color: white;
    border-color: var(--success-color);
}

.datatable-btn-success:hover {
    background: linear-gradient(135deg, var(--success-dark) 0%, var(--success-color) 100%);
    color: white;
    border-color: var(--success-dark);
}

/* ===== WARNING BUTTON ===== */
.datatable-btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
    color: white;
    border-color: var(--warning-color);
}

.datatable-btn-warning:hover {
    background: linear-gradient(135deg, var(--warning-dark) 0%, var(--warning-color) 100%);
    color: white;
    border-color: var(--warning-dark);
}

/* ===== DANGER BUTTON ===== */
.datatable-btn-danger {
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    color: white;
    border-color: var(--danger-color);
}

.datatable-btn-danger:hover {
    background: linear-gradient(135deg, var(--danger-dark) 0%, var(--danger-color) 100%);
    color: white;
    border-color: var(--danger-dark);
}

/* ===== OUTLINE BUTTONS ===== */
.datatable-btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.datatable-btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.datatable-btn-outline-secondary {
    background: transparent;
    color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.datatable-btn-outline-secondary:hover {
    background: var(--secondary-color);
    color: white;
    border-color: var(--secondary-color);
}

.datatable-btn-outline-success {
    background: transparent;
    color: var(--success-color);
    border-color: var(--success-color);
}

.datatable-btn-outline-success:hover {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.datatable-btn-outline-warning {
    background: transparent;
    color: var(--warning-color);
    border-color: var(--warning-color);
}

.datatable-btn-outline-warning:hover {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.datatable-btn-outline-danger {
    background: transparent;
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.datatable-btn-outline-danger:hover {
    background: var(--danger-color);
    color: white;
    border-color: var(--danger-color);
}

/* ===== FILTER BUTTON SPECIAL STYLES ===== */
.datatable-filter-btn {
    position: relative;
}

.datatable-filter-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-sm);
    animation: pulse-badge 2s infinite;
    border: 2px solid var(--light-card-bg);
}

body.dark-theme .datatable-filter-badge {
    border-color: var(--dark-card-bg);
}

[dir="rtl"] .datatable-filter-badge {
    right: auto;
    left: -8px;
}

@keyframes pulse-badge {
    0% {
        box-shadow: var(--box-shadow-sm);
        transform: scale(1);
    }
    50% {
        box-shadow: var(--box-shadow-md);
        transform: scale(1.05);
    }
    100% {
        box-shadow: var(--box-shadow-sm);
        transform: scale(1);
    }
}

/* ===== BUTTON GROUPS ===== */
.toolbar-left .datatable-btn + .datatable-btn {
    margin-left: var(--spacing-3);
}

[dir="rtl"] .toolbar-left .datatable-btn + .datatable-btn {
    margin-left: 0;
    margin-right: var(--spacing-3);
}

/* ===== RESPONSIVE BUTTONS ===== */
@media (max-width: 768px) {
    .datatable-btn {
        padding: var(--spacing-2) var(--spacing-4);
        font-size: 13px;
    }

    .datatable-btn i {
        font-size: 14px;
    }

    .datatable-btn span {
        display: none;
    }

    .toolbar-left {
        gap: var(--spacing-2);
    }

    .toolbar-left .datatable-btn + .datatable-btn {
        margin-left: 0;
    }

    [dir="rtl"] .toolbar-left .datatable-btn + .datatable-btn {
        margin-right: 0;
    }
}
