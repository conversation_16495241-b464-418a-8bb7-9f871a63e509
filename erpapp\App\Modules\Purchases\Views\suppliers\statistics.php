<?php
/**
 * صفحة إحصائيات الموردين البسيطة
 * نقل بطاقات الإحصائيات من index.php
 */

// التحقق من الصلاحيات
if (!is_logged_in()) {
    redirect('login');
}

// التحقق من صلاحية الوصول للوحدة
if (!has_permission('purchases_view')) {
    show_error('ليس لديك صلاحية للوصول إلى هذه الصفحة');
}

// إعداد الإحصائيات (نفس الكود من index.php)
$stats_cards = [
    [
        'title' => 'إجمالي الموردين',
        'value' => $stats['total_suppliers'] ?? 0,
        'icon' => 'fas fa-truck',
        'color' => 'success'
    ],
    [
        'title' => 'الموردين النشطين',
        'value' => $stats['active_suppliers'] ?? 0,
        'icon' => 'fas fa-check-circle',
        'color' => 'success'
    ]
];

// إعداد breadcrumb
$safe_breadcrumb = [
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'url' => 'purchases/suppliers'],
    ['title' => 'الإحصائيات', 'active' => true]
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إحصائيات الموردين - <?= APP_NAME ?></title>
    
    <!-- تحميل CSS -->
    <?php load_css_files(); ?>
    <?php load_external_css_libraries(); ?>
</head>
<body>

<?php include BASE_PATH . '/App/Layouts/main.php'; ?>

<div class="container-fluid">
    
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <?php foreach ($safe_breadcrumb as $item): ?>
                <?php if (isset($item['active'])): ?>
                    <li class="breadcrumb-item active" aria-current="page"><?= $item['title'] ?></li>
                <?php else: ?>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url($item['url']) ?>"><?= $item['title'] ?></a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ol>
    </nav>

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">📊 إحصائيات الموردين</h1>
            <p class="text-muted">بطاقات الإحصائيات الأساسية</p>
        </div>
        <div>
            <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-outline-primary">
                <i class="fas fa-arrow-right"></i>
                العودة للموردين
            </a>
        </div>
    </div>

    <!-- بطاقات الإحصائيات (نفس التصميم من datatable_helper.php) -->
    <div class="row mb-4">
        <?php foreach ($stats_cards as $card): ?>
            <div class="col-xl-6 col-md-6 mb-3">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-<?= $card['color'] ?> text-white rounded-circle p-3">
                                    <i class="<?= $card['icon'] ?> fa-lg"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="card-title mb-1"><?= $card['title'] ?></h6>
                                <h2 class="text-<?= $card['color'] ?> mb-0"><?= number_format($card['value']) ?></h2>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <!-- معلومات إضافية -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <h5 class="card-title">📈 تفاصيل أكثر</h5>
                    <p class="text-muted">يمكنك العودة إلى صفحة الموردين لإدارة البيانات</p>
                    <a href="<?= base_url('purchases/suppliers') ?>" class="btn btn-primary">
                        <i class="fas fa-list"></i>
                        عرض قائمة الموردين
                    </a>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- تحميل JavaScript -->
<?php load_external_js_libraries(); ?>
<?php load_js_files(); ?>

</body>
</html>
