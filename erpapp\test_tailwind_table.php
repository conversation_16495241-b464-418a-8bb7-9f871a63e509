<?php
/**
 * اختبار تصميم Tailwind CSS للجداول
 */

// تضمين الملفات المطلوبة
require_once 'App/Helpers/datatable_helper.php';

// بيانات تجريبية
$columns = [
    [
        'field' => 'id',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '80px'
    ],
    [
        'field' => 'name',
        'title' => 'الاسم',
        'type' => 'link',
        'url' => 'users/{entity_number}/view',
        'subtitle_field' => 'email'
    ],
    [
        'field' => 'status',
        'title' => 'الحالة',
        'type' => 'badge',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'danger',
                'pending' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'pending' => 'في الانتظار'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'users/{entity_number}/edit',
                'class' => 'btn-outline-primary',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ],
            [
                'type' => 'button',
                'onclick' => 'confirmDelete({entity_number})',
                'class' => 'btn-outline-danger',
                'icon' => 'fas fa-trash',
                'title' => 'حذف'
            ]
        ]
    ]
];

$data = [
    [
        'id' => 1,
        'entity_number' => 'USR001',
        'name' => 'أحمد محمد',
        'email' => '<EMAIL>',
        'status' => 'active'
    ],
    [
        'id' => 2,
        'entity_number' => 'USR002',
        'name' => 'فاطمة علي',
        'email' => '<EMAIL>',
        'status' => 'pending'
    ],
    [
        'id' => 3,
        'entity_number' => 'USR003',
        'name' => 'محمد حسن',
        'email' => '<EMAIL>',
        'status' => 'inactive'
    ]
];

$empty_state = [
    'icon' => 'fas fa-users',
    'message' => 'لا يوجد مستخدمين',
    'action' => [
        'url' => 'users/create',
        'text' => 'إضافة مستخدم جديد'
    ]
];

$module = 'users';
$entity = 'user';

// دوال مساعدة مؤقتة
function base_url($path = '') {
    return 'http://localhost/erpapp/' . $path;
}

function htmlspecialchars_decode($string) {
    return htmlspecialchars($string);
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Tailwind CSS للجداول</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    fontFamily: {
                        'tajawal': ['Tajawal', 'sans-serif']
                    }
                }
            }
        }
    </script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        body { 
            font-family: 'Tajawal', sans-serif; 
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        
        .dark body {
            background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
        }
    </style>
</head>
<body class="p-8">

<div class="max-w-6xl mx-auto">
    
    <!-- Header -->
    <div class="mb-8 text-center">
        <h1 class="text-4xl font-bold text-gray-800 dark:text-white mb-4">
            🎨 اختبار تصميم Tailwind CSS للجداول
        </h1>
        <p class="text-gray-600 dark:text-gray-300 text-lg">
            تصميم عصري وجميل للجداول باستخدام Tailwind CSS
        </p>
        
        <!-- Dark Mode Toggle -->
        <button onclick="toggleDarkMode()" 
                class="mt-4 px-6 py-2 bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-800 rounded-lg hover:bg-gray-700 dark:hover:bg-gray-300 transition-colors duration-200">
            <i class="fas fa-moon dark:hidden mr-2"></i>
            <i class="fas fa-sun hidden dark:inline mr-2"></i>
            تبديل الثيم
        </button>
    </div>

    <!-- Table Container -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden border border-gray-200 dark:border-gray-700">
        
        <!-- Table Header -->
        <div class="px-6 py-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
            <h2 class="text-xl font-semibold text-gray-800 dark:text-white">
                📊 قائمة المستخدمين
            </h2>
            <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                إجمالي: <?= count($data) ?> مستخدم
            </p>
        </div>

        <!-- Table -->
        <div class="table-responsive">
            <?php render_datatable_table($columns, $data, $empty_state, $module, $entity); ?>
        </div>
        
    </div>

    <!-- Features -->
    <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        
        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-palette text-blue-600 dark:text-blue-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">تصميم عصري</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">
                    تصميم جميل ومتطور باستخدام Tailwind CSS
                </p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-moon text-green-600 dark:text-green-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">دعم الثيم الداكن</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">
                    انتقال سلس بين الثيم الفاتح والداكن
                </p>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-mobile-alt text-purple-600 dark:text-purple-400 text-xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">تصميم متجاوب</h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm">
                    يعمل بشكل مثالي على جميع الأجهزة
                </p>
            </div>
        </div>
        
    </div>

</div>

<script>
function toggleDarkMode() {
    document.documentElement.classList.toggle('dark');
    localStorage.setItem('darkMode', document.documentElement.classList.contains('dark'));
}

// Load saved theme
if (localStorage.getItem('darkMode') === 'true') {
    document.documentElement.classList.add('dark');
}

// Simulate functions
function confirmDelete(id) {
    alert('حذف المستخدم رقم: ' + id);
}
</script>

</body>
</html>
