<?php
/**
 * اختبار تصميم الـ Breadcrumb
 * عرض أمثلة مختلفة على التصميم الجديد
 */

require_once 'loader.php';
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تصميم Breadcrumb</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="public/css/core/variables.css">
    <link rel="stylesheet" href="public/css/core/base.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            padding: 2rem;
            background: var(--light-bg-color);
        }
        
        .demo-section {
            margin-bottom: 3rem;
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        .demo-description {
            color: var(--gray-600);
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            z-index: 1000;
        }
    </style>
</head>
<body class="rtl">

<button class="theme-toggle" onclick="toggleTheme()">
    <i class="fas fa-moon"></i> تبديل الثيم
</button>

<div class="container-fluid">
    <h1 style="text-align: center; margin-bottom: 3rem; color: var(--primary-color);">
        🧭 اختبار تصميم Breadcrumb
    </h1>

    <!-- مثال 1: Breadcrumb بسيط -->
    <div class="demo-section">
        <h2 class="demo-title">1. Breadcrumb بسيط</h2>
        <p class="demo-description">مثال على breadcrumb بسيط مع رابطين</p>
        
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-home"></i> الرئيسية</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <i class="fas fa-cog"></i> الإعدادات
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">الإعدادات العامة</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- مثال 2: Breadcrumb للموردين -->
    <div class="demo-section">
        <h2 class="demo-title">2. Breadcrumb للموردين</h2>
        <p class="demo-description">مثال من نظام الموردين كما هو مستخدم في المشروع</p>
        
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-home"></i> الرئيسية</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-shopping-cart"></i> المشتريات</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <i class="fas fa-truck"></i> الموردين
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">إدارة الموردين</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- مثال 3: Breadcrumb طويل -->
    <div class="demo-section">
        <h2 class="demo-title">3. Breadcrumb طويل</h2>
        <p class="demo-description">مثال على breadcrumb مع عدة مستويات</p>
        
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-home"></i> الرئيسية</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-boxes"></i> المخزون</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-cube"></i> المنتجات</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-tags"></i> الفئات</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <i class="fas fa-edit"></i> تعديل الفئة
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">تعديل فئة المنتجات</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- مثال 4: Breadcrumb مع أيقونات مختلفة -->
    <div class="demo-section">
        <h2 class="demo-title">4. Breadcrumb مع أيقونات متنوعة</h2>
        <p class="demo-description">مثال يوضح استخدام أيقونات مختلفة</p>
        
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-users"></i> إدارة المستخدمين</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#"><i class="fas fa-user-shield"></i> الصلاحيات</a>
                            </li>
                            <li class="breadcrumb-item active">
                                <i class="fas fa-plus-circle"></i> إضافة صلاحية جديدة
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">إضافة صلاحية جديدة</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- مثال 5: Breadcrumb بدون أيقونات -->
    <div class="demo-section">
        <h2 class="demo-title">5. Breadcrumb بدون أيقونات</h2>
        <p class="demo-description">تصميم نظيف بدون أيقونات</p>
        
        <div class="row">
            <div class="col-12">
                <div class="page-title-box">
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item">
                                <a href="#">الرئيسية</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#">التقارير</a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="#">تقارير المبيعات</a>
                            </li>
                            <li class="breadcrumb-item active">
                                التقرير الشهري
                            </li>
                        </ol>
                    </div>
                    <h4 class="page-title">تقرير المبيعات الشهري</h4>
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات التصميم -->
    <div class="demo-section">
        <div class="alert alert-info">
            <h4><i class="fas fa-info-circle"></i> مميزات التصميم:</h4>
            <ul>
                <li><strong>تصميم متجاوب:</strong> يتكيف مع جميع أحجام الشاشات</li>
                <li><strong>دعم RTL:</strong> يعمل مع اللغة العربية والإنجليزية</li>
                <li><strong>تأثيرات تفاعلية:</strong> تأثيرات hover وانتقالات سلسة</li>
                <li><strong>دعم الثيم الداكن:</strong> يتكيف مع الوضع الليلي</li>
                <li><strong>أيقونات اختيارية:</strong> يمكن استخدامه مع أو بدون أيقونات</li>
                <li><strong>فواصل ذكية:</strong> أسهم تتجه حسب اتجاه اللغة</li>
            </ul>
        </div>
    </div>
</div>

<script>
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    const icon = document.querySelector('.theme-toggle i');
    if (document.body.classList.contains('dark-theme')) {
        icon.className = 'fas fa-sun';
    } else {
        icon.className = 'fas fa-moon';
    }
}

// إضافة تأثيرات إضافية
document.addEventListener('DOMContentLoaded', function() {
    // تأثير عند النقر على روابط breadcrumb
    document.querySelectorAll('.breadcrumb-item a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // تأثير بصري
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            console.log('تم النقر على:', this.textContent.trim());
        });
    });
});
</script>

</body>
</html>
