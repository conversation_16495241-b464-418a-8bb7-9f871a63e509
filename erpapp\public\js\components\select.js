/**
 * مكون Select المبسط
 * يستخدم المكتبات المحملة مسبق<|im_start|> من js_helper.php
 * لا يحمل مكتبات - فقط يهيئها
 */

(function() {
    'use strict';

    // إعدادات افتراضية
    const DEFAULT_CONFIG = {
        rtl: true,
        searchable: true,
        clearable: false,
        placeholder: 'اختر...',
        noResultsText: 'لا توجد نتائج',
        loadingText: 'جاري التحميل...',
        searchPlaceholder: 'ابحث...',
        allowCreate: false,
        ajax: null
    };

    // كلاس Select المبسط
    class SimpleSelect {
        constructor(element, options = {}) {
            this.element = element;
            this.config = { ...DEFAULT_CONFIG, ...options };
            this.instance = null;

            this.init();
        }

        init() {
            // التحقق من توفر المكتبات
            if (this.isSelect2Available()) {
                this.initSelect2();
            } else if (this.isChoicesAvailable()) {
                this.initChoices();
            } else {
                console.warn('⚠️ لا توجد مكتبة Select متاحة، استخدام Select العادي');
                this.fallbackToNative();
            }
        }

        isSelect2Available() {
            return typeof window.jQuery !== 'undefined' &&
                   typeof window.jQuery.fn.select2 !== 'undefined';
        }

        isChoicesAvailable() {
            return typeof window.Choices !== 'undefined';
        }

        initSelect2() {
            const config = {
                theme: 'default',
                dir: this.config.rtl ? 'rtl' : 'ltr',
                placeholder: this.config.placeholder,
                allowClear: this.config.clearable,
                language: {
                    noResults: () => this.config.noResultsText,
                    searching: () => this.config.loadingText,
                    inputTooShort: () => 'أدخل حرف واحد على الأقل',
                    loadingMore: () => 'جاري تحميل المزيد...'
                },
                width: '100%'
            };

            if (this.config.ajax) {
                config.ajax = this.config.ajax;
            }

            if (!this.config.searchable) {
                config.minimumResultsForSearch = Infinity;
            }

            this.instance = window.jQuery(this.element).select2(config);
            console.log('✅ تم تهيئة Select2');
        }

        initChoices() {
            const config = {
                searchEnabled: this.config.searchable,
                removeItemButton: this.config.clearable,
                placeholder: true,
                placeholderValue: this.config.placeholder,
                noResultsText: this.config.noResultsText,
                loadingText: this.config.loadingText,
                searchPlaceholderValue: this.config.searchPlaceholder,
                itemSelectText: '',
                addItemText: (value) => `إضافة "${value}"`,
                maxItemText: (maxItemCount) => `يمكن اختيار ${maxItemCount} عناصر فقط`,
                uniqueItemText: 'يمكن إضافة هذا العنصر مرة واحدة فقط',
                shouldSort: false
            };

            if (this.config.allowCreate) {
                config.addItems = true;
                config.addItemFilter = null;
            }

            this.instance = new window.Choices(this.element, config);
            console.log('✅ تم تهيئة Choices.js');
        }

        // API موحد
        getValue() {
            if (this.instance && this.isSelect2Available()) {
                return window.jQuery(this.element).val();
            } else if (this.instance && this.isChoicesAvailable()) {
                return this.instance.getValue();
            } else {
                return this.element.value;
            }
        }

        setValue(value) {
            if (this.instance && this.isSelect2Available()) {
                window.jQuery(this.element).val(value).trigger('change');
            } else if (this.instance && this.isChoicesAvailable()) {
                this.instance.setChoiceByValue(value);
            } else {
                this.element.value = value;
            }
        }

        clear() {
            if (this.instance && this.isSelect2Available()) {
                window.jQuery(this.element).val(null).trigger('change');
            } else if (this.instance && this.isChoicesAvailable()) {
                this.instance.clearStore();
            } else {
                this.element.value = '';
            }
        }

        destroy() {
            if (this.instance) {
                if (this.isSelect2Available()) {
                    window.jQuery(this.element).select2('destroy');
                } else if (this.isChoicesAvailable()) {
                    this.instance.destroy();
                }
            }
        }

        fallbackToNative() {
            console.warn('⚠️ تم الرجوع إلى Select العادي');
            this.element.classList.add('form-select');
        }
    }

    // دالة تهيئة عامة
    window.initSimpleSelect = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const instances = [];

        elements.forEach(element => {
            const instance = new SimpleSelect(element, options);
            instances.push(instance);
        });

        return instances.length === 1 ? instances[0] : instances;
    };

    // تهيئة تلقائية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة جميع select مع data-select
        document.querySelectorAll('select[data-select]').forEach(element => {
            const config = element.dataset.selectConfig ?
                JSON.parse(element.dataset.selectConfig) : {};

            new SimpleSelect(element, config);
        });

        console.log('🚀 تم تهيئة جميع Select البسيطة');
    });

})();
