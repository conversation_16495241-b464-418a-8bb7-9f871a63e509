/**
 * مكون Select المتقدم - نظام موحد لجميع أنواع Select
 * يدعم: Select2, Choices.js, Tom Select
 */

(function() {
    'use strict';

    // إعدادات افتراضية
    const DEFAULT_CONFIG = {
        library: 'select2', // select2, choices, tomselect
        theme: 'default',
        rtl: true,
        searchable: true,
        clearable: true,
        placeholder: 'اختر...',
        noResultsText: 'لا توجد نتائج',
        loadingText: 'جاري التحميل...',
        searchPlaceholder: 'ابحث...',
        allowCreate: false,
        maxItems: null,
        ajax: null
    };

    // كلاس Select المتقدم
    class AdvancedSelect {
        constructor(element, options = {}) {
            this.element = element;
            this.config = { ...DEFAULT_CONFIG, ...options };
            this.instance = null;
            this.library = this.config.library;
            
            this.init();
        }

        async init() {
            try {
                // تحميل المكتبة المطلوبة
                await this.loadLibrary();
                
                // تهيئة Select
                this.initializeSelect();
                
                // إضافة event listeners
                this.attachEvents();
                
                console.log(`✅ تم تهيئة ${this.library} بنجاح`);
            } catch (error) {
                console.error('❌ خطأ في تهيئة Select:', error);
                this.fallbackToNative();
            }
        }

        async loadLibrary() {
            switch (this.library) {
                case 'select2':
                    await this.loadSelect2();
                    break;
                case 'choices':
                    await this.loadChoices();
                    break;
                case 'tomselect':
                    await this.loadTomSelect();
                    break;
                default:
                    throw new Error(`مكتبة غير مدعومة: ${this.library}`);
            }
        }

        async loadSelect2() {
            // تحميل CSS
            if (!document.querySelector('link[href*="select2"]')) {
                await this.loadCSS('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
            }
            
            // تحميل JS
            if (typeof window.jQuery === 'undefined') {
                await this.loadScript('https://code.jquery.com/jquery-3.6.0.min.js');
            }
            
            if (typeof window.jQuery.fn.select2 === 'undefined') {
                await this.loadScript('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js');
            }
        }

        async loadChoices() {
            // تحميل CSS
            if (!document.querySelector('link[href*="choices"]')) {
                await this.loadCSS('https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/styles/choices.min.css');
            }
            
            // تحميل JS
            if (typeof window.Choices === 'undefined') {
                await this.loadScript('https://cdn.jsdelivr.net/npm/choices.js@10.2.0/public/assets/scripts/choices.min.js');
            }
        }

        async loadTomSelect() {
            // تحميل CSS
            if (!document.querySelector('link[href*="tom-select"]')) {
                await this.loadCSS('https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/css/tom-select.css');
            }
            
            // تحميل JS
            if (typeof window.TomSelect === 'undefined') {
                await this.loadScript('https://cdn.jsdelivr.net/npm/tom-select@2.2.2/dist/js/tom-select.complete.min.js');
            }
        }

        loadCSS(url) {
            return new Promise((resolve, reject) => {
                const link = document.createElement('link');
                link.rel = 'stylesheet';
                link.href = url;
                link.onload = resolve;
                link.onerror = reject;
                document.head.appendChild(link);
            });
        }

        loadScript(url) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = url;
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }

        initializeSelect() {
            switch (this.library) {
                case 'select2':
                    this.initSelect2();
                    break;
                case 'choices':
                    this.initChoices();
                    break;
                case 'tomselect':
                    this.initTomSelect();
                    break;
            }
        }

        initSelect2() {
            const config = {
                theme: 'default',
                dir: this.config.rtl ? 'rtl' : 'ltr',
                placeholder: this.config.placeholder,
                allowClear: this.config.clearable,
                language: {
                    noResults: () => this.config.noResultsText,
                    searching: () => this.config.loadingText,
                    inputTooShort: () => 'أدخل حرف واحد على الأقل',
                    loadingMore: () => 'جاري تحميل المزيد...'
                },
                width: '100%'
            };

            if (this.config.ajax) {
                config.ajax = this.config.ajax;
            }

            if (!this.config.searchable) {
                config.minimumResultsForSearch = Infinity;
            }

            this.instance = window.jQuery(this.element).select2(config);
        }

        initChoices() {
            const config = {
                searchEnabled: this.config.searchable,
                removeItemButton: this.config.clearable,
                placeholder: true,
                placeholderValue: this.config.placeholder,
                noResultsText: this.config.noResultsText,
                loadingText: this.config.loadingText,
                searchPlaceholderValue: this.config.searchPlaceholder,
                itemSelectText: '',
                addItemText: (value) => `إضافة "${value}"`,
                maxItemText: (maxItemCount) => `يمكن اختيار ${maxItemCount} عناصر فقط`,
                uniqueItemText: 'يمكن إضافة هذا العنصر مرة واحدة فقط',
                shouldSort: false
            };

            if (this.config.allowCreate) {
                config.addItems = true;
                config.addItemFilter = null;
            }

            if (this.config.maxItems) {
                config.maxItemCount = this.config.maxItems;
            }

            this.instance = new window.Choices(this.element, config);
        }

        initTomSelect() {
            const config = {
                plugins: ['remove_button', 'clear_button'],
                placeholder: this.config.placeholder,
                searchField: ['text', 'value'],
                render: {
                    no_results: () => `<div class="no-results">${this.config.noResultsText}</div>`,
                    loading: () => `<div class="loading">${this.config.loadingText}</div>`
                }
            };

            if (this.config.allowCreate) {
                config.create = true;
                config.createOnBlur = true;
            }

            if (this.config.maxItems) {
                config.maxItems = this.config.maxItems;
            }

            if (this.config.ajax) {
                config.load = this.config.ajax;
            }

            this.instance = new window.TomSelect(this.element, config);
        }

        attachEvents() {
            // إضافة events مخصصة
            this.element.addEventListener('select:change', (e) => {
                console.log('Select changed:', e.detail);
            });

            this.element.addEventListener('select:open', (e) => {
                console.log('Select opened');
            });

            this.element.addEventListener('select:close', (e) => {
                console.log('Select closed');
            });
        }

        // API موحد
        getValue() {
            switch (this.library) {
                case 'select2':
                    return window.jQuery(this.element).val();
                case 'choices':
                    return this.instance.getValue();
                case 'tomselect':
                    return this.instance.getValue();
            }
        }

        setValue(value) {
            switch (this.library) {
                case 'select2':
                    window.jQuery(this.element).val(value).trigger('change');
                    break;
                case 'choices':
                    this.instance.setChoiceByValue(value);
                    break;
                case 'tomselect':
                    this.instance.setValue(value);
                    break;
            }
        }

        addOption(option) {
            switch (this.library) {
                case 'select2':
                    const newOption = new Option(option.text, option.value, false, false);
                    window.jQuery(this.element).append(newOption);
                    break;
                case 'choices':
                    this.instance.setChoices([option], 'value', 'label', false);
                    break;
                case 'tomselect':
                    this.instance.addOption(option);
                    break;
            }
        }

        clear() {
            switch (this.library) {
                case 'select2':
                    window.jQuery(this.element).val(null).trigger('change');
                    break;
                case 'choices':
                    this.instance.clearStore();
                    break;
                case 'tomselect':
                    this.instance.clear();
                    break;
            }
        }

        destroy() {
            if (this.instance) {
                switch (this.library) {
                    case 'select2':
                        window.jQuery(this.element).select2('destroy');
                        break;
                    case 'choices':
                        this.instance.destroy();
                        break;
                    case 'tomselect':
                        this.instance.destroy();
                        break;
                }
            }
        }

        fallbackToNative() {
            console.warn('⚠️ تم الرجوع إلى Select العادي');
            this.element.classList.add('form-select');
        }
    }

    // دالة تهيئة عامة
    window.initAdvancedSelect = function(selector, options = {}) {
        const elements = document.querySelectorAll(selector);
        const instances = [];

        elements.forEach(element => {
            const instance = new AdvancedSelect(element, options);
            instances.push(instance);
        });

        return instances.length === 1 ? instances[0] : instances;
    };

    // تهيئة تلقائية عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        // تهيئة جميع select مع data-select
        document.querySelectorAll('select[data-select]').forEach(element => {
            const config = element.dataset.selectConfig ? 
                JSON.parse(element.dataset.selectConfig) : {};
            
            new AdvancedSelect(element, config);
        });

        console.log('🚀 تم تهيئة جميع Select المتقدمة');
    });

})();
