<?php
/**
 * اختبار الأزرار في منطقة العنوان
 * التصميم الجديد بدون تداخل
 */

require_once 'loader.php';

echo "<h1>🔧 اختبار الأزرار في منطقة العنوان</h1>";

// إعداد بيانات الموردين للاختبار
$suppliers = [
    [
        'entity_number' => 1,
        'G_name_ar' => 'شركة الأمل للتجارة',
        'G_name_en' => 'Al-Amal Trading Company',
        'S_company_name' => 'شركة الأمل للتجارة المحدودة',
        'group_name' => 'موردين رئيسيين',
        'G_phone' => '0501234567',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 2,
        'G_name_ar' => 'مؤسسة النور التجارية',
        'G_name_en' => 'Al-Noor Commercial Est.',
        'S_company_name' => 'مؤسسة النور التجارية',
        'group_name' => 'موردين محليين',
        'G_phone' => '0507654321',
        'G_status' => 'active'
    ],
    [
        'entity_number' => 3,
        'G_name_ar' => 'شركة الفجر للمواد',
        'G_name_en' => 'Al-Fajr Materials Co.',
        'S_company_name' => 'شركة الفجر للمواد الإنشائية',
        'group_name' => 'موردين مواد',
        'G_phone' => '0551234567',
        'G_status' => 'suspended'
    ]
];

// إعداد الأعمدة
$columns = [
    [
        'field' => 'entity_number',
        'title' => 'الرقم',
        'type' => 'text',
        'width' => '120px',
        'sortable' => true,
        'data_type' => 'number'
    ],
    [
        'field' => 'G_name_ar',
        'title' => 'اسم المورد',
        'type' => 'link',
        'url' => 'purchases/suppliers/{entity_number}',
        'subtitle_field' => 'G_name_en',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'S_company_name',
        'title' => 'اسم الشركة',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'group_name',
        'title' => 'المجموعة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_phone',
        'title' => 'الهاتف',
        'type' => 'text',
        'sortable' => true,
        'data_type' => 'text'
    ],
    [
        'field' => 'G_status',
        'title' => 'الحالة',
        'type' => 'badge',
        'sortable' => true,
        'data_type' => 'text',
        'status_config' => [
            'classes' => [
                'active' => 'success',
                'inactive' => 'secondary',
                'suspended' => 'warning'
            ],
            'texts' => [
                'active' => 'نشط',
                'inactive' => 'غير نشط',
                'suspended' => 'معلق'
            ]
        ]
    ],
    [
        'field' => 'actions',
        'title' => 'الإجراءات',
        'type' => 'actions',
        'width' => '125px',
        'buttons' => [
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}',
                'class' => 'btn-primary',
                'icon' => 'fas fa-eye',
                'title' => 'عرض'
            ],
            [
                'type' => 'link',
                'url' => 'purchases/suppliers/{entity_number}/edit',
                'class' => 'btn-success',
                'icon' => 'fas fa-edit',
                'title' => 'تعديل'
            ]
        ]
    ]
];

// إعداد الإجراءات (الأزرار)
$actions = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'fas fa-plus-circle',
        'text' => 'إضافة مورد جديد'
    ],
    [
        'type' => 'warning',
        'url' => 'purchases/suppliers/mixed',
        'icon' => 'fas fa-layer-group',
        'text' => 'العرض المختلط'
    ],
    [
        'type' => 'success',
        'url' => 'purchases/suppliers/dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'text' => 'لوحة التحكم'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/suppliers/stats',
        'icon' => 'fas fa-chart-bar',
        'text' => 'عرض الإحصائيات'
    ]
];

// إعداد الفلاتر
$filters_config = [
    [
        'name' => 'search',
        'type' => 'search',
        'label' => 'البحث في الموردين',
        'placeholder' => 'ابحث بالاسم أو اسم الشركة...',
        'icon' => 'fas fa-search',
        'col_size' => 6
    ],
    [
        'name' => 'status',
        'type' => 'select',
        'label' => 'حالة المورد',
        'placeholder' => 'جميع الحالات',
        'icon' => 'fas fa-check-circle',
        'col_size' => 6,
        'options' => [
            'active' => 'نشط',
            'inactive' => 'غير نشط',
            'suspended' => 'معلق'
        ]
    ]
];

// إعداد pagination
$pagination = [
    'current_page' => 1,
    'total_pages' => 1,
    'total_items' => count($suppliers),
    'per_page' => 10,
    'start_item' => 1,
    'end_item' => count($suppliers),
    'has_previous' => false,
    'has_next' => false,
    'previous_page' => 0,
    'next_page' => 0
];

// إعداد breadcrumb
$breadcrumb = [
    ['title' => 'الرئيسية', 'url' => 'dashboard'],
    ['title' => 'المشتريات', 'url' => 'purchases'],
    ['title' => 'الموردين', 'active' => true]
];

// إعداد Empty State
$empty_state = [
    'icon' => 'fas fa-truck-loading',
    'message' => 'لا توجد موردين',
    'action' => [
        'url' => 'purchases/suppliers/create',
        'text' => 'إضافة مورد جديد'
    ]
];

// استخدام النظام المباشر للجداول
render_datatable_page([
    'title' => 'الموردين - اختبار الأزرار في العنوان',
    'module' => 'purchases',
    'entity' => 'suppliers',
    'data' => $suppliers,
    'columns' => $columns,
    'pagination' => $pagination,
    'filters' => [],
    'breadcrumb' => $breadcrumb,
    'actions' => $actions,
    'filters_config' => $filters_config,
    'empty_state' => $empty_state
]);

echo "<hr>";
echo "<div class='alert alert-success'>";
echo "<h4>✅ تم إصلاح ترتيب العناصر حسب اتجاه اللغة!</h4>";
echo "<ul>";
echo "<li><strong>الـ Breadcrumb:</strong> في الأعلى منفصل</li>";
echo "<li><strong>العربية (RTL):</strong> الأزرار على اليسار، العنوان على اليمين</li>";
echo "<li><strong>الإنجليزية (LTR):</strong> العنوان على اليسار، الأزرار على اليمين</li>";
echo "<li><strong>شريط الأدوات:</strong> يحتوي على الفلاتر فقط</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-info'>";
echo "<h4>🎨 مميزات التصميم الجديد:</h4>";
echo "<ul>";
echo "<li><strong>ترتيب صحيح:</strong> يتبع اتجاه اللغة المحددة</li>";
echo "<li><strong>تخطيط واضح:</strong> لا يوجد تداخل بين العناصر</li>";
echo "<li><strong>تصميم متجاوب:</strong> الأزرار تنتقل تحت العنوان في الشاشات الصغيرة</li>";
echo "<li><strong>فصل المهام:</strong> الأزرار في العنوان، الفلاتر في شريط الأدوات</li>";
echo "<li><strong>سهولة الاستخدام:</strong> الأزرار المهمة في مكان بارز</li>";
echo "</ul>";
echo "</div>";

echo "<div class='alert alert-warning'>";
echo "<h4>🔄 اختبار اتجاه اللغة:</h4>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>العربية (RTL) - الحالي:</h5>";
echo "<div class='d-flex justify-content-between border p-2 mb-2'>";
echo "<div class='text-primary'>🔵 🟢 🟡 🔴 (أزرار)</div>";
echo "<div class='text-success'>العنوان</div>";
echo "</div>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>الإنجليزية (LTR):</h5>";
echo "<div class='d-flex justify-content-between border p-2 mb-2'>";
echo "<div class='text-success'>Title</div>";
echo "<div class='text-primary'>🔵 🟢 🟡 🔴 (Buttons)</div>";
echo "</div>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<hr>";
echo "<h2>🔄 اختبار اللغة الإنجليزية (LTR)</h2>";

// محاكاة تغيير اللغة للإنجليزية
echo "<div class='ltr-test' dir='ltr'>";
echo "<style>";
echo ".ltr-test { direction: ltr; text-align: left; }";
echo ".ltr-test .page-title-header { flex-direction: row; }";
echo ".ltr-test .page-title { order: 1; }";
echo ".ltr-test .page-title-actions { order: 2; }";
echo "</style>";

// إعداد بيانات إنجليزية
$breadcrumb_en = [
    ['title' => 'Home', 'url' => 'dashboard'],
    ['title' => 'Purchases', 'url' => 'purchases'],
    ['title' => 'Suppliers', 'active' => true]
];

$actions_en = [
    [
        'type' => 'primary',
        'url' => 'purchases/suppliers/create',
        'icon' => 'fas fa-plus-circle',
        'text' => 'Add New Supplier'
    ],
    [
        'type' => 'success',
        'url' => 'purchases/suppliers/dashboard',
        'icon' => 'fas fa-tachometer-alt',
        'text' => 'Dashboard'
    ],
    [
        'type' => 'info',
        'url' => 'purchases/suppliers/stats',
        'icon' => 'fas fa-chart-bar',
        'text' => 'Statistics'
    ]
];

// عرض مثال للتخطيط الإنجليزي
echo "<div class='page-title-box'>";
echo "<div class='page-title-right'>";
echo "<ol class='breadcrumb m-0'>";
foreach ($breadcrumb_en as $item) {
    if (isset($item['url']) && !empty($item['url']) && !isset($item['active'])) {
        echo "<li class='breadcrumb-item'><a href='#'>{$item['title']}</a></li>";
    } else {
        echo "<li class='breadcrumb-item active'>{$item['title']}</li>";
    }
}
echo "</ol>";
echo "</div>";

echo "<div class='page-title-header'>";
echo "<h4 class='page-title'>Suppliers Management</h4>";
echo "<div class='page-title-actions'>";
foreach ($actions_en as $action) {
    echo "<a href='#' class='btn btn-{$action['type']} btn-sm'>";
    echo "<i class='{$action['icon']}'></i>";
    echo "<span>{$action['text']}</span>";
    echo "</a>";
}
echo "</div>";
echo "</div>";
echo "</div>";

echo "</div>";

echo "<div class='alert alert-primary mt-3'>";
echo "<h4>📋 ملخص الترتيب:</h4>";
echo "<div class='row'>";
echo "<div class='col-md-6'>";
echo "<h5>🇸🇦 العربية (RTL):</h5>";
echo "<ol>";
echo "<li>Breadcrumb في الأعلى</li>";
echo "<li>الأزرار على اليسار</li>";
echo "<li>العنوان على اليمين</li>";
echo "</ol>";
echo "</div>";
echo "<div class='col-md-6'>";
echo "<h5>🇺🇸 الإنجليزية (LTR):</h5>";
echo "<ol>";
echo "<li>Breadcrumb at the top</li>";
echo "<li>Title on the left</li>";
echo "<li>Buttons on the right</li>";
echo "</ol>";
echo "</div>";
echo "</div>";
echo "</div>";
?>
